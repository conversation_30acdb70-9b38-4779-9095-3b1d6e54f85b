# 第八篇：通讯互动与评价反馈系统

## 概述

在前面的教程中，我们已经构建了完整的订单管理和支付结算系统。本篇教程将实现平台的通讯互动功能和评价反馈体系，这是提升用户体验和服务质量的关键组件。

我们将实现：
- 平台内即时通讯系统
- 消息推送与通知机制
- 多维度评价反馈体系
- 服务商信誉积分系统

## 技术栈

- **后端框架**: NestJS + TypeORM
- **实时通讯**: Socket.IO + WebSocket
- **消息队列**: Redis + Bull Queue
- **通知服务**: 短信(阿里云SMS) + 邮件(Nodemailer)
- **数据库**: MySQL 8.0

## 项目结构

```
src/
├── modules/
│   ├── chat/                 # 即时通讯模块
│   │   ├── entities/
│   │   ├── dto/
│   │   ├── services/
│   │   └── gateways/
│   ├── notification/         # 通知模块
│   │   ├── entities/
│   │   ├── dto/
│   │   ├── services/
│   │   └── providers/
│   └── review/              # 评价反馈模块
│       ├── entities/
│       ├── dto/
│       └── services/
└── common/
    ├── decorators/
    ├── guards/
    └── interfaces/
```

---

## 8.1 平台内即时通讯系统

### 8.1.1 IM系统架构设计

即时通讯系统是现代O2O平台的核心功能，让用户和服务商能够实时沟通，提升服务效率和用户满意度。

#### 系统架构特点

1. **基于WebSocket的实时通讯**
   - 支持文本、图片、语音消息
   - 消息实时推送与状态同步
   - 连接状态管理与断线重连

2. **消息存储与历史记录**
   - 消息持久化存储
   - 聊天记录分页查询
   - 消息状态跟踪(已发送/已读)

3. **多端同步支持**
   - 支持Web端、移动端同时在线
   - 消息多端实时同步
   - 离线消息推送

### 8.1.2 数据库设计

首先创建聊天相关的数据表结构：

#### 聊天会话实体 (ChatSession)

```typescript
// src/modules/chat/entities/chat-session.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../user/entities/user.entity';
import { Order } from '../../order/entities/order.entity';
import { ChatMessage } from './chat-message.entity';

export enum ChatSessionType {
  ORDER_CHAT = 'order_chat',      // 订单相关聊天
  CUSTOMER_SERVICE = 'customer_service', // 客服聊天
  SYSTEM_NOTICE = 'system_notice', // 系统通知
}

export enum ChatSessionStatus {
  ACTIVE = 'active',
  CLOSED = 'closed',
  ARCHIVED = 'archived',
}

@Entity('chat_sessions')
@Index(['userId', 'serviceProviderId'])
@Index(['orderId'])
export class ChatSession {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: ChatSessionType,
    default: ChatSessionType.ORDER_CHAT,
  })
  type: ChatSessionType;

  @Column({
    type: 'enum',
    enum: ChatSessionStatus,
    default: ChatSessionStatus.ACTIVE,
  })
  status: ChatSessionStatus;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'service_provider_id', nullable: true })
  serviceProviderId: string;

  @Column({ name: 'order_id', nullable: true })
  orderId: string;

  @Column({ name: 'last_message', type: 'text', nullable: true })
  lastMessage: string;

  @Column({ name: 'last_message_time', nullable: true })
  lastMessageTime: Date;

  @Column({ name: 'unread_count_user', default: 0 })
  unreadCountUser: number;

  @Column({ name: 'unread_count_provider', default: 0 })
  unreadCountProvider: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'service_provider_id' })
  serviceProvider: User;

  @ManyToOne(() => Order)
  @JoinColumn({ name: 'order_id' })
  order: Order;

  @OneToMany(() => ChatMessage, (message) => message.session)
  messages: ChatMessage[];
}
```

#### 聊天消息实体 (ChatMessage)

```typescript
// src/modules/chat/entities/chat-message.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../user/entities/user.entity';
import { ChatSession } from './chat-session.entity';

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  VOICE = 'voice',
  FILE = 'file',
  SYSTEM = 'system',
}

export enum MessageStatus {
  SENDING = 'sending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
}

@Entity('chat_messages')
@Index(['sessionId', 'createdAt'])
@Index(['senderId'])
export class ChatMessage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'session_id' })
  sessionId: string;

  @Column({ name: 'sender_id' })
  senderId: string;

  @Column({
    type: 'enum',
    enum: MessageType,
    default: MessageType.TEXT,
  })
  type: MessageType;

  @Column({ type: 'text' })
  content: string;

  @Column({ name: 'media_url', nullable: true })
  mediaUrl: string;

  @Column({ name: 'media_size', nullable: true })
  mediaSize: number;

  @Column({ name: 'media_duration', nullable: true })
  mediaDuration: number;

  @Column({
    type: 'enum',
    enum: MessageStatus,
    default: MessageStatus.SENDING,
  })
  status: MessageStatus;

  @Column({ name: 'read_at', nullable: true })
  readAt: Date;

  @Column({ name: 'is_recalled', default: false })
  isRecalled: boolean;

  @Column({ name: 'recalled_at', nullable: true })
  recalledAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => ChatSession, (session) => session.messages)
  @JoinColumn({ name: 'session_id' })
  session: ChatSession;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'sender_id' })
  sender: User;
}
```

### 8.1.3 WebSocket网关实现

接下来实现WebSocket网关，处理实时通讯连接和消息传输：

```typescript
// src/modules/chat/gateways/chat.gateway.ts
import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Injectable, UseGuards, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ChatService } from '../services/chat.service';
import { WsJwtGuard } from '../../../common/guards/ws-jwt.guard';
import { SendMessageDto } from '../dto/send-message.dto';

@Injectable()
@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
  namespace: '/chat',
})
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(ChatGateway.name);
  private connectedUsers = new Map<string, Set<string>>(); // userId -> Set<socketId>

  constructor(
    private readonly jwtService: JwtService,
    private readonly chatService: ChatService,
  ) {}

  async handleConnection(client: Socket) {
    try {
      const token = client.handshake.auth.token;
      if (!token) {
        client.disconnect();
        return;
      }

      const payload = this.jwtService.verify(token);
      const userId = payload.sub;

      // 将用户加入在线用户列表
      if (!this.connectedUsers.has(userId)) {
        this.connectedUsers.set(userId, new Set());
      }
      this.connectedUsers.get(userId).add(client.id);

      // 将用户信息存储到socket中
      client.data.userId = userId;
      client.data.userRole = payload.role;

      // 加入用户专属房间
      await client.join(`user_${userId}`);

      this.logger.log(`User ${userId} connected with socket ${client.id}`);

      // 通知用户上线状态
      this.server.emit('user_online', { userId });

      // 发送离线消息
      await this.sendOfflineMessages(userId);
    } catch (error) {
      this.logger.error('Connection error:', error);
      client.disconnect();
    }
  }

  async handleDisconnect(client: Socket) {
    const userId = client.data?.userId;
    if (userId) {
      const userSockets = this.connectedUsers.get(userId);
      if (userSockets) {
        userSockets.delete(client.id);
        if (userSockets.size === 0) {
          this.connectedUsers.delete(userId);
          // 通知用户离线状态
          this.server.emit('user_offline', { userId });
        }
      }
      this.logger.log(`User ${userId} disconnected`);
    }
  }

  @UseGuards(WsJwtGuard)
  @SubscribeMessage('send_message')
  async handleSendMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: SendMessageDto,
  ) {
    try {
      const userId = client.data.userId;
      const message = await this.chatService.sendMessage(userId, data);

      // 发送给会话中的所有用户
      const session = await this.chatService.getSessionById(data.sessionId);
      const targetUsers = [session.userId, session.serviceProviderId].filter(
        (id) => id && id !== userId,
      );

      // 发送给发送者确认
      client.emit('message_sent', message);

      // 发送给接收者
      targetUsers.forEach((targetUserId) => {
        this.server.to(`user_${targetUserId}`).emit('new_message', message);
      });

      this.logger.log(`Message sent from ${userId} in session ${data.sessionId}`);
    } catch (error) {
      this.logger.error('Send message error:', error);
      client.emit('message_error', { error: error.message });
    }
  }

  @UseGuards(WsJwtGuard)
  @SubscribeMessage('mark_as_read')
  async handleMarkAsRead(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { sessionId: string; messageId: string },
  ) {
    try {
      const userId = client.data.userId;
      await this.chatService.markMessageAsRead(data.messageId, userId);

      // 通知发送者消息已读
      const message = await this.chatService.getMessageById(data.messageId);
      this.server
        .to(`user_${message.senderId}`)
        .emit('message_read', { messageId: data.messageId, readBy: userId });
    } catch (error) {
      this.logger.error('Mark as read error:', error);
    }
  }

  @UseGuards(WsJwtGuard)
  @SubscribeMessage('join_session')
  async handleJoinSession(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { sessionId: string },
  ) {
    await client.join(`session_${data.sessionId}`);
    this.logger.log(`User ${client.data.userId} joined session ${data.sessionId}`);
  }

  @UseGuards(WsJwtGuard)
  @SubscribeMessage('leave_session')
  async handleLeaveSession(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { sessionId: string },
  ) {
    await client.leave(`session_${data.sessionId}`);
    this.logger.log(`User ${client.data.userId} left session ${data.sessionId}`);
  }

  // 发送离线消息
  private async sendOfflineMessages(userId: string) {
    try {
      const offlineMessages = await this.chatService.getOfflineMessages(userId);
      if (offlineMessages.length > 0) {
        this.server.to(`user_${userId}`).emit('offline_messages', offlineMessages);
      }
    } catch (error) {
      this.logger.error('Send offline messages error:', error);
    }
  }

  // 检查用户是否在线
  isUserOnline(userId: string): boolean {
    return this.connectedUsers.has(userId);
  }

  // 向特定用户发送消息
  async sendToUser(userId: string, event: string, data: any) {
    this.server.to(`user_${userId}`).emit(event, data);
  }
}
```

### 8.1.4 聊天服务实现

实现聊天核心业务逻辑：

```typescript
// src/modules/chat/services/chat.service.ts
import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { ChatSession, ChatSessionType, ChatSessionStatus } from '../entities/chat-session.entity';
import { ChatMessage, MessageType, MessageStatus } from '../entities/chat-message.entity';
import { SendMessageDto } from '../dto/send-message.dto';
import { CreateSessionDto } from '../dto/create-session.dto';
import { GetMessagesDto } from '../dto/get-messages.dto';
import { FileUploadService } from '../../file/services/file-upload.service';

@Injectable()
export class ChatService {
  constructor(
    @InjectRepository(ChatSession)
    private readonly sessionRepository: Repository<ChatSession>,
    @InjectRepository(ChatMessage)
    private readonly messageRepository: Repository<ChatMessage>,
    private readonly fileUploadService: FileUploadService,
  ) {}

  // 创建聊天会话
  async createSession(createSessionDto: CreateSessionDto): Promise<ChatSession> {
    const { userId, serviceProviderId, orderId, type } = createSessionDto;

    // 检查是否已存在相同的会话
    let existingSession = null;
    if (orderId) {
      existingSession = await this.sessionRepository.findOne({
        where: { orderId, status: ChatSessionStatus.ACTIVE },
      });
    } else {
      existingSession = await this.sessionRepository.findOne({
        where: {
          userId,
          serviceProviderId,
          type,
          status: ChatSessionStatus.ACTIVE,
        },
      });
    }

    if (existingSession) {
      return existingSession;
    }

    // 创建新会话
    const session = this.sessionRepository.create({
      userId,
      serviceProviderId,
      orderId,
      type: type || ChatSessionType.ORDER_CHAT,
      status: ChatSessionStatus.ACTIVE,
    });

    return await this.sessionRepository.save(session);
  }

  // 发送消息
  async sendMessage(senderId: string, sendMessageDto: SendMessageDto): Promise<ChatMessage> {
    const { sessionId, content, type, mediaFile } = sendMessageDto;

    // 验证会话是否存在且活跃
    const session = await this.sessionRepository.findOne({
      where: { id: sessionId, status: ChatSessionStatus.ACTIVE },
    });

    if (!session) {
      throw new NotFoundException('聊天会话不存在或已关闭');
    }

    // 验证发送者是否有权限在此会话中发送消息
    if (session.userId !== senderId && session.serviceProviderId !== senderId) {
      throw new BadRequestException('无权限在此会话中发送消息');
    }

    let mediaUrl = null;
    let mediaSize = null;
    let mediaDuration = null;

    // 处理媒体文件上传
    if (mediaFile && type !== MessageType.TEXT) {
      const uploadResult = await this.fileUploadService.uploadFile(
        mediaFile,
        'chat-media',
      );
      mediaUrl = uploadResult.url;
      mediaSize = mediaFile.size;

      // 如果是语音消息，可以从文件元数据中获取时长
      if (type === MessageType.VOICE) {
        mediaDuration = sendMessageDto.mediaDuration;
      }
    }

    // 创建消息
    const message = this.messageRepository.create({
      sessionId,
      senderId,
      content: content || '',
      type: type || MessageType.TEXT,
      mediaUrl,
      mediaSize,
      mediaDuration,
      status: MessageStatus.SENT,
    });

    const savedMessage = await this.messageRepository.save(message);

    // 更新会话的最后消息信息
    await this.updateSessionLastMessage(sessionId, content || '[媒体消息]');

    // 更新未读消息计数
    await this.updateUnreadCount(sessionId, senderId);

    return await this.messageRepository.findOne({
      where: { id: savedMessage.id },
      relations: ['sender'],
    });
  }

  // 获取会话消息列表
  async getMessages(sessionId: string, getMessagesDto: GetMessagesDto): Promise<{
    messages: ChatMessage[];
    total: number;
    hasMore: boolean;
  }> {
    const { page = 1, limit = 20, beforeMessageId } = getMessagesDto;

    let whereCondition: any = { sessionId };

    // 如果指定了beforeMessageId，则获取该消息之前的消息
    if (beforeMessageId) {
      const beforeMessage = await this.messageRepository.findOne({
        where: { id: beforeMessageId },
      });
      if (beforeMessage) {
        whereCondition.createdAt = { $lt: beforeMessage.createdAt };
      }
    }

    const [messages, total] = await this.messageRepository.findAndCount({
      where: whereCondition,
      relations: ['sender'],
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      messages: messages.reverse(), // 按时间正序返回
      total,
      hasMore: total > page * limit,
    };
  }

  // 标记消息为已读
  async markMessageAsRead(messageId: string, userId: string): Promise<void> {
    const message = await this.messageRepository.findOne({
      where: { id: messageId },
      relations: ['session'],
    });

    if (!message) {
      throw new NotFoundException('消息不存在');
    }

    // 只有接收者才能标记消息为已读
    if (message.senderId === userId) {
      return;
    }

    // 验证用户是否有权限读取此消息
    const session = message.session;
    if (session.userId !== userId && session.serviceProviderId !== userId) {
      throw new BadRequestException('无权限读取此消息');
    }

    // 更新消息状态
    await this.messageRepository.update(messageId, {
      status: MessageStatus.READ,
      readAt: new Date(),
    });

    // 更新会话未读计数
    if (session.userId === userId) {
      await this.sessionRepository.update(session.id, {
        unreadCountUser: 0,
      });
    } else {
      await this.sessionRepository.update(session.id, {
        unreadCountProvider: 0,
      });
    }
  }

  // 获取用户的聊天会话列表
  async getUserSessions(userId: string): Promise<ChatSession[]> {
    return await this.sessionRepository.find({
      where: [
        { userId, status: ChatSessionStatus.ACTIVE },
        { serviceProviderId: userId, status: ChatSessionStatus.ACTIVE },
      ],
      relations: ['user', 'serviceProvider', 'order'],
      order: { lastMessageTime: 'DESC' },
    });
  }

  // 获取会话详情
  async getSessionById(sessionId: string): Promise<ChatSession> {
    const session = await this.sessionRepository.findOne({
      where: { id: sessionId },
      relations: ['user', 'serviceProvider', 'order'],
    });

    if (!session) {
      throw new NotFoundException('聊天会话不存在');
    }

    return session;
  }

  // 获取消息详情
  async getMessageById(messageId: string): Promise<ChatMessage> {
    const message = await this.messageRepository.findOne({
      where: { id: messageId },
      relations: ['sender', 'session'],
    });

    if (!message) {
      throw new NotFoundException('消息不存在');
    }

    return message;
  }

  // 获取离线消息
  async getOfflineMessages(userId: string): Promise<ChatMessage[]> {
    // 获取用户参与的所有活跃会话
    const sessions = await this.sessionRepository.find({
      where: [
        { userId, status: ChatSessionStatus.ACTIVE },
        { serviceProviderId: userId, status: ChatSessionStatus.ACTIVE },
      ],
    });

    if (sessions.length === 0) {
      return [];
    }

    const sessionIds = sessions.map(session => session.id);

    // 获取这些会话中的未读消息
    return await this.messageRepository.find({
      where: {
        sessionId: In(sessionIds),
        senderId: { $ne: userId },
        status: { $in: [MessageStatus.SENT, MessageStatus.DELIVERED] },
      },
      relations: ['sender', 'session'],
      order: { createdAt: 'ASC' },
      take: 100, // 限制离线消息数量
    });
  }

  // 关闭会话
  async closeSession(sessionId: string, userId: string): Promise<void> {
    const session = await this.getSessionById(sessionId);

    // 验证权限
    if (session.userId !== userId && session.serviceProviderId !== userId) {
      throw new BadRequestException('无权限关闭此会话');
    }

    await this.sessionRepository.update(sessionId, {
      status: ChatSessionStatus.CLOSED,
    });
  }

  // 更新会话最后消息
  private async updateSessionLastMessage(sessionId: string, content: string): Promise<void> {
    await this.sessionRepository.update(sessionId, {
      lastMessage: content,
      lastMessageTime: new Date(),
    });
  }

  // 更新未读消息计数
  private async updateUnreadCount(sessionId: string, senderId: string): Promise<void> {
    const session = await this.sessionRepository.findOne({
      where: { id: sessionId },
    });

    if (!session) return;

    // 增加接收者的未读计数
    if (session.userId === senderId) {
      // 发送者是用户，增加服务商的未读计数
      await this.sessionRepository.increment(
        { id: sessionId },
        'unreadCountProvider',
        1,
      );
    } else {
      // 发送者是服务商，增加用户的未读计数
      await this.sessionRepository.increment(
        { id: sessionId },
        'unreadCountUser',
        1,
      );
    }
  }
}
```

### 8.1.5 DTO定义

定义聊天相关的数据传输对象：

```typescript
// src/modules/chat/dto/send-message.dto.ts
import { IsString, IsEnum, IsOptional, IsNumber, MaxLength } from 'class-validator';
import { MessageType } from '../entities/chat-message.entity';

export class SendMessageDto {
  @IsString()
  sessionId: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000, { message: '消息内容不能超过1000个字符' })
  content?: string;

  @IsOptional()
  @IsEnum(MessageType)
  type?: MessageType;

  @IsOptional()
  mediaFile?: Express.Multer.File;

  @IsOptional()
  @IsNumber()
  mediaDuration?: number; // 语音消息时长(秒)
}
```

```typescript
// src/modules/chat/dto/create-session.dto.ts
import { IsString, IsEnum, IsOptional } from 'class-validator';
import { ChatSessionType } from '../entities/chat-session.entity';

export class CreateSessionDto {
  @IsString()
  userId: string;

  @IsOptional()
  @IsString()
  serviceProviderId?: string;

  @IsOptional()
  @IsString()
  orderId?: string;

  @IsOptional()
  @IsEnum(ChatSessionType)
  type?: ChatSessionType;
}
```

```typescript
// src/modules/chat/dto/get-messages.dto.ts
import { IsOptional, IsNumber, IsString, Min, Max } from 'class-validator';
import { Transform } from 'class-transformer';

export class GetMessagesDto {
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 20;

  @IsOptional()
  @IsString()
  beforeMessageId?: string;
}
```

### 8.1.6 聊天控制器

实现RESTful API接口：

```typescript
// src/modules/chat/controllers/chat.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../../../common/guards/jwt-auth.guard';
import { ChatService } from '../services/chat.service';
import { CreateSessionDto } from '../dto/create-session.dto';
import { SendMessageDto } from '../dto/send-message.dto';
import { GetMessagesDto } from '../dto/get-messages.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('聊天管理')
@ApiBearerAuth()
@Controller('chat')
@UseGuards(JwtAuthGuard)
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Post('sessions')
  @ApiOperation({ summary: '创建聊天会话' })
  @ApiResponse({ status: 201, description: '会话创建成功' })
  async createSession(@Body() createSessionDto: CreateSessionDto) {
    return await this.chatService.createSession(createSessionDto);
  }

  @Get('sessions')
  @ApiOperation({ summary: '获取用户聊天会话列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getUserSessions(@Request() req) {
    const userId = req.user.sub;
    return await this.chatService.getUserSessions(userId);
  }

  @Get('sessions/:sessionId')
  @ApiOperation({ summary: '获取会话详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getSession(@Param('sessionId') sessionId: string) {
    return await this.chatService.getSessionById(sessionId);
  }

  @Get('sessions/:sessionId/messages')
  @ApiOperation({ summary: '获取会话消息列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getMessages(
    @Param('sessionId') sessionId: string,
    @Query() getMessagesDto: GetMessagesDto,
  ) {
    return await this.chatService.getMessages(sessionId, getMessagesDto);
  }

  @Post('messages')
  @ApiOperation({ summary: '发送消息' })
  @ApiResponse({ status: 201, description: '消息发送成功' })
  @UseInterceptors(FileInterceptor('mediaFile'))
  async sendMessage(
    @Request() req,
    @Body() sendMessageDto: SendMessageDto,
    @UploadedFile() mediaFile?: Express.Multer.File,
  ) {
    const userId = req.user.sub;
    if (mediaFile) {
      sendMessageDto.mediaFile = mediaFile;
    }
    return await this.chatService.sendMessage(userId, sendMessageDto);
  }

  @Post('messages/:messageId/read')
  @ApiOperation({ summary: '标记消息为已读' })
  @ApiResponse({ status: 200, description: '标记成功' })
  async markAsRead(@Param('messageId') messageId: string, @Request() req) {
    const userId = req.user.sub;
    await this.chatService.markMessageAsRead(messageId, userId);
    return { message: '标记成功' };
  }

  @Post('sessions/:sessionId/close')
  @ApiOperation({ summary: '关闭会话' })
  @ApiResponse({ status: 200, description: '关闭成功' })
  async closeSession(@Param('sessionId') sessionId: string, @Request() req) {
    const userId = req.user.sub;
    await this.chatService.closeSession(sessionId, userId);
    return { message: '会话已关闭' };
  }
}
```

---

## 8.2 消息推送与通知系统

### 8.2.1 通知系统架构

通知系统负责向用户发送各种类型的消息，包括系统通知、短信、邮件等。

#### 系统特点

1. **多渠道通知支持**
   - 站内消息推送
   - 短信通知(阿里云SMS)
   - 邮件通知(SMTP)
   - 微信模板消息(可选)

2. **消息队列处理**
   - 异步消息发送
   - 失败重试机制
   - 消息优先级管理

3. **模板化消息管理**
   - 消息模板配置
   - 动态参数替换
   - 多语言支持

### 8.2.2 通知实体设计

```typescript
// src/modules/notification/entities/notification.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../user/entities/user.entity';

export enum NotificationType {
  SYSTEM = 'system',           // 系统通知
  ORDER = 'order',            // 订单通知
  PAYMENT = 'payment',        // 支付通知
  SERVICE = 'service',        // 服务通知
  PROMOTION = 'promotion',    // 促销通知
  SECURITY = 'security',      // 安全通知
}

export enum NotificationChannel {
  IN_APP = 'in_app',          // 站内消息
  SMS = 'sms',               // 短信
  EMAIL = 'email',           // 邮件
  WECHAT = 'wechat',         // 微信
}

export enum NotificationStatus {
  PENDING = 'pending',        // 待发送
  SENT = 'sent',             // 已发送
  DELIVERED = 'delivered',    // 已送达
  READ = 'read',             // 已读
  FAILED = 'failed',         // 发送失败
}

@Entity('notifications')
@Index(['userId', 'status'])
@Index(['type', 'createdAt'])
export class Notification {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({
    type: 'enum',
    enum: NotificationType,
  })
  type: NotificationType;

  @Column({
    type: 'enum',
    enum: NotificationChannel,
  })
  channel: NotificationChannel;

  @Column()
  title: string;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'json', nullable: true })
  data: any; // 额外数据，如订单ID、链接等

  @Column({
    type: 'enum',
    enum: NotificationStatus,
    default: NotificationStatus.PENDING,
  })
  status: NotificationStatus;

  @Column({ name: 'sent_at', nullable: true })
  sentAt: Date;

  @Column({ name: 'read_at', nullable: true })
  readAt: Date;

  @Column({ name: 'retry_count', default: 0 })
  retryCount: number;

  @Column({ name: 'error_message', nullable: true })
  errorMessage: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'user_id' })
  user: User;
}
```

### 8.2.3 通知模板实体

```typescript
// src/modules/notification/entities/notification-template.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { NotificationType, NotificationChannel } from './notification.entity';

@Entity('notification_templates')
@Index(['type', 'channel'])
export class NotificationTemplate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  code: string; // 模板代码，如 'ORDER_CREATED'

  @Column()
  name: string; // 模板名称

  @Column({
    type: 'enum',
    enum: NotificationType,
  })
  type: NotificationType;

  @Column({
    type: 'enum',
    enum: NotificationChannel,
  })
  channel: NotificationChannel;

  @Column()
  title: string; // 支持变量替换，如 '您的订单{{orderNo}}已创建'

  @Column({ type: 'text' })
  content: string; // 支持变量替换

  @Column({ type: 'json', nullable: true })
  variables: string[]; // 模板变量列表

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
```

### 8.2.4 通知服务实现

```typescript
// src/modules/notification/services/notification.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { Notification, NotificationType, NotificationChannel, NotificationStatus } from '../entities/notification.entity';
import { NotificationTemplate } from '../entities/notification-template.entity';
import { SendNotificationDto } from '../dto/send-notification.dto';
import { SmsService } from '../providers/sms.service';
import { EmailService } from '../providers/email.service';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
    @InjectRepository(NotificationTemplate)
    private readonly templateRepository: Repository<NotificationTemplate>,
    @InjectQueue('notification')
    private readonly notificationQueue: Queue,
    private readonly smsService: SmsService,
    private readonly emailService: EmailService,
  ) {}

  // 发送通知
  async sendNotification(sendNotificationDto: SendNotificationDto): Promise<void> {
    const { userId, templateCode, variables, channels, priority = 0 } = sendNotificationDto;

    // 获取模板
    const templates = await this.templateRepository.find({
      where: {
        code: templateCode,
        channel: channels ? { $in: channels } : undefined,
        isActive: true,
      },
    });

    if (templates.length === 0) {
      this.logger.warn(`No active templates found for code: ${templateCode}`);
      return;
    }

    // 为每个渠道创建通知
    for (const template of templates) {
      const notification = await this.createNotification(userId, template, variables);

      // 添加到队列异步处理
      await this.notificationQueue.add(
        'send',
        { notificationId: notification.id },
        { priority, attempts: 3, backoff: 'exponential' },
      );
    }
  }

  // 创建通知记录
  private async createNotification(
    userId: string,
    template: NotificationTemplate,
    variables: Record<string, any> = {},
  ): Promise<Notification> {
    const title = this.replaceVariables(template.title, variables);
    const content = this.replaceVariables(template.content, variables);

    const notification = this.notificationRepository.create({
      userId,
      type: template.type,
      channel: template.channel,
      title,
      content,
      data: variables,
      status: NotificationStatus.PENDING,
    });

    return await this.notificationRepository.save(notification);
  }

  // 处理通知发送
  async processNotification(notificationId: string): Promise<void> {
    const notification = await this.notificationRepository.findOne({
      where: { id: notificationId },
      relations: ['user'],
    });

    if (!notification) {
      this.logger.error(`Notification not found: ${notificationId}`);
      return;
    }

    try {
      switch (notification.channel) {
        case NotificationChannel.IN_APP:
          await this.sendInAppNotification(notification);
          break;
        case NotificationChannel.SMS:
          await this.sendSmsNotification(notification);
          break;
        case NotificationChannel.EMAIL:
          await this.sendEmailNotification(notification);
          break;
        default:
          throw new Error(`Unsupported channel: ${notification.channel}`);
      }

      // 更新发送状态
      await this.notificationRepository.update(notification.id, {
        status: NotificationStatus.SENT,
        sentAt: new Date(),
      });

      this.logger.log(`Notification sent successfully: ${notificationId}`);
    } catch (error) {
      this.logger.error(`Failed to send notification ${notificationId}:`, error);

      // 更新失败状态
      await this.notificationRepository.update(notification.id, {
        status: NotificationStatus.FAILED,
        retryCount: notification.retryCount + 1,
        errorMessage: error.message,
      });

      throw error;
    }
  }

  // 发送站内通知
  private async sendInAppNotification(notification: Notification): Promise<void> {
    // 站内通知直接标记为已发送，通过WebSocket实时推送
    // 这里可以集成WebSocket推送逻辑
    this.logger.log(`In-app notification sent to user: ${notification.userId}`);
  }

  // 发送短信通知
  private async sendSmsNotification(notification: Notification): Promise<void> {
    const phoneNumber = notification.user.phone;
    if (!phoneNumber) {
      throw new Error('User phone number not found');
    }

    await this.smsService.sendSms(phoneNumber, notification.content);
  }

  // 发送邮件通知
  private async sendEmailNotification(notification: Notification): Promise<void> {
    const email = notification.user.email;
    if (!email) {
      throw new Error('User email not found');
    }

    await this.emailService.sendEmail(
      email,
      notification.title,
      notification.content,
    );
  }

  // 获取用户通知列表
  async getUserNotifications(
    userId: string,
    page: number = 1,
    limit: number = 20,
    type?: NotificationType,
  ): Promise<{
    notifications: Notification[];
    total: number;
    unreadCount: number;
  }> {
    const whereCondition: any = { userId, channel: NotificationChannel.IN_APP };
    if (type) {
      whereCondition.type = type;
    }

    const [notifications, total] = await this.notificationRepository.findAndCount({
      where: whereCondition,
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    const unreadCount = await this.notificationRepository.count({
      where: {
        userId,
        channel: NotificationChannel.IN_APP,
        status: { $in: [NotificationStatus.SENT, NotificationStatus.DELIVERED] },
      },
    });

    return { notifications, total, unreadCount };
  }

  // 标记通知为已读
  async markAsRead(notificationId: string, userId: string): Promise<void> {
    await this.notificationRepository.update(
      { id: notificationId, userId },
      { status: NotificationStatus.READ, readAt: new Date() },
    );
  }

  // 批量标记为已读
  async markAllAsRead(userId: string): Promise<void> {
    await this.notificationRepository.update(
      {
        userId,
        channel: NotificationChannel.IN_APP,
        status: { $in: [NotificationStatus.SENT, NotificationStatus.DELIVERED] },
      },
      { status: NotificationStatus.READ, readAt: new Date() },
    );
  }

  // 变量替换
  private replaceVariables(template: string, variables: Record<string, any>): string {
    let result = template;
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, String(value));
    }
    return result;
  }
}
```

---

## 8.3 评价反馈体系

### 8.3.1 评价系统设计

评价反馈体系是O2O平台的重要组成部分，用于收集用户对服务的反馈，建立服务商信誉体系。

#### 系统特点

1. **多维度评价**
   - 服务质量评分
   - 服务态度评分
   - 时效性评分
   - 性价比评分

2. **评价真实性保障**
   - 订单完成后才能评价
   - 防止恶意刷评
   - 评价内容审核

3. **信誉积分计算**
   - 综合评分计算
   - 历史评价权重
   - 服务商等级评定

### 8.3.2 评价实体设计

```typescript
// src/modules/review/entities/review.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../user/entities/user.entity';
import { Order } from '../../order/entities/order.entity';

export enum ReviewStatus {
  PENDING = 'pending',     // 待审核
  APPROVED = 'approved',   // 已通过
  REJECTED = 'rejected',   // 已拒绝
}

@Entity('reviews')
@Index(['orderId'])
@Index(['serviceProviderId', 'status'])
@Index(['userId'])
export class Review {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'order_id' })
  orderId: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'service_provider_id' })
  serviceProviderId: string;

  @Column({ name: 'service_quality_score', type: 'decimal', precision: 3, scale: 1 })
  serviceQualityScore: number; // 服务质量评分 1-5

  @Column({ name: 'service_attitude_score', type: 'decimal', precision: 3, scale: 1 })
  serviceAttitudeScore: number; // 服务态度评分 1-5

  @Column({ name: 'timeliness_score', type: 'decimal', precision: 3, scale: 1 })
  timelinessScore: number; // 时效性评分 1-5

  @Column({ name: 'value_score', type: 'decimal', precision: 3, scale: 1 })
  valueScore: number; // 性价比评分 1-5

  @Column({ name: 'overall_score', type: 'decimal', precision: 3, scale: 1 })
  overallScore: number; // 综合评分 1-5

  @Column({ type: 'text', nullable: true })
  comment: string; // 评价内容

  @Column({ type: 'json', nullable: true })
  images: string[]; // 评价图片

  @Column({
    type: 'enum',
    enum: ReviewStatus,
    default: ReviewStatus.PENDING,
  })
  status: ReviewStatus;

  @Column({ name: 'is_anonymous', default: false })
  isAnonymous: boolean; // 是否匿名评价

  @Column({ name: 'admin_comment', type: 'text', nullable: true })
  adminComment: string; // 管理员审核意见

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => Order, { eager: true })
  @JoinColumn({ name: 'order_id' })
  order: Order;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'service_provider_id' })
  serviceProvider: User;
}
```

### 8.3.3 评价服务实现

```typescript
// src/modules/review/services/review.service.ts
import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Review, ReviewStatus } from '../entities/review.entity';
import { Order, OrderStatus } from '../../order/entities/order.entity';
import { CreateReviewDto } from '../dto/create-review.dto';
import { GetReviewsDto } from '../dto/get-reviews.dto';

@Injectable()
export class ReviewService {
  constructor(
    @InjectRepository(Review)
    private readonly reviewRepository: Repository<Review>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
  ) {}

  // 创建评价
  async createReview(userId: string, createReviewDto: CreateReviewDto): Promise<Review> {
    const { orderId, ...reviewData } = createReviewDto;

    // 验证订单是否存在且已完成
    const order = await this.orderRepository.findOne({
      where: { id: orderId, userId },
      relations: ['serviceProvider'],
    });

    if (!order) {
      throw new NotFoundException('订单不存在');
    }

    if (order.status !== OrderStatus.COMPLETED) {
      throw new BadRequestException('只能对已完成的订单进行评价');
    }

    // 检查是否已经评价过
    const existingReview = await this.reviewRepository.findOne({
      where: { orderId, userId },
    });

    if (existingReview) {
      throw new BadRequestException('该订单已经评价过了');
    }

    // 计算综合评分
    const overallScore = this.calculateOverallScore(reviewData);

    // 创建评价
    const review = this.reviewRepository.create({
      orderId,
      userId,
      serviceProviderId: order.serviceProviderId,
      ...reviewData,
      overallScore,
      status: ReviewStatus.PENDING,
    });

    const savedReview = await this.reviewRepository.save(review);

    // 更新服务商评分统计
    await this.updateServiceProviderRating(order.serviceProviderId);

    return savedReview;
  }

  // 获取评价列表
  async getReviews(getReviewsDto: GetReviewsDto): Promise<{
    reviews: Review[];
    total: number;
    averageScore: number;
  }> {
    const {
      serviceProviderId,
      userId,
      status = ReviewStatus.APPROVED,
      page = 1,
      limit = 20,
    } = getReviewsDto;

    const whereCondition: any = { status };
    if (serviceProviderId) {
      whereCondition.serviceProviderId = serviceProviderId;
    }
    if (userId) {
      whereCondition.userId = userId;
    }

    const [reviews, total] = await this.reviewRepository.findAndCount({
      where: whereCondition,
      relations: ['user', 'order'],
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    // 计算平均评分
    const averageScore = await this.calculateAverageScore(serviceProviderId);

    return { reviews, total, averageScore };
  }

  // 获取服务商评价统计
  async getServiceProviderReviewStats(serviceProviderId: string): Promise<{
    totalReviews: number;
    averageScore: number;
    scoreDistribution: Record<string, number>;
    recentReviews: Review[];
  }> {
    const totalReviews = await this.reviewRepository.count({
      where: { serviceProviderId, status: ReviewStatus.APPROVED },
    });

    const averageScore = await this.calculateAverageScore(serviceProviderId);

    // 评分分布统计
    const scoreDistribution = await this.getScoreDistribution(serviceProviderId);

    // 最近的评价
    const recentReviews = await this.reviewRepository.find({
      where: { serviceProviderId, status: ReviewStatus.APPROVED },
      relations: ['user', 'order'],
      order: { createdAt: 'DESC' },
      take: 5,
    });

    return {
      totalReviews,
      averageScore,
      scoreDistribution,
      recentReviews,
    };
  }

  // 审核评价
  async reviewApproval(
    reviewId: string,
    status: ReviewStatus,
    adminComment?: string,
  ): Promise<void> {
    const review = await this.reviewRepository.findOne({
      where: { id: reviewId },
    });

    if (!review) {
      throw new NotFoundException('评价不存在');
    }

    await this.reviewRepository.update(reviewId, {
      status,
      adminComment,
    });

    // 如果审核通过，更新服务商评分
    if (status === ReviewStatus.APPROVED) {
      await this.updateServiceProviderRating(review.serviceProviderId);
    }
  }

  // 计算综合评分
  private calculateOverallScore(reviewData: Partial<CreateReviewDto>): number {
    const {
      serviceQualityScore,
      serviceAttitudeScore,
      timelinessScore,
      valueScore,
    } = reviewData;

    // 各维度权重
    const weights = {
      serviceQuality: 0.3,
      serviceAttitude: 0.25,
      timeliness: 0.25,
      value: 0.2,
    };

    const overallScore =
      serviceQualityScore * weights.serviceQuality +
      serviceAttitudeScore * weights.serviceAttitude +
      timelinessScore * weights.timeliness +
      valueScore * weights.value;

    return Math.round(overallScore * 10) / 10; // 保留一位小数
  }

  // 计算平均评分
  private async calculateAverageScore(serviceProviderId: string): Promise<number> {
    const result = await this.reviewRepository
      .createQueryBuilder('review')
      .select('AVG(review.overallScore)', 'averageScore')
      .where('review.serviceProviderId = :serviceProviderId', { serviceProviderId })
      .andWhere('review.status = :status', { status: ReviewStatus.APPROVED })
      .getRawOne();

    return result.averageScore ? Math.round(result.averageScore * 10) / 10 : 0;
  }

  // 获取评分分布
  private async getScoreDistribution(serviceProviderId: string): Promise<Record<string, number>> {
    const result = await this.reviewRepository
      .createQueryBuilder('review')
      .select('FLOOR(review.overallScore)', 'score')
      .addSelect('COUNT(*)', 'count')
      .where('review.serviceProviderId = :serviceProviderId', { serviceProviderId })
      .andWhere('review.status = :status', { status: ReviewStatus.APPROVED })
      .groupBy('FLOOR(review.overallScore)')
      .getRawMany();

    const distribution = { '1': 0, '2': 0, '3': 0, '4': 0, '5': 0 };
    result.forEach((item) => {
      distribution[item.score] = parseInt(item.count);
    });

    return distribution;
  }

  // 更新服务商评分统计
  private async updateServiceProviderRating(serviceProviderId: string): Promise<void> {
    const averageScore = await this.calculateAverageScore(serviceProviderId);
    const totalReviews = await this.reviewRepository.count({
      where: { serviceProviderId, status: ReviewStatus.APPROVED },
    });

    // 这里可以更新用户表中的评分字段
    // await this.userRepository.update(serviceProviderId, {
    //   averageRating: averageScore,
    //   totalReviews,
    // });
  }
}
```

### 8.3.4 DTO定义

```typescript
// src/modules/review/dto/create-review.dto.ts
import {
  IsString,
  IsNumber,
  IsOptional,
  IsBoolean,
  IsArray,
  Min,
  Max,
  MaxLength,
} from 'class-validator';

export class CreateReviewDto {
  @IsString()
  orderId: string;

  @IsNumber()
  @Min(1)
  @Max(5)
  serviceQualityScore: number;

  @IsNumber()
  @Min(1)
  @Max(5)
  serviceAttitudeScore: number;

  @IsNumber()
  @Min(1)
  @Max(5)
  timelinessScore: number;

  @IsNumber()
  @Min(1)
  @Max(5)
  valueScore: number;

  @IsOptional()
  @IsString()
  @MaxLength(500, { message: '评价内容不能超过500个字符' })
  comment?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  images?: string[];

  @IsOptional()
  @IsBoolean()
  isAnonymous?: boolean;
}
```

```typescript
// src/modules/review/dto/get-reviews.dto.ts
import { IsOptional, IsString, IsEnum, IsNumber, Min, Max } from 'class-validator';
import { Transform } from 'class-transformer';
import { ReviewStatus } from '../entities/review.entity';

export class GetReviewsDto {
  @IsOptional()
  @IsString()
  serviceProviderId?: string;

  @IsOptional()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsEnum(ReviewStatus)
  status?: ReviewStatus;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 20;
}
```

## 总结

本篇教程实现了完整的通讯互动与评价反馈系统，包括：

### 主要功能

1. **即时通讯系统**
   - WebSocket实时通讯
   - 多媒体消息支持
   - 消息状态跟踪
   - 离线消息处理

2. **消息推送系统**
   - 多渠道通知支持
   - 模板化消息管理
   - 异步队列处理
   - 失败重试机制

3. **评价反馈体系**
   - 多维度评价系统
   - 评价真实性保障
   - 服务商信誉计算
   - 评价审核机制

### 技术亮点

- **实时通讯**: 基于Socket.IO的WebSocket实现
- **消息队列**: 使用Bull Queue处理异步任务
- **模板引擎**: 支持变量替换的消息模板
- **评分算法**: 多维度加权评分计算

### 下一步

在下一篇教程中，我们将开始前端开发，实现React前端项目的初始化与基础配置，为用户提供现代化的Web界面。
