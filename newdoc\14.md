好的，我们继续进行前端部分的**第六篇：消息通知与支付集成 (概念)**。

在本篇中，我们将讨论两个非常重要的模块：**消息通知**和**支付集成**。由于这两个模块的实现细节往往依赖于后端服务和第三方支付平台，前端部分将主要围绕**概念设计、用户界面交互和集成点的预留**展开，不会提供完整的代码实现。

---

## 第六篇：消息通知与支付集成 (概念)

### 6.1 消息通知系统

一个高效的消息通知系统对于提升用户体验和平台活跃度至关重要。它能确保用户和技师及时获取订单状态更新、新订单分配、评价通知等关键信息。

#### 6.1.1 消息通知类型

我们需要区分不同类型的通知，以便在前端进行差异化展示和处理：

* **订单状态通知：**
    * 用户订单被接受/拒绝。
    * 技师开始/完成服务。
    * 订单取消/退款。
    * 技师提交报价（对用户）。
    * 用户确认报价（对技师）。
* **新订单通知 (针对技师)：** 当有新的匹配订单时，及时通知技师。
* **评价通知：** 用户完成评价后，通知技师。
* **系统通知：** 平台公告、维护通知等。
* **聊天消息：** 如果有内置聊天功能，则需要实时消息通知。

#### 6.1.2 前端实现思路

1.  **通知中心页面 (`src/pages/NotificationPage.tsx`)**
    * **UI 设计：** 使用 Ant Design 的 `List` 或 `Table` 组件展示消息列表。每条消息应包含：
        * **标题/摘要：** 简要说明通知内容。
        * **时间：** 通知发送时间。
        * **状态：** 已读/未读标记。
        * **操作：** 点击查看详情，标记为已读，或跳转到相关页面。
    * **筛选/排序：** 提供按类型、时间、已读/未读状态进行筛选和排序的功能。
    * **分页加载：** 如果消息量大，需要实现分页加载。
    * **未读计数：** 在导航栏或用户头像旁显示未读消息数量。

2.  **实时通知 (WebSockets)**
    * **集成 WebSocket 客户端：** 使用 `socket.io-client` 或原生 `WebSocket` API 连接后端 WebSocket 服务器。
    * **监听事件：** 客户端监听后端推送的通知事件（例如：`'orderStatusUpdate'`, `'newOrder'`, `'chatMessage'`）。
    * **UI 提示：** 收到新通知时，通过以下方式进行提示：
        * **顶部提示框：** 使用 Ant Design 的 `notification` 或 `message` 组件在页面顶部弹出短暂提示。
        * **红点标记：** 在导航栏的通知图标上显示小红点或未读数量。
        * **声音提醒 (可选)：** 在某些关键通知时播放提示音。
    * **数据更新：** 实时更新通知列表，或促使用户刷新相关页面。

3.  **API 接口预留**
    * `GET /notifications`: 获取所有通知列表 (带分页、筛选)。
    * `GET /notifications/unread-count`: 获取未读通知数量。
    * `POST /notifications/:id/mark-as-read`: 将特定通知标记为已读。
    * `POST /notifications/mark-all-as-read`: 将所有通知标记为已读。
    * `DELETE /notifications/:id`: 删除通知。

#### 6.1.3 示例代码结构 (仅概念性展示)

```tsx
// src/pages/NotificationPage.tsx (概念性代码)
import React, { useState, useEffect } from 'react';
import { Card, List, Typography, Spin, Tag, Button, Empty, Popconfirm, message } from 'antd';
import { BellOutlined, CheckCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import api from '../services/api';
import moment from 'moment';
// import { useSocket } from '../contexts/SocketContext'; // 假设你有一个 Socket Context

const { Title, Text } = Typography;

interface Notification {
  id: number;
  type: 'order' | 'system' | 'chat'; // 通知类型
  title: string;
  content: string;
  isRead: boolean;
  createdAt: string;
  relatedEntityId?: number; // 关联的实体ID (如订单ID, 聊天室ID)
  relatedEntityType?: string; // 关联实体类型 (如 'order', 'chat')
}

const NotificationPage: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  // const socket = useSocket(); // 从 context 获取 socket 实例

  useEffect(() => {
    fetchNotifications();
    // 监听实时通知
    // if (socket) {
    //   socket.on('newNotification', (data: Notification) => {
    //     message.info(`您有新的通知: ${data.title}`);
    //     setNotifications(prev => [data, ...prev]); // 新消息置顶
    //     // 触发更新未读计数
    //   });
    // }
    // return () => {
    //   if (socket) {
    //     socket.off('newNotification');
    //   }
    // };
  }, []); // 依赖 socket 实例

  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const response = await api.get('/notifications'); // 获取通知列表
      setNotifications(response.data);
    } catch (error) {
      console.error('获取通知失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMarkAsRead = async (id: number) => {
    try {
      await api.post(`/notifications/${id}/mark-as-read`);
      setNotifications(prev =>
        prev.map(n => (n.id === id ? { ...n, isRead: true } : n))
      );
      message.success('已标记为已读');
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  };

  const handleDeleteNotification = async (id: number) => {
    try {
      await api.delete(`/notifications/${id}`);
      setNotifications(prev => prev.filter(n => n.id !== id));
      message.success('通知已删除');
    } catch (error) {
      console.error('删除通知失败:', error);
    }
  };

  const handleViewNotification = (notification: Notification) => {
    // 根据通知类型和相关实体ID进行跳转
    if (notification.relatedEntityType === 'order' && notification.relatedEntityId) {
      // 根据用户角色，跳转到用户或技师的订单详情页
      // 简单的判断，实际需要更严谨的角色权限判断
      if (localStorage.getItem('userRole') === 'technician') { // 假设角色存在 localStorage
        window.location.href = `/technician/order/${notification.relatedEntityId}`;
      } else {
        window.location.href = `/order/${notification.relatedEntityId}`;
      }
    }
    // ... 其他类型通知的跳转逻辑
    handleMarkAsRead(notification.id); // 查看后自动标记为已读
  };

  return (
    <Card title="我的消息" style={{ maxWidth: 900, margin: '20px auto' }}>
      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}><Spin size="large" tip="加载消息..." /></div>
      ) : (
        <List
          itemLayout="horizontal"
          dataSource={notifications}
          locale={{ emptyText: <Empty description="暂无消息" /> }}
          renderItem={item => (
            <List.Item
              actions={[
                !item.isRead && (
                  <Button key="read" type="link" onClick={() => handleMarkAsRead(item.id)}>
                    标记已读
                  </Button>
                ),
                <Popconfirm
                  key="delete"
                  title="确定删除此消息吗？"
                  onConfirm={() => handleDeleteNotification(item.id)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button type="link" danger icon={<DeleteOutlined />}>删除</Button>
                </Popconfirm>,
              ].filter(Boolean)}
            >
              <List.Item.Meta
                avatar={<BellOutlined style={{ fontSize: 24, color: item.isRead ? '#ccc' : '#1890ff' }} />}
                title={
                  <a onClick={() => handleViewNotification(item)}>
                    <Text strong={!item.isRead}>{item.title}</Text>
                    {!item.isRead && <Tag color="red" style={{ marginLeft: 8 }}>未读</Tag>}
                  </a>
                }
                description={
                  <div>
                    <Paragraph ellipsis={{ rows: 2, expandable: true, symbol: '展开' }}>{item.content}</Paragraph>
                    <Text type="secondary">{moment(item.createdAt).format('YYYY-MM-DD HH:mm')}</Text>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      )}
    </Card>
  );
};

export default NotificationPage;
```

**在 `App.tsx` 中添加路由：**

```tsx
// src/App.tsx (部分)
import NotificationPage from './pages/NotificationPage';
// ... 其他 import

function App() {
  return (
    <Router>
      <Layout className="layout">
        {/* ... Header, Content, Footer */}
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              {/* ... 其他公共路由 */}

              <Route element={<PrivateRoute />}>
                {/* ... 其他受保护的用户路由 */}
                <Route path="/notifications" element={<NotificationPage />} /> {/* 消息通知页面 */}
              </Route>

              {/* ... 404 路由 */}
            </Routes>
          </div>
        </Content>
        {/* ... Footer */}
      </Layout>
    </Router>
  );
}

export default App;
```

### 6.2 支付集成 (概念性)

支付功能是 O2O 平台的核心环节。前端需要引导用户完成支付流程，并处理支付结果。由于支付涉及到资金安全和复杂的第三方集成，这里只进行概念性的描述和预留。

#### 6.2.1 支付流程概述

1.  **用户发起支付：** 用户在订单详情页或报价确认页点击“去支付”按钮。
2.  **后端生成支付订单：** 前端向后端发送支付请求（订单ID、金额等），后端收到请求后，与支付服务商（如微信支付、支付宝）交互，生成支付订单（预支付订单）。
3.  **返回支付信息：** 后端将支付服务商返回的支付信息（如二维码链接、App 跳转参数、H5 支付 URL）返回给前端。
4.  **前端调起支付：** 前端根据支付信息，调起相应的支付界面（例如：显示二维码供用户扫码，跳转到支付宝/微信 App，或跳转到 H5 支付页面）。
5.  **用户完成支付：** 用户在支付服务商的界面完成支付操作。
6.  **支付结果通知：** 支付服务商会异步通知后端支付结果（成功/失败）。后端处理完支付结果后，更新订单状态，并通过 WebSocket 或其他方式通知前端。
7.  **前端展示结果：** 前端根据后端通知或用户手动查询，展示支付成功/失败页面，并引导用户回到订单详情页。

#### 6.2.2 前端集成点预留

1.  **支付按钮：** 在订单详情页或报价确认页，当订单状态为“待支付”或“已报价待确认”时，显示“去支付”按钮。

    ```tsx
    // 在 OrderDetailPage.tsx 中预留
    // ...
    {user?.role === 'user' && (
      <div style={{ textAlign: 'center', marginTop: '30px' }}>
        {order.status === 'quoted' && ( // 技师报价后，用户可以确认并支付
            <Space>
                <Button type="primary" onClick={handleInitiatePayment}>确认报价并支付</Button> {/* 新增的支付按钮 */}
                <Button type="default" danger onClick={handleCancelOrder}>取消订单</Button>
            </Space>
        )}
        {/* ... 其他用户操作 */}
      </div>
    )}
    // ...

    // 支付发起函数 (概念性)
    const handleInitiatePayment = async () => {
        try {
            message.loading('正在创建支付订单...', 0);
            const response = await api.post(`/payments/initiate`, { orderId: order!.id, amount: order!.quotedPrice || order!.totalAmount }); // 假设后端支付发起API
            message.destroy();

            const { paymentUrl, qrCodeData, appId, paySign, ...rest } = response.data; // 后端返回的支付信息

            if (paymentUrl) {
                // H5 支付或 PC 网页支付，直接跳转
                window.location.href = paymentUrl;
            } else if (qrCodeData) {
                // 二维码支付 (如微信/支付宝扫码支付)
                Modal.info({
                    title: '请扫码支付',
                    content: (
                        <div style={{ textAlign: 'center' }}>
                            <QRCode value={qrCodeData} size={200} /> {/* 需要引入 QRCode 组件 */}
                            <p style={{ marginTop: 10 }}>请使用微信或支付宝扫描上方二维码完成支付</p>
                            <Button style={{ marginTop: 20 }} onClick={() => message.info('支付结果将异步通知...')}>我已支付/取消</Button> {/* 提示用户等待 */}
                        </div>
                    ),
                    okButtonProps: { style: { display: 'none' } },
                    onCancel: () => {
                        // 用户关闭弹窗，可以提示他们支付未完成，或提供查询支付状态的按钮
                        message.warning('支付未完成，您可以稍后在订单详情页继续支付。');
                    }
                });
            } else if (appId && paySign) {
                // App 支付 (如微信/支付宝 App 内支付)
                // 引导用户打开 App，或通过 Scheme / Universal Link 跳转
                // 具体实现复杂，这里仅为概念
                message.info('正在尝试跳转至支付App...');
                // 这里可能需要前端SDK或URL Scheme跳转逻辑
            } else {
                message.error('未获取到有效的支付方式，请联系客服。');
            }
        } catch (error) {
            console.error('发起支付失败:', error);
            message.error('发起支付失败，请稍后再试。');
        }
    };

    // 如果需要 QRCode 组件，你可能需要安装 qrcode.react 或自己实现
    // npm install qrcode.react
    // import QRCode from 'qrcode.react';
    ```

2.  **支付结果页面 (`src/pages/PaymentResultPage.tsx`) (可选)**
    * 支付完成后，支付服务商可能会回调到你的网站，显示支付结果。
    * 页面需要解析 URL 参数（如 `status=success/fail`, `orderId=xxx`），然后根据订单 ID 向后端查询最终支付状态。

    ```tsx
    // src/pages/PaymentResultPage.tsx (概念性代码)
    import React, { useEffect, useState } from 'react';
    import { Result, Button, Spin, Typography } from 'antd';
    import { useParams, useLocation, useNavigate } from 'react-router-dom';
    import api from '../services/api';

    const { Text } = Typography;

    const PaymentResultPage: React.FC = () => {
      const location = useLocation();
      const navigate = useNavigate();
      const [paymentStatus, setPaymentStatus] = useState<'success' | 'fail' | 'processing' | null>(null);
      const [orderId, setOrderId] = useState<number | null>(null);
      const [loading, setLoading] = useState(true);

      useEffect(() => {
        const queryParams = new URLSearchParams(location.search);
        const status = queryParams.get('status'); // 假设支付服务商回调时会带 status 参数
        const oId = queryParams.get('orderId'); // 假设会带 orderId

        if (oId) {
          const parsedOrderId = parseInt(oId);
          setOrderId(parsedOrderId);
          // 实际应用中，这里需要向后端发起请求，查询该订单的最终支付状态
          // 因为前端的 status 参数可能不准确，后端通知才是最可靠的
          checkPaymentStatus(parsedOrderId);
        } else if (status) {
          // 如果没有 orderId 只有 status，可能需要根据业务逻辑处理
          setPaymentStatus(status === 'success' ? 'success' : 'fail');
          setLoading(false);
        } else {
          // 既没有 status 也没有 orderId，可能是一个无效的访问
          setPaymentStatus('fail');
          setLoading(false);
        }
      }, [location.search]);

      const checkPaymentStatus = async (oId: number) => {
        try {
          // 假设后端提供一个查询订单支付状态的接口
          const response = await api.get(`/orders/${oId}/payment-status`);
          if (response.data.status === 'paid' || response.data.status === 'completed') {
            setPaymentStatus('success');
          } else {
            setPaymentStatus('fail');
          }
        } catch (error) {
          console.error('查询支付状态失败:', error);
          setPaymentStatus('fail');
        } finally {
          setLoading(false);
        }
      };

      if (loading) {
        return (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" tip="正在查询支付结果..." />
          </div>
        );
      }

      if (paymentStatus === 'success') {
        return (
          <Result
            status="success"
            title="支付成功！"
            subTitle={orderId ? `订单号: ${orderId} 已成功支付。` : '您的支付已成功完成。'}
            extra={[
              <Button type="primary" key="dashboard" onClick={() => navigate('/orders')}>
                查看我的订单
              </Button>,
              orderId && <Button key="buy" onClick={() => navigate(`/order/${orderId}`)}>
                查看订单详情
              </Button>,
            ]}
          />
        );
      } else {
        return (
          <Result
            status="error"
            title="支付失败"
            subTitle={orderId ? `订单号: ${orderId} 支付失败，请重试或联系客服。` : '您的支付未能成功完成。'}
            extra={[
              orderId && <Button type="primary" key="retry" onClick={() => navigate(`/order/${orderId}`)}>
                返回订单重试
              </Button>,
              <Button key="contact" onClick={() => message.info('请联系客服支持。')}>
                联系客服
              </Button>,
            ]}
          />
        );
      }
    };

    export default PaymentResultPage;
    ```

**在 `App.tsx` 中添加路由：**

```tsx
// src/App.tsx (部分)
import PaymentResultPage from './pages/PaymentResultPage';
// ... 其他 import

function App() {
  return (
    <Router>
      <Layout className="layout">
        {/* ... Header, Content, Footer */}
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              {/* ... 其他公共路由 */}

              <Route element={<PrivateRoute />}>
                {/* ... 其他受保护的路由 */}
                <Route path="/payment/result" element={<PaymentResultPage />} /> {/* 支付结果页面 */}
              </Route>

              {/* ... 404 路由 */}
            </Routes>
          </div>
        </Content>
        {/* ... Footer */}
      </Layout>
    </Router>
  );
}

export default App;
```

---

**本篇总结：**

本篇主要从概念层面探讨了**消息通知系统**和**支付集成**的前端设计与预留：

* **消息通知：** 讨论了消息类型、通知中心 UI 设计，并重点提到了 WebSocket 实时通知的重要性，提供了一个概念性的通知列表页面。
* **支付集成：** 概述了支付流程，并预留了前端支付发起按钮和支付结果页面的基本结构，强调了与后端和第三方支付服务商的协作是关键。

这两个模块的完整实现需要后端提供强大的支持，尤其是支付涉及到严格的资金安全和复杂的协议对接。

---

至此，我们的前端基础功能和核心业务流程已经基本覆盖。后续可以根据实际需求，进一步优化 UI/UX、添加更多高级功能（如聊天、地图集成、数据统计等）。

您对消息通知或支付集成方面有什么疑问吗？或者我们已经完成了前端部分的讨论，可以转向后端部分了吗？