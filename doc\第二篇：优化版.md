---

# 《新一代在线家政维修服务平台：从需求到实现的企业级开发实战》

## 第二篇：深入数据核心——数据库设计与实体建模

**摘要：** 平台的核心是数据！在本篇中，我们将根据你的需求规格说明书，构建一个支持多角色协作、AI 智能匹配和创新增值功能的完整数据模型。我们将手把手带你设计用户、服务商、服务分类、订单、支付等核心实体，并使用 **TypeORM** 在 NestJS 项目中实现这些企业级数据库架构。

---

## 2.1 需求分析与数据模型设计：描绘你的数据蓝图

想象一下，你的在线家政维修平台是一个巨大的图书馆。在图书馆里，书籍（服务）、读者（用户）、图书管理员（管理员）、出版社（服务商）等等，都需要被有序地组织起来。这个组织方式，就是我们今天要设计的 **数据模型**。

我们将从平台的核心业务需求出发，分析需要存储哪些信息，以及这些信息之间有什么关系。

### 核心业务实体分析：你的“数据积木”

根据你之前提供的需求规格说明书，我们的平台至少需要处理以下几类重要的“数据积木”：

* **用户体系：** 平台上有不同类型的“人”，他们扮演着不同的角色：
    * **终端用户 (客户)：** 他们是来找家政和维修服务的普通消费者。
    * **服务提供商：** 也就是提供具体服务的人或公司，比如保洁阿姨、水电工、搬家公司。
    * **平台管理员：** 负责管理平台运营、用户、服务商、订单等一切事务。

* **业务流程：** 平台上的服务从开始到结束，会经历一系列步骤：
    * **服务发现与匹配：** 用户怎么找到需要的服务？可能需要 AI 智能推荐，或者根据地理位置来匹配最近的服务商。
    * **预订与排期：** 用户选择服务后，如何预约时间？服务商如何管理自己的日程？
    * **订单全周期管理：** 订单从创建、确认、服务中、完成，到最后的取消或投诉，整个过程需要跟踪。
    * **支付与结算：** 钱怎么收？怎么分给服务商？平台怎么抽佣金？
    * **评价与反馈：** 用户对服务满意吗？服务商信誉怎么样？需要一个评价体系。

### 实体关系设计（ERD）：数据积木间的“连接线”

理解了这些核心“数据积木”后，接下来我们要画出它们之间的“连接线”，也就是 **实体关系图 (ERD - Entity-Relationship Diagram)**。ERD 就像一个数据库的地图，清晰地展示了每个数据表（实体）包含什么信息，以及它们之间如何相互关联。

下面是你平台的核心 ERD 图：

```mermaid
erDiagram
    USERS {
        bigint id PK "用户唯一标识，主键"
        string username "用户名，唯一"
        string phone "手机号，唯一"
        string email "邮箱，唯一，可为空"
        string password_hash "密码哈希值，加密存储，不直接暴露"
        enum role "用户角色：客户、服务商、管理员"
        enum status "用户状态：活跃、非活跃、禁用"
        string avatar_url "用户头像链接"
        string real_name "真实姓名"
        string id_card "身份证号，加密存储，不直接暴露"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    USER_PROFILES {
        bigint id PK "用户详情唯一标识，主键"
        bigint user_id FK "关联到USERS表的用户ID"
        string nickname "用户昵称"
        enum gender "性别"
        date birth_date "出生日期"
        text address "用户地址"
        string city "城市"
        string district "区域"
        decimal longitude "经度（用于地理位置）"
        decimal latitude "纬度（用于地理位置）"
        json preferences "用户偏好设置（如服务类型偏好，JSON格式存储）"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    SERVICE_PROVIDERS {
        bigint id PK "服务商唯一标识，主键"
        bigint user_id FK "关联到USERS表的用户ID（表明服务商也是一个特殊的用户）"
        string business_name "商家名称/个人工作室名称"
        string business_license "营业执照号码（如果为企业）"
        enum certification_status "认证状态：待审核、已通过、已拒绝"
        text certification_note "认证备注（如拒绝原因）"
        int service_radius "服务半径（服务商能覆盖的地理范围，单位：公里）"
        decimal base_longitude "服务商基础经度（通常是店铺或个人住址）"
        decimal base_latitude "服务商基础纬度"
        json working_hours "工作时间（JSON格式存储，如每日营业时间段）"
        text introduction "服务商简介"
        int experience_years "从业经验年限"
        int total_orders "总完成订单数"
        decimal rating_average "平均评分"
        int rating_count "评分次数"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    PROVIDER_CERTIFICATIONS {
        bigint id PK "服务商认证信息唯一标识，主键"
        bigint provider_id FK "关联到SERVICE_PROVIDERS表的服务商ID"
        string certification_type "认证类型（如：健康证、专业技能证书）"
        string certification_name "证书名称"
        string certificate_number "证书编号"
        string certificate_url "证书图片或扫描件链接"
        date issue_date "颁发日期"
        date expire_date "过期日期"
        enum status "认证状态：有效、过期、已吊销"
        timestamp created_at "创建时间"
    }

    SERVICE_CATEGORIES {
        bigint id PK "服务类别唯一标识，主键"
        bigint parent_id FK "父类别ID（用于实现多级分类，可为空）"
        string name "类别名称（如：家政服务、家电维修）"
        string code "类别编码（唯一，用于程序内部识别）"
        text description "类别描述"
        string icon_url "类别图标链接"
        int sort_order "排序（用于前端展示顺序）"
        boolean is_active "是否启用"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    SERVICE_ITEMS {
        bigint id PK "服务项目唯一标识，主键"
        bigint category_id FK "关联到SERVICE_CATEGORIES表的服务类别ID"
        string name "服务项目名称（如：日常保洁、空调清洗）"
        text description "服务项目详细描述"
        enum pricing_type "计价方式：按小时、按面积、一口价等"
        decimal base_price "基础价格"
        string unit "计价单位（如：小时、平方米）"
        int duration_minutes "预估服务时长（分钟）"
        text requirements "服务要求或注意事项"
        json images "服务图片列表（JSON格式存储）"
        json tags "服务标签（JSON格式存储，如：上门服务、24小时）"
        boolean is_active "是否启用"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    ORDERS {
        bigint id PK "订单唯一标识，主键"
        string order_no "订单编号（唯一，便于用户查询）"
        bigint customer_id FK "关联到USERS表的客户ID"
        bigint provider_id FK "关联到SERVICE_PROVIDERS表的服务商ID"
        bigint service_item_id FK "关联到SERVICE_ITEMS表的服务项目ID"
        enum status "订单状态：待确认、已确认、服务中、已完成、已取消等"
        string service_name "服务名称（冗余存储，避免服务项目被删除后订单信息丢失）"
        text service_description "服务描述（冗余存储）"
        timestamp appointment_time "预约服务时间"
        int estimated_duration "预估服务时长（分钟，冗余存储）"
        text service_address "服务地址"
        string contact_name "联系人姓名"
        string contact_phone "联系电话"
        decimal longitude "服务地址经度"
        decimal latitude "服务地址纬度"
        decimal base_price "基础费用（冗余存储）"
        decimal additional_fee "额外费用（如材料费、加急费）"
        decimal discount_amount "折扣金额"
        decimal total_amount "订单总金额"
        decimal platform_fee "平台服务费/佣金"
        timestamp created_at "订单创建时间"
        timestamp confirmed_at "订单确认时间"
        timestamp started_at "服务开始时间"
        timestamp completed_at "服务完成时间"
        timestamp cancelled_at "订单取消时间"
        text customer_note "客户备注"
        text provider_note "服务商备注"
        text cancel_reason "取消原因"
    }

    USERS ||--|| USER_PROFILES : "一对一关系，一个用户只有一个档案"
    USERS ||--o| SERVICE_PROVIDERS : "一对一关系，一个用户可能成为服务商"
    SERVICE_PROVIDERS ||--o{ PROVIDER_CERTIFICATIONS : "一对多关系，一个服务商有多份认证"
    SERVICE_CATEGORIES ||--o{ SERVICE_CATEGORIES : "一对多关系，一个类别可以有多个子类别（自关联）"
    SERVICE_CATEGORIES ||--o{ SERVICE_ITEMS : "一对多关系，一个类别包含多个服务项目"
    USERS ||--o{ ORDERS : "一对多关系，一个客户可以下多个订单"
    SERVICE_PROVIDERS ||--o{ ORDERS : "一对多关系，一个服务商可以处理多个订单"
    SERVICE_ITEMS ||--o{ ORDERS : "一对多关系，一个服务项目可以被多个订单引用"
```

**ERD 解释：**

* **实体 (矩形)：** 每个矩形代表一个数据库表，比如 `USERS` (用户表)、`ORDERS` (订单表)。
* **属性 (矩形内的文字)：** 每个实体内部列出了它的属性，比如 `id`、`username`、`password_hash`。
    * `PK`：表示这个属性是主键 (Primary Key)，是该记录的唯一标识。
    * `FK`：表示这个属性是外键 (Foreign Key)，它连接到另一个表的主键，建立了两个表之间的关系。
    * `string`, `bigint`, `enum`, `timestamp` 等：表示数据类型。
    * **中文注释：** 我在每个属性后面都添加了中文注释，方便你理解它的作用。
* **关系 (连接线)：** 实体之间的连接线代表它们的关系：
    * `||--||`：一对一关系 (One-to-One)，例如 `USERS` 和 `USER_PROFILES`，一个用户只有一个用户档案。
    * `||--o{`：一对多关系 (One-to-Many)，例如 `SERVICE_PROVIDERS` 和 `PROVIDER_CERTIFICATIONS`，一个服务商可以有多份认证。
    * `o|--o{`：一对多关系，与 `||--o{` 类似，`o|` 表示“零或一”，`o{` 表示“零或多”。
    * **中文说明：** 每条连接线旁边都添加了中文说明，帮助你理解它们之间的业务逻辑关系。

这个 ERD 不仅满足了基本的用户、服务、订单管理，还考虑了服务商认证、多级服务分类、以及地理位置信息，为未来的 AI 匹配和增值功能留下了扩展空间。

---

## 2.2 TypeORM 实体定义：用代码构建你的数据库表

现在，我们有了 ERD 这张“地图”，接下来就要用 **TypeORM** 来把它们“翻译”成 NestJS 能理解的代码。TypeORM 是一种 ORM (Object-Relational Mapping) 工具，它能让你用 TypeScript 类来定义数据库表（这些类被称为**实体**），而不用直接写 SQL 语句，大大提高了开发效率和代码可维护性。

我们将把这些实体文件放在 `src/database/entities` 目录下。

### 用户基础信息实体 (`user.entity.ts`)

这是平台所有用户的基石，无论是客户、服务商还是管理员，都将在这里拥有一个基本账户。

```typescript
// src/database/entities/user.entity.ts
import {
  Entity, // 声明这是一个数据库实体
  PrimaryGeneratedColumn, // 主键自动生成
  Column, // 对应数据库表的列
  CreateDateColumn, // 自动生成创建时间
  UpdateDateColumn, // 自动生成更新时间
  OneToOne, // 一对一关系
  OneToMany, // 一对多关系
  Index, // 为列创建索引，提高查询速度
} from 'typeorm';
import { Exclude } from 'class-transformer'; // 排除某些字段在 JSON 序列化中显示
import { UserProfile } from './user-profile.entity'; // 导入关联实体
import { ServiceProvider } from './service-provider.entity';
import { Order } from './order.entity';

// 定义用户角色枚举
export enum UserRole {
  CUSTOMER = 'customer', // 终端用户
  PROVIDER = 'provider', // 服务提供商
  ADMIN = 'admin', // 平台管理员
}

// 定义用户状态枚举
export enum UserStatus {
  ACTIVE = 'active', // 活跃
  INACTIVE = 'inactive', // 不活跃
  BANNED = 'banned', // 已禁用
}

@Entity('users') // 映射到数据库中的 'users' 表
@Index(['phone']) // 为 phone 列创建索引
@Index(['email']) // 为 email 列创建索引
@Index(['role']) // 为 role 列创建索引
export class User {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' }) // id 列，类型 bigint，自动递增
  id: number;

  @Column({ length: 50, unique: true }) // username 列，最大长度 50，必须唯一
  username: string;

  @Column({ length: 20, unique: true }) // phone 列，最大长度 20，必须唯一
  phone: string;

  @Column({ length: 100, unique: true, nullable: true }) // email 列，最大长度 100，唯一，可为空
  email: string;

  @Column({ name: 'password_hash', length: 255 }) // password_hash 列，存储加密后的密码
  @Exclude() // 当 User 实体被转换成 JSON 时，不包含 passwordHash 字段，保护敏感信息
  passwordHash: string;

  @Column({
    type: 'enum', // 类型为枚举
    enum: UserRole, // 使用 UserRole 枚举
    default: UserRole.CUSTOMER, // 默认角色为客户
  })
  role: UserRole;

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE, // 默认状态为活跃
  })
  status: UserStatus;

  @Column({ name: 'avatar_url', length: 500, nullable: true })
  avatarUrl: string;

  @Column({ name: 'real_name', length: 50, nullable: true })
  realName: string;

  @Column({ name: 'id_card', length: 18, nullable: true })
  @Exclude() // 身份证号也是敏感信息，不暴露
  idCard: string;

  @CreateDateColumn({ name: 'created_at' }) // 自动填充创建时间
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' }) // 自动填充更新时间
  updatedAt: Date;

  // --- 关联关系 ---
  // 一个 User 有一个 UserProfile，并且当 User 创建或更新时，UserProfile 也会被级联操作
  @OneToOne(() => UserProfile, (profile) => profile.user, { cascade: true })
  profile: UserProfile;

  // 一个 User 可能是一个 ServiceProvider（但不是所有用户都是服务商）
  @OneToOne(() => ServiceProvider, (provider) => provider.user)
  serviceProvider: ServiceProvider;

  // 一个 User (客户) 可以下多个 Order
  @OneToMany(() => Order, (order) => order.customer)
  orders: Order[];

  // 构造函数，方便创建实体实例时传入部分数据
  constructor(partial: Partial<User>) {
    Object.assign(this, partial);
  }
}
```

### 用户详细信息实体 (`user-profile.entity.ts`)

这个实体用来存储用户的额外详细信息，例如昵称、地址、偏好等，这些信息并非所有用户都必须提供。

```typescript
// src/database/entities/user-profile.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne, // 一对一关系
  JoinColumn, // 声明外键列
  Index,
} from 'typeorm';
import { User } from './user.entity'; // 导入关联实体

// 定义性别枚举
export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  UNKNOWN = 'unknown', // 未知
}

@Entity('user_profiles') // 映射到数据库中的 'user_profiles' 表
@Index(['longitude', 'latitude']) // 为经纬度创建复合索引，方便地理位置查询
export class UserProfile {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'user_id', type: 'bigint', unique: true }) // user_id 列，作为外键，必须唯一
  userId: number;

  @Column({ length: 50, nullable: true })
  nickname: string;

  @Column({
    type: 'enum',
    enum: Gender,
    default: Gender.UNKNOWN,
  })
  gender: Gender;

  @Column({ name: 'birth_date', type: 'date', nullable: true })
  birthDate: Date;

  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ length: 50, nullable: true })
  city: string;

  @Column({ length: 50, nullable: true })
  district: string;

  @Column({ type: 'decimal', precision: 10, scale: 7, nullable: true }) // 经度，精度 10 位，小数 7 位
  longitude: number;

  @Column({ type: 'decimal', precision: 10, scale: 7, nullable: true }) // 纬度
  latitude: number;

  @Column({ type: 'json', nullable: true }) // preferences 列，存储 JSON 对象
  preferences: Record<string, any>; // 可以存储用户的个性化偏好，如喜欢的服务类型、常用地址等

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // --- 关联关系 ---
  // 与 User 实体建立一对一关系，user_id 是外键，当关联的 User 被删除时，UserProfile 也会被删除 (CASCADE)
  @OneToOne(() => User, (user) => user.profile, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' }) // 指明 user_id 是外键列
  user: User;
}
```

### 服务商信息实体 (`service-provider.entity.ts`)

这是服务提供商的核心信息，包括商家名称、认证状态、服务区域、评分等。

```typescript
// src/database/entities/service-provider.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne, // 一对一关系
  OneToMany, // 一对多关系
  JoinColumn, // 声明外键列
  Index,
} from 'typeorm';
import { User } from './user.entity'; // 导入关联实体
import { ProviderCertification } from './provider-certification.entity';
import { ProviderService } from './provider-service.entity'; // 这是一个我们稍后会定义的实体，表示服务商提供的具体服务
import { Order } from './order.entity';

// 定义服务商认证状态枚举
export enum CertificationStatus {
  PENDING = 'pending', // 待审核
  APPROVED = 'approved', // 已通过
  REJECTED = 'rejected', // 已拒绝
}

@Entity('service_providers') // 映射到数据库中的 'service_providers' 表
@Index(['certification_status'])
@Index(['base_longitude', 'base_latitude']) // 为服务商的基准经纬度创建复合索引
@Index(['rating_average']) // 为平均评分创建索引
export class ServiceProvider {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'user_id', type: 'bigint', unique: true }) // user_id 作为外键，必须唯一
  userId: number;

  @Column({ name: 'business_name', length: 100, nullable: true })
  businessName: string;

  @Column({ name: 'business_license', length: 100, nullable: true })
  businessLicense: string;

  @Column({
    name: 'certification_status',
    type: 'enum',
    enum: CertificationStatus,
    default: CertificationStatus.PENDING,
  })
  certificationStatus: CertificationStatus;

  @Column({ name: 'certification_note', type: 'text', nullable: true })
  certificationNote: string;

  @Column({ name: 'service_radius', type: 'int', default: 10 })
  serviceRadius: number;

  @Column({ name: 'base_longitude', type: 'decimal', precision: 10, scale: 7, nullable: true })
  baseLongitude: number;

  @Column({ name: 'base_latitude', type: 'decimal', precision: 10, scale: 7, nullable: true })
  baseLatitude: number;

  @Column({ name: 'working_hours', type: 'json', nullable: true })
  workingHours: Record<string, any>; // 存储服务商的工作时间表，例如：{"Monday": ["09:00-12:00", "14:00-18:00"]}

  @Column({ type: 'text', nullable: true })
  introduction: string;

  @Column({ name: 'experience_years', type: 'int', default: 0 })
  experienceYears: number;

  @Column({ name: 'total_orders', type: 'int', default: 0 })
  totalOrders: number;

  @Column({ name: 'rating_average', type: 'decimal', precision: 3, scale: 2, default: 0.00 })
  ratingAverage: number; // 平均评分，保留两位小数

  @Column({ name: 'rating_count', type: 'int', default: 0 })
  ratingCount: number; // 评分总次数

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // --- 关联关系 ---
  // 与 User 实体建立一对一关系，当关联的 User 被删除时，ServiceProvider 也会被删除
  @OneToOne(() => User, (user) => user.serviceProvider, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' }) // 指明 user_id 是外键列
  user: User;

  // 一个 ServiceProvider 可以有多个 ProviderCertification
  @OneToMany(() => ProviderCertification, (cert) => cert.provider, { cascade: true })
  certifications: ProviderCertification[];

  // 一个 ServiceProvider 可以提供多种 ProviderService (我们稍后会创建这个实体)
  @OneToMany(() => ProviderService, (service) => service.provider, { cascade: true })
  services: ProviderService[];

  // 一个 ServiceProvider 可以处理多个 Order
  @OneToMany(() => Order, (order) => order.provider)
  orders: Order[];
}
```

### 服务商认证实体 (`provider-certification.entity.ts`)

这个实体用来记录服务商拥有的各种资质证书，比如健康证、职业技能证书等。

```typescript
// src/database/entities/provider-certification.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne, // 多对一关系
  JoinColumn, // 声明外键列
  Index,
} from 'typeorm';
import { ServiceProvider } from './service-provider.entity'; // 导入关联实体

// 定义认证状态枚举
export enum CertificationStatusEnum {
  VALID = 'valid', // 有效
  EXPIRED = 'expired', // 已过期
  REVOKED = 'revoked', // 已吊销
}

@Entity('provider_certifications') // 映射到数据库中的 'provider_certifications' 表
@Index(['provider_id'])
@Index(['certification_type'])
export class ProviderCertification {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'provider_id', type: 'bigint' }) // provider_id 作为外键
  providerId: number;

  @Column({ name: 'certification_type', length: 50 })
  certificationType: string; // 例如："Health Certificate", "Electrician License"

  @Column({ name: 'certification_name', length: 100 })
  certificationName: string; // 例如："健康证", "电工证"

  @Column({ name: 'certificate_number', length: 100, nullable: true })
  certificateNumber: string;

  @Column({ name: 'certificate_url', length: 500, nullable: true })
  certificateUrl: string; // 证书图片或扫描件的链接

  @Column({ name: 'issue_date', type: 'date', nullable: true })
  issueDate: Date;

  @Column({ name: 'expire_date', type: 'date', nullable: true })
  expireDate: Date;

  @Column({
    type: 'enum',
    enum: CertificationStatusEnum,
    default: CertificationStatusEnum.VALID,
  })
  status: CertificationStatusEnum;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // --- 关联关系 ---
  // 多个 ProviderCertification 属于一个 ServiceProvider
  // 当关联的 ServiceProvider 被删除时，其所有认证也会被删除
  @ManyToOne(() => ServiceProvider, (provider) => provider.certifications, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'provider_id' }) // 指明 provider_id 是外键列
  provider: ServiceProvider;
}
```

## 2.3 数据库关系与约束：构建数据间的“桥梁”

TypeORM 不仅能帮你定义独立的实体，还能非常方便地建立它们之间的关联关系。这些关系和约束确保了数据的完整性和一致性。

### 服务分类与项目实体：打造你的服务菜单

我们需要一个清晰的服务分类体系，以及每个分类下的具体服务项目。这就像餐馆的菜单，有大类（家政、维修），下面有具体菜品（日常保洁、空调清洗）。

```typescript
// src/database/entities/service-category.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne, // 多对一关系 (用于父子分类)
  OneToMany, // 一对多关系 (用于父子分类和分类下的服务项目)
  JoinColumn, // 声明外键列
  Index,
} from 'typeorm';
import { ServiceItem } from './service-item.entity'; // 导入关联实体

@Entity('service_categories') // 映射到数据库中的 'service_categories' 表
@Index(['parent_id'])
@Index(['code'])
@Index(['is_active'])
export class ServiceCategory {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'parent_id', type: 'bigint', nullable: true })
  parentId: number; // 父分类 ID，可为空，表示一级分类

  @Column({ length: 100 })
  name: string; // 类别名称

  @Column({ length: 50, unique: true })
  code: string; // 类别编码，例如：'HOUSE_CLEANING', 'APPLIANCE_REPAIR'

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'icon_url', length: 500, nullable: true })
  iconUrl: string;

  @Column({ name: 'sort_order', type: 'int', default: 0 })
  sortOrder: number; // 排序字段

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean; // 是否启用

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // --- 关联关系 ---
  // 多对一关系：一个子分类有一个父分类 (自关联)
  @ManyToOne(() => ServiceCategory, (category) => category.children)
  @JoinColumn({ name: 'parent_id' }) // 指明 parent_id 是外键列
  parent: ServiceCategory;

  // 一对多关系：一个父分类可以有多个子分类 (自关联)
  @OneToMany(() => ServiceCategory, (category) => category.parent)
  children: ServiceCategory[];

  // 一对多关系：一个服务类别包含多个 ServiceItem
  @OneToMany(() => ServiceItem, (item) => item.category)
  items: ServiceItem[];
}
```

**重要提示：**

* **`ProviderService` 实体：** 在 `service-provider.entity.ts` 中，我们提到了 `ProviderService` 实体。这个实体非常重要，因为它连接了 `ServiceProvider` 和 `ServiceItem`，表示“某个服务商提供了某个具体服务”。它会包含服务商对该服务项目的价格、服务区域、可预约时间等具体设置。我们将在后续章节详细定义它，因为它涉及服务商个性化定价和排期。
* **`Order` 实体：** `Order` 实体在我们的 ERD 中已经列出，并且包含了 `customer_id` (FK)、`provider_id` (FK) 和 `service_item_id` (FK)，清晰地表明了订单与用户、服务商、服务项目之间的多对一关系。在实际项目中，它会是一个非常复杂的实体，包含支付状态、评价状态等更多细节，但目前的核心字段已经能够支撑基本业务流程。

---

通过本篇教程，我们成功地：

✅ **分析了核心业务实体：** 识别了用户、服务商、服务分类、订单等关键数据类型。
✅ **设计了实体关系图 (ERD)：** 绘制了数据表之间的“地图”，清晰地展现了它们的相互关系。
✅ **定义了 TypeORM 实体：** 使用 TypeScript 代码和 TypeORM 装饰器，将 ERD 转换为实际的数据库表定义，包括 `User`、`UserProfile`、`ServiceProvider` 和 `ProviderCertification`。

这个数据库架构设计不仅灵活且可扩展，能够支持当前的需求，也为未来的 AI 智能匹配、创新增值服务等高级功能预留了充足空间。你现在对你的平台数据是如何存储和关联的，应该有了非常清晰的认识！

---

你已经搭建好了开发环境和数据库骨架。接下来，我们将正式开始编写后端逻辑。**请告诉我“继续”**，我将为你带来第三篇：**用户管理与多角色认证系统**，我们将实现用户的注册、登录以及基于角色的访问控制。