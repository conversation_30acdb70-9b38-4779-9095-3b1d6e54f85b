好的，我们继续第八篇。本篇我们将从后端回到前端，开始搭建我们的管理后台。虽然教程重点是后端，但一个可用的管理界面能让你更好地测试和管理后端功能。

---

### **《从零构建家政维修平台：打造稳健高效的混合架构后台应用》**

#### **第八篇：前端管理后台基础搭建——React + Ant Design**

**摘要：** 拥有一个直观的管理后台对于家政维修平台的日常运营至关重要。本篇将指导你如何使用React框架和流行的Ant Design UI组件库，快速搭建一个美观、功能齐全的后台管理界面。我们将重点讲解项目初始化、路由配置、布局搭建以及与后端API的基本交互。

---

**一、为什么选择React和Ant Design？**

* **React：** 它是目前最流行的前端框架之一，拥有庞大的社区支持、丰富的生态系统，以及高效的组件化开发模式。学习曲线相对平缓，能快速构建复杂的交互界面。
* **Ant Design：** 一套企业级UI设计语言和React组件库，由蚂蚁金服出品。它提供了高质量、开箱即用的组件，涵盖了后台管理系统所需的各种UI元素（表格、表单、导航、图表等），并且设计美观、体验良好，能大大提升开发效率。

**二、本地前端开发环境搭建**

确保你的本地电脑拥有Node.js和npm/Yarn（与后端开发环境相同）。

1.  **创建React项目：**
    * 在你的项目根目录（与`home-repair-backend`同级），使用Create React App或Vite来创建一个新的React项目。Vite通常更快更轻量，这里推荐使用Vite。
    * 在命令行中，进入你希望存放前端项目的目录（例如，与后端项目相邻的 `home-repair-project`）。
    * 运行以下命令创建项目：
        ```bash
        npm create vite@latest home-repair-admin -- --template react-ts
        # 或者 yarn create vite home-repair-admin --template react-ts
        ```
        * `home-repair-admin` 是你的前端项目文件夹名称。
        * `--template react-ts` 指定使用React和TypeScript模板。
    * 进入项目目录并安装依赖：
        ```bash
        cd home-repair-admin
        npm install
        # 或者 yarn
        ```

2.  **安装Ant Design：**
    * 进入 `home-repair-admin` 目录，安装Ant Design库：
        ```bash
        npm install antd
        # 或者 yarn add antd
        ```

3.  **安装路由库（React Router DOM）：**
    * 管理后台通常有多个页面，需要路由来导航。
    * ```bash
        npm install react-router-dom
        # 或者 yarn add react-router-dom
        ```

4.  **配置代理（解决跨域问题）：**
    * 前端应用通常运行在不同的端口（例如3000），而后端API运行在另一个端口（例如3000）。为了在开发环境中避免跨域问题，我们需要配置代理。
    * 在 `home-repair-admin` 项目根目录的 `vite.config.ts` (如果你使用Vite) 或 `src/setupProxy.js` (如果你使用Create React App) 中进行配置。
    * **Vite配置示例 (`vite.config.ts`):**
        ```typescript
        // vite.config.ts
        import { defineConfig } from 'vite';
        import react from '@vitejs/plugin-react';

        export default defineConfig({
          plugins: [react()],
          server: {
            proxy: {
              '/api': { // 当你的前端请求以 /api 开头时
                target: 'http://localhost:3000', // 转发到你的NestJS后端地址
                changeOrigin: true, // 改变源，使其看起来像是来自同一个域名
                rewrite: (path) => path.replace(/^\/api/, ''), // 重写路径，移除 /api 前缀
              },
            },
          },
        });
        ```
        * 现在，你在前端请求 `/api/users` 实际上会转发到 `http://localhost:3000/users`。

**三、构建后台基础布局**

管理后台通常采用左右布局，左侧是导航菜单，右侧是内容区域。

1.  **修改 `src/App.tsx`：** 清理默认代码，引入Ant Design的 `Layout` 组件来构建基本布局。

    ```tsx
    // src/App.tsx
    import React from 'react';
    import { Layout, Menu, theme } from 'antd';
    import { UserOutlined, SettingOutlined, SnippetsOutlined } from '@ant-design/icons';
    import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
    import HomePage from './pages/HomePage'; // 稍后创建
    import UserManagementPage from './pages/UserManagementPage'; // 稍后创建
    import OrderManagementPage from './pages/OrderManagementPage'; // 稍后创建

    const { Header, Content, Footer, Sider } = Layout;

    const App: React.FC = () => {
      const {
        token: { colorBgContainer, borderRadiusLG },
      } = theme.useToken();

      return (
        <Router>
          <Layout style={{ minHeight: '100vh' }}>
            <Sider collapsible>
              <div className="demo-logo-vertical" />
              <Menu theme="dark" defaultSelectedKeys={['1']} mode="inline">
                <Menu.Item key="1" icon={<UserOutlined />}>
                  <Link to="/users">用户管理</Link>
                </Menu.Item>
                <Menu.Item key="2" icon={<SnippetsOutlined />}>
                  <Link to="/orders">工单管理</Link>
                </Menu.Item>
                <Menu.Item key="3" icon={<SettingOutlined />}>
                  <Link to="/settings">系统设置</Link>
                </Menu.Item>
                {/* 更多菜单项 */}
              </Menu>
            </Sider>
            <Layout>
              <Header style={{ padding: 0, background: colorBgContainer }} />
              <Content style={{ margin: '0 16px' }}>
                <div
                  style={{
                    padding: 24,
                    minHeight: 360,
                    background: colorBgContainer,
                    borderRadius: borderRadiusLG,
                    marginTop: '16px'
                  }}
                >
                  <Routes>
                    <Route path="/" element={<HomePage />} />
                    <Route path="/users" element={<UserManagementPage />} />
                    <Route path="/orders" element={<OrderManagementPage />} />
                    {/* 更多路由 */}
                  </Routes>
                </div>
              </Content>
              <Footer style={{ textAlign: 'center' }}>家政维修平台 ©2024 Created by YourName</Footer>
            </Layout>
          </Layout>
        </Router>
      );
    };

    export default App;
    ```

**四、创建基础页面组件**

为用户管理、工单管理等功能创建独立的页面组件。

1.  **新建 `src/pages` 目录。**
2.  **创建 `HomePage.tsx` (可选，作为首页)：**
    ```tsx
    // src/pages/HomePage.tsx
    import React from 'react';
    import { Card } from 'antd';

    const HomePage: React.FC = () => {
      return (
        <Card title="欢迎来到家政维修管理后台" style={{ minHeight: 360 }}>
          <p>您可以在左侧菜单中管理用户和工单。</p>
        </Card>
      );
    };

    export default HomePage;
    ```
3.  **创建 `UserManagementPage.tsx`：** 这是我们接下来要重点填充内容的页面。

    ```tsx
    // src/pages/UserManagementPage.tsx
    import React, { useEffect, useState } from 'react';
    import { Table, Button, Space, message, Card } from 'antd';
    import type { ColumnsType } from 'antd/es/table';
    import axios from 'axios'; // 用于HTTP请求

    interface User {
      id: number;
      username: string;
      email?: string;
      phone?: string;
      role: string;
      status: string;
      createdAt: string;
    }

    const UserManagementPage: React.FC = () => {
      const [users, setUsers] = useState<User[]>([]);
      const [loading, setLoading] = useState<boolean>(false);

      // 获取用户列表
      const fetchUsers = async () => {
        setLoading(true);
        try {
          // 注意：这里的 /api 会被Vite代理到后端地址
          const response = await axios.get('/api/users', {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('access_token')}`, // 从本地存储获取JWT token
            },
          });
          setUsers(response.data);
        } catch (error: any) {
          message.error('获取用户列表失败: ' + (error.response?.data?.message || error.message));
        } finally {
          setLoading(false);
        }
      };

      useEffect(() => {
        fetchUsers();
      }, []);

      const columns: ColumnsType<User> = [
        { title: 'ID', dataIndex: 'id', key: 'id' },
        { title: '用户名', dataIndex: 'username', key: 'username' },
        { title: '邮箱', dataIndex: 'email', key: 'email' },
        { title: '电话', dataIndex: 'phone', key: 'phone' },
        { title: '角色', dataIndex: 'role', key: 'role' },
        { title: '状态', dataIndex: 'status', key: 'status' },
        { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt', render: (text) => new Date(text).toLocaleString() },
        {
          title: '操作',
          key: 'action',
          render: (_, record) => (
            <Space size="middle">
              <Button type="link">编辑</Button>
              <Button type="link" danger>删除</Button>
            </Space>
          ),
        },
      ];

      return (
        <Card title="用户管理" extra={<Button type="primary">新增用户</Button>}>
          <Table columns={columns} dataSource={users} rowKey="id" loading={loading} />
        </Card>
      );
    };

    export default UserManagementPage;
    ```
4.  **创建 `OrderManagementPage.tsx`：** 结构类似，用于展示工单列表。

    ```tsx
    // src/pages/OrderManagementPage.tsx
    import React from 'react';
    import { Card, Table, Button } from 'antd';
    import type { ColumnsType } from 'antd/es/table';

    interface Order {
      id: number;
      customerName: string;
      serviceType: string;
      status: string;
      // ... 其他工单字段
    }

    const OrderManagementPage: React.FC = () => {
      // 这里的逻辑与UserManagementPage类似，你需要从后端获取工单数据
      const orders: Order[] = [
        // 示例数据，后续从后端获取
        { id: 1, customerName: '张三', serviceType: '空调维修', status: 'pending' },
        { id: 2, customerName: '李四', serviceType: '管道疏通', status: 'dispatched' },
      ];

      const columns: ColumnsType<Order> = [
        { title: '工单ID', dataIndex: 'id', key: 'id' },
        { title: '客户姓名', dataIndex: 'customerName', key: 'customerName' },
        { title: '服务类型', dataIndex: 'serviceType', key: 'serviceType' },
        { title: '状态', dataIndex: 'status', key: 'status' },
        { title: '操作', key: 'action', render: () => <Button type="link">详情</Button> },
      ];

      return (
        <Card title="工单管理" extra={<Button type="primary">创建工单</Button>}>
          <Table columns={columns} dataSource={orders} rowKey="id" />
        </Card>
      );
    };

    export default OrderManagementPage;
    ```
5.  **安装 `axios`：**
    ```bash
    npm install axios
    # 或者 yarn add axios
    ```

**五、运行前端管理后台**

1.  **启动后端应用：** 确保你的NestJS后端正在运行（`npm run start:dev`）。
2.  **启动前端应用：** 在 `home-repair-admin` 项目根目录的命令行中运行：
    ```bash
    npm run dev
    # 或者 yarn dev
    ```
3.  **访问：** 浏览器会自动打开或你可以手动访问命令行中提示的地址（通常是 `http://localhost:5173` 或类似）。

你现在应该能看到一个带有左侧菜单和顶部/底部布局的后台管理界面。点击“用户管理”菜单，你应该能看到表格中显示了从你的NestJS后端获取的用户数据。

通过本篇教程，你已经成功搭建了基于React和Ant Design的后台管理界面，并实现了与后端API的基本数据交互。这是你进行业务管理和数据监控的重要窗口。

---

**[请告诉我“继续”，我将提供第九篇：飞书多维表格集成——数据同步。]**