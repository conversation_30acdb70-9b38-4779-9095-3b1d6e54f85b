好的，我们继续进行前端部分的**第五篇：技师端功能与评价体系**。

在本篇中，我们将完善技师的核心工作流程，并引入用户评价技师的功能。

---

## 第五篇：技师端功能与评价体系

### 5.1 技师个人中心

技师需要一个个人中心页面来查看自己的信息、管理技能、查看收入等。

#### 5.1.1 技师个人中心页面 (`src/pages/TechnicianDashboardPage.tsx`)

```tsx
// src/pages/TechnicianDashboardPage.tsx
import React, { useState, useEffect } from 'react';
import { Card, Avatar, Typography, Spin, Row, Col, Statistic, Tag, message, Button, Modal, Form, Input, Select } from 'antd';
import { UserOutlined, PhoneOutlined, MailOutlined, HomeOutlined, StarOutlined, DollarOutlined, ToolOutlined } from '@ant-design/icons';
import api from '../services/api';
import { useUser } from '../contexts/UserContext';
import { useNavigate } from 'react-router-dom';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

interface TechnicianProfile {
  id: number;
  username: string;
  phoneNumber: string;
  email?: string;
  avatarUrl?: string;
  description?: string; // 技师自我介绍
  rating: number; // 平均评分
  completedOrders: number; // 完成订单数
  specialties: string[]; // 擅长技能
  // ... 其他技师信息，如服务区域等
}

interface ServiceCategory {
  id: number;
  name: string;
}

const TechnicianDashboardPage: React.FC = () => {
  const [profile, setProfile] = useState<TechnicianProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [form] = Form.useForm();
  const [categories, setCategories] = useState<ServiceCategory[]>([]);

  const { user } = useUser();
  const navigate = useNavigate();

  useEffect(() => {
    if (!user || user.role !== 'technician') {
      message.error('您没有权限访问此页面！');
      navigate('/dashboard');
      return;
    }
    fetchTechnicianProfile();
    fetchServiceCategories();
  }, [user, navigate]);

  const fetchTechnicianProfile = async () => {
    setLoading(true);
    try {
      // 假设后端技师个人信息 API 为 /technicians/profile
      const response = await api.get('/technicians/profile');
      setProfile(response.data);
      form.setFieldsValue(response.data); // 将获取到的数据填充到表单
    } catch (error) {
      console.error('获取技师个人信息失败:', error);
      message.error('加载技师信息失败。');
    } finally {
      setLoading(false);
    }
  };

  const fetchServiceCategories = async () => {
    try {
      const response = await api.get('/service-categories'); // 假设后端服务类别API
      setCategories(response.data);
    } catch (error) {
      console.error('获取服务类别失败:', error);
    }
  };

  const handleEditProfile = () => {
    setIsEditingProfile(true);
  };

  const handleUpdateProfile = async (values: any) => {
    setLoading(true);
    try {
      await api.put('/technicians/profile', values); // 假设后端更新技师信息 API
      message.success('个人信息更新成功！');
      setIsEditingProfile(false);
      fetchTechnicianProfile(); // 重新加载最新数据
    } catch (error) {
      console.error('更新技师信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading || !profile) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}><Spin size="large" tip="加载技师信息..." /></div>
    );
  }

  return (
    <Card title="技师个人中心" style={{ maxWidth: 800, margin: '20px auto' }}>
      <Row gutter={[16, 16]} justify="center">
        <Col span={24} style={{ textAlign: 'center' }}>
          <Avatar size={100} src={profile.avatarUrl} icon={<UserOutlined />} />
          <Title level={3} style={{ marginTop: 10 }}>{profile.username}</Title>
          <Text type="secondary"><PhoneOutlined /> {profile.phoneNumber}</Text>
          {profile.email && <Text type="secondary" style={{ marginLeft: 20 }}><MailOutlined /> {profile.email}</Text>}
        </Col>

        <Col span={24}>
          <Divider />
          <Paragraph>
            <Text strong>自我介绍:</Text> {profile.description || '暂无介绍。'}
          </Paragraph>
        </Col>

        <Col span={24}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <Statistic title="平均评分" value={profile.rating} precision={1} prefix={<StarOutlined style={{ color: '#fadb14' }} />} />
            </Col>
            <Col xs={24} sm={8}>
              <Statistic title="完成订单数" value={profile.completedOrders} prefix={<DollarOutlined />} />
            </Col>
            <Col xs={24} sm={8}>
              <Statistic title="擅长技能" value={profile.specialties.length > 0 ? profile.specialties.join(', ') : '暂无'} prefix={<ToolOutlined />} />
            </Col>
          </Row>
        </Col>

        <Col span={24} style={{ textAlign: 'center', marginTop: 20 }}>
            <Button type="primary" onClick={handleEditProfile}>编辑个人信息</Button>
            <Button style={{ marginLeft: 10 }} onClick={() => navigate('/technician/tasks')}>查看我的任务</Button>
        </Col>
      </Row>

      <Modal
        title="编辑个人信息"
        open={isEditingProfile}
        onCancel={() => {
          setIsEditingProfile(false);
          form.resetFields(); // 关闭时重置表单
        }}
        footer={null} // 隐藏默认的footer，自定义按钮
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpdateProfile}
          initialValues={profile} // 初始化表单数据
        >
          <Form.Item name="username" label="用户名" rules={[{ required: true, message: '请输入用户名！' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="phoneNumber" label="手机号" rules={[{ required: true, message: '请输入手机号！' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="email" label="邮箱">
            <Input />
          </Form.Item>
          <Form.Item name="description" label="自我介绍">
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item name="specialties" label="擅长技能">
            <Select
              mode="multiple"
              allowClear
              placeholder="选择您的擅长技能"
              optionFilterProp="children"
            >
              {categories.map(cat => (
                <Option key={cat.id} value={cat.name}>{cat.name}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button onClick={() => setIsEditingProfile(false)}>取消</Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default TechnicianDashboardPage;
```

**在 `App.tsx` 中添加路由：**

```tsx
// src/App.tsx (部分)
import TechnicianDashboardPage from './pages/TechnicianDashboardPage';
// ... 其他 import

function App() {
  return (
    <Router>
      <Layout className="layout">
        {/* ... Header, Content, Footer */}
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              {/* ... 其他公共路由 */}

              <Route element={<PrivateRoute allowedRoles={['technician']} />}>
                <Route path="/technician/dashboard" element={<TechnicianDashboardPage />} /> {/* 技师个人中心 */}
                {/* ... 其他技师路由 */}
              </Route>

              {/* ... 404 路由 */}
            </Routes>
          </div>
        </Content>
        {/* ... Footer */}
      </Layout>
    </Router>
  );
}

export default App;
```

### 5.2 评价体系

用户在订单完成后可以对技师的服务进行评价，包括星级评分和文字评论。

#### 5.2.1 评价页面 (`src/pages/OrderReviewPage.tsx`)

```tsx
// src/pages/OrderReviewPage.tsx
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Typography, Spin, Form, Input, Button, Rate, message, Descriptions } from 'antd';
import api from '../services/api';
import moment from 'moment';

const { Title, Paragraph, Text } = Typography;
const { TextArea } = Input;

interface OrderDetailForReview {
  id: number;
  orderNumber: string;
  serviceName: string;
  technician?: {
    id: number;
    username: string;
    avatarUrl?: string;
  };
  completedAt: string;
  // ... 其他评价需要的订单信息
}

const OrderReviewPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [order, setOrder] = useState<OrderDetailForReview | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (id) {
      fetchOrderDetailsForReview(parseInt(id));
    }
  }, [id]);

  const fetchOrderDetailsForReview = async (orderId: number) => {
    setLoading(true);
    try {
      // 假设后端提供一个接口来获取用于评价的订单信息，确保订单状态为 'completed'
      const response = await api.get(`/orders/${orderId}/review-info`);
      if (response.data.status !== 'completed') {
        message.warning('该订单当前状态不允许评价。');
        navigate(`/order/${orderId}`); // 跳转回订单详情
        return;
      }
      setOrder(response.data);
    } catch (error) {
      console.error('获取订单评价信息失败:', error);
      message.error('加载订单评价信息失败。');
      navigate('/orders');
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values: any) => {
    setSubmitting(true);
    try {
      // 假设后端评价接口为 /reviews/order/:orderId
      await api.post(`/reviews/order/${id}`, values);
      message.success('评价提交成功！感谢您的宝贵意见。');
      navigate(`/order/${id}`); // 评价成功后跳转回订单详情页
    } catch (error) {
      console.error('提交评价失败:', error);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading || !order) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}><Spin size="large" tip="加载评价信息..." /></div>
    );
  }

  return (
    <Card title={`评价订单：${order.orderNumber}`} style={{ maxWidth: 700, margin: '20px auto' }}>
      <Paragraph>
        请您对本次订单服务进行评价，您的反馈对我们很重要。
      </Paragraph>

      <Descriptions bordered column={1} size="small" style={{ marginBottom: 20 }}>
        <Descriptions.Item label="服务名称">{order.serviceName}</Descriptions.Item>
        {order.technician && (
          <Descriptions.Item label="服务技师">
            <Avatar size="small" src={order.technician.avatarUrl} icon={<UserOutlined />} style={{ marginRight: 8 }} />
            {order.technician.username}
          </Descriptions.Item>
        )}
        <Descriptions.Item label="完成时间">{moment(order.completedAt).format('YYYY-MM-DD HH:mm')}</Descriptions.Item>
      </Descriptions>

      <Form
        form={form}
        name="review_form"
        onFinish={onFinish}
        layout="vertical"
      >
        <Form.Item
          name="rating"
          label="服务评分"
          rules={[{ required: true, message: '请给出您的评分！' }]}
        >
          <Rate allowHalf defaultValue={5} />
        </Form.Item>

        <Form.Item
          name="comment"
          label="评论内容 (可选)"
        >
          <TextArea rows={4} placeholder="请分享您的服务体验..." />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={submitting} block>
            提交评价
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default OrderReviewPage;
```

**在 `App.tsx` 中添加路由：**

```tsx
// src/App.tsx (部分)
import OrderReviewPage from './pages/OrderReviewPage'; // 引入评价页面
// ... 其他 import

function App() {
  return (
    <Router>
      <Layout className="layout">
        {/* ... Header, Content, Footer */}
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              {/* ... 其他公共路由 */}

              <Route element={<PrivateRoute />}>
                {/* ... 其他受保护的用户路由 */}
                <Route path="/order/:id/review" element={<OrderReviewPage />} /> {/* 新增的评价路由 */}
              </Route>

              {/* ... 404 路由 */}
            </Routes>
          </div>
        </Content>
        {/* ... Footer */}
      </Layout>
    </Router>
  );
}

export default App;
```

### 5.3 技师评价列表 (技师端)

技师可以在自己的个人中心或单独的页面查看所有用户对自己的评价。

#### 5.3.1 技师评价列表页面 (`src/pages/TechnicianReviewsPage.tsx`)

```tsx
// src/pages/TechnicianReviewsPage.tsx
import React, { useState, useEffect } from 'react';
import { Card, List, Typography, Spin, Rate, Empty, Comment, Avatar, Tooltip } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import api from '../services/api';
import moment from 'moment';
import { useUser } from '../contexts/UserContext';
import { useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;

interface Review {
  id: number;
  rating: number;
  comment?: string;
  createdAt: string;
  user: { // 评价用户的信息
    id: number;
    username: string;
    avatarUrl?: string;
  };
  order: { // 关联的订单信息
    id: number;
    orderNumber: string;
    serviceName: string;
  };
}

const TechnicianReviewsPage: React.FC = () => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useUser();
  const navigate = useNavigate();

  useEffect(() => {
    if (!user || user.role !== 'technician') {
      message.error('您没有权限访问此页面！');
      navigate('/dashboard');
      return;
    }
    fetchTechnicianReviews();
  }, [user, navigate]);

  const fetchTechnicianReviews = async () => {
    setLoading(true);
    try {
      // 假设后端技师评价列表 API 为 /reviews/technician/my
      const response = await api.get('/reviews/technician/my');
      setReviews(response.data);
    } catch (error) {
      console.error('获取技师评价列表失败:', error);
      message.error('加载评价列表失败。');
    } finally {
      setLoading(false);
    }
  };

  if (user && user.role !== 'technician') {
    return null;
  }

  return (
    <Card title="我的评价" style={{ maxWidth: 900, margin: '20px auto' }}>
      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}><Spin size="large" tip="加载评价..." /></div>
      ) : (
        <List
          itemLayout="horizontal"
          dataSource={reviews}
          locale={{ emptyText: <Empty description="暂无用户评价" /> }}
          renderItem={review => (
            <List.Item>
              <Comment
                author={<Text strong>{review.user.username}</Text>}
                avatar={<Avatar src={review.user.avatarUrl} icon={<UserOutlined />} />}
                content={
                  <Space direction="vertical">
                    <Rate disabled defaultValue={review.rating} allowHalf />
                    <Paragraph>{review.comment || '无评论内容'}</Paragraph>
                    <Text type="secondary">
                      针对订单：<Link to={`/order/${review.order.id}`}>{review.order.orderNumber} - {review.order.serviceName}</Link>
                    </Text>
                  </Space>
                }
                datetime={
                  <Tooltip title={moment(review.createdAt).format('YYYY-MM-DD HH:mm:ss')}>
                    <span>{moment(review.createdAt).fromNow()}</span>
                  </Tooltip>
                }
              />
            </List.Item>
          )}
        />
      )}
    </Card>
  );
};

export default TechnicianReviewsPage;
```

**在 `App.tsx` 中添加路由：**

```tsx
// src/App.tsx (部分)
import TechnicianReviewsPage from './pages/TechnicianReviewsPage';
// ... 其他 import

function App() {
  return (
    <Router>
      <Layout className="layout">
        {/* ... Header, Content, Footer */}
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              {/* ... 其他公共路由 */}

              <Route element={<PrivateRoute allowedRoles={['technician']} />}>
                <Route path="/technician/reviews" element={<TechnicianReviewsPage />} /> {/* 技师评价列表 */}
              </Route>

              {/* ... 404 路由 */}
            </Routes>
          </div>
        </Content>
        {/* ... Footer */}
      </Layout>
    </Router>
  );
}

export default App;
```

---

**本篇总结：**

在这一篇中，我们进一步完善了**技师端的功能**和**用户评价体系**：

* **技师个人中心：** 允许技师查看自己的个人信息、技能、完成订单数和平均评分，并能编辑个人资料。
* **用户评价页面：** 为用户提供在订单完成后对技师进行星级评分和文字评论的功能。
* **技师评价列表：** 允许技师查看所有用户对自己服务的评价，帮助技师了解自己的表现和用户反馈。

这些功能将极大地增强技师在平台上的参与感和动力，同时为用户提供透明、可靠的服务选择。

---

现在，用户和技师的核心功能已经基本完成。您对这些新增加的功能有什么问题吗？如果没有，我们将进入**第六篇：消息通知与支付集成 (概念)**，讨论一些更高级但重要的模块。