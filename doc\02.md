好的，我们继续第二篇。这篇我们将专注于本地开发环境的搭建，并初始化我们的NestJS项目。

---

### **《从零构建家政维修平台：打造稳健高效的混合架构后台应用》**

#### **第二篇：本地开发环境搭建与NestJS项目初始化**

**摘要：** 本篇将引导你在本地计算机上搭建必要的开发环境，包括Node.js、数据库工具，并使用NestJS CLI（命令行工具）快速初始化后端项目。这是你编写代码、测试功能的起点，确保你在部署到服务器之前拥有一个可工作的开发基础。

---

**一、安装核心开发环境**

在开始任何项目之前，确保你的本地电脑拥有所有必要的工具。

1.  **安装Node.js和npm/Yarn：**
    * NestJS是基于Node.js运行的，你需要安装Node.js环境。Node.js通常会自带npm（Node Package Manager）。Yarn是npm的一个流行替代品，你可以选择使用其中一个。
    * **安装方法：** 访问Node.js官方网站 `https://nodejs.org/` 下载并安装LTS（长期支持）版本。安装向导会帮你完成大部分设置。
    * **验证安装：** 打开命令行工具（Windows用户推荐使用PowerShell或Git Bash，macOS/Linux用户使用终端），输入 `node -v` 和 `npm -v`（或 `yarn -v`）检查版本。

2.  **安装数据库客户端（MySQL）：**
    * 我们将使用MySQL作为核心数据库。你可以选择安装MySQL Server在本地运行，或者更推荐使用像Docker这样的容器工具来运行MySQL实例，这样环境更隔离，方便管理。
    * **Docker Desktop（推荐）：** 访问 `https://www.docker.com/products/docker-desktop/` 下载并安装Docker Desktop。安装完成后，你可以在命令行运行 `docker version` 验证。
    * **启动MySQL容器：** 使用以下命令在Docker中启动一个MySQL实例：
        ```bash
        docker run --name mysql-db -e MYSQL_ROOT_PASSWORD=your_root_password -p 3306:3306 -d mysql:8.0
        ```
        * `your_root_password` 替换为你的MySQL root用户密码。
        * 这会把MySQL的3306端口映射到你本地的3306端口。
    * **安装数据库管理工具：** 为了方便查看和管理数据库，建议安装一个图形化工具，如 `DataGrip` (付费)、`DBeaver` (免费开源) 或 `Navicat` (付费)。

3.  **安装NestJS CLI：**
    * NestJS提供了一个强大的命令行工具（CLI），可以帮助我们快速创建项目、生成组件等。
    * 在命令行中运行：
        ```bash
        npm install -g @nestjs/cli
        # 或者如果你使用yarn
        # yarn global add @nestjs/cli
        ```
    * **验证安装：** 输入 `nest -v` 检查CLI版本。

**二、初始化NestJS后端项目**

现在，我们使用NestJS CLI来创建我们的项目骨架。

1.  **选择项目目录：**
    * 在你的电脑上，选择一个合适的目录来存放你的项目代码。例如，你可以在桌面上创建一个名为 `home-repair-project` 的文件夹。
    * 在命令行中，进入这个目录：
        ```bash
        cd /path/to/your/home-repair-project
        ```

2.  **创建新的NestJS项目：**
    * 运行以下命令，创建一个名为 `home-repair-backend` 的NestJS项目：
        ```bash
        nest new home-repair-backend
        ```
    * CLI会询问你选择哪种包管理器（`npm` 或 `yarn`），选择你之前安装并习惯使用的那个。
    * NestJS CLI会自动为你生成所有必要的文件和目录结构，并安装初始依赖。

3.  **安装项目依赖：**
    * 项目创建完成后，进入项目根目录：
        ```bash
        cd home-repair-backend
        ```
    * NestJS项目通常会使用TypeORM来连接数据库。现在预先安装TypeORM和MySQL驱动：
        ```bash
        npm install @nestjs/typeorm typeorm mysql2
        # 或者如果你使用yarn
        # yarn add @nestjs/typeorm typeorm mysql2
        ```

**三、配置数据库连接**

让你的NestJS应用能够连接到本地运行的MySQL数据库。

1.  **打开项目：** 使用你喜欢的代码编辑器（如VS Code）打开 `home-repair-backend` 项目文件夹。

2.  **配置根模块：** 找到 `src/app.module.ts` 文件。这是NestJS应用的根模块，我们需要在这里配置数据库连接。
    * 在文件顶部，添加对 `TypeOrmModule` 的导入。
    * 在 `imports` 数组中，添加 `TypeOrmModule.forRoot()` 方法，并填入你的本地MySQL连接信息：

    ```typescript
    // src/app.module.ts

    import { Module } from '@nestjs/common';
    import { AppController } from './app.controller';
    import { AppService } from './app.service';
    import { TypeOrmModule } from '@nestjs/typeorm'; // 导入 TypeOrmModule

    @Module({
      imports: [
        TypeOrmModule.forRoot({
          type: 'mysql', // 数据库类型
          host: 'localhost', // 数据库地址，因为是Docker容器，所以是localhost
          port: 3306, // 数据库端口
          username: 'root', // 数据库用户名，这里使用root，实际开发中建议创建独立用户
          password: 'your_root_password', // 你的MySQL root密码
          database: 'home_repair_db', // 你准备创建的数据库名称
          entities: [], // 稍后会在这里添加实体路径
          synchronize: true, // 重要：开发环境设置为true，TypeORM会自动创建/更新表结构
        }),
      ],
      controllers: [AppController],
      providers: [AppService],
    })
    export class AppModule {}
    ```
    * **创建数据库：** 在你的数据库管理工具中，连接到本地MySQL，手动创建一个名为 `home_repair_db` 的空数据库。确保使用的字符集是 `utf8mb4`。
    * **`synchronize: true` 的注意事项：** 在开发环境中，`synchronize: true` 非常方便，它会根据你的实体定义自动同步数据库表结构。**但请务必记住，在生产环境中，这个值必须设置为 `false`，并使用数据库迁移（Migrations）来管理表结构变更。**

**四、运行并测试你的第一个NestJS应用**

现在，你可以尝试运行你的后端应用，验证一切是否正常。

1.  **启动应用：**
    * 在命令行中，确保你位于 `home-repair-backend` 项目的根目录。
    * 运行以下命令：
        ```bash
        npm run start:dev
        # 或者 yarn start:dev
        ```
    * 如果一切顺利，你会看到类似于“Nest application successfully started”的日志信息，并且应用默认监听在 `http://localhost:3000` 端口。

2.  **访问默认接口：**
    * 打开浏览器，访问 `http://localhost:3000`。
    * 你应该能看到来自NestJS的默认欢迎信息（例如“Hello World!”）。这表明你的后端应用已成功启动并响应请求。

至此，你已经完成了本地开发环境的搭建和NestJS项目的初始化，并成功连接到MySQL数据库。你拥有了一个坚实的起点，可以在此基础上构建家政维修平台的各项后端功能。

---

**[请告诉我“继续”，我将提供第三篇：核心业务模块构建（用户管理）。]**