好的，我们继续进行定制化系列教程的第六篇。在之前的篇章中，我们已经完成了项目初始化、数据库基础设计、用户和技师的身份认证与权限管理、服务与订单的基础管理，以及维修工作流与派单。现在，我们将构建另外两个关键的业务模块——**配件管理与评价系统**。

---

### **《新一代在线家政维修服务平台：从需求到实现的高效开发实战》**

#### **定制化系列教程 第六篇：核心业务模块三：配件与评价**

**摘要：** 本篇教程将专注于“家维在线系统”的配件管理和评价功能。我们将实现对维修配件的增删改查、库存管理，并为用户提供对已完成服务的评价功能，同时支持技师和管理员查看和管理这些评价。这将完善服务的闭环，并为平台积累数据资产。

---

### **6.1 配件管理模块 (Part Module)**

配件管理是维修服务中不可或缺的一部分，涉及到配件的分类、库存、品牌、型号以及兼容性等。

#### **6.1.1 数据库实体设计**

我们需要为配件相关的业务创建新的数据库实体：`Part`（配件）、`Brand`（品牌）、`ProductModel`（产品型号）和 `PartCompatibility`（配件兼容性）。

**1. 创建 `Part` 实体 (src/part/entities/part.entity.ts):**

```typescript
// src/part/entities/part.entity.ts
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, OneToMany } from 'typeorm';
import { Brand } from './brand.entity'; // 假设品牌是单独的实体
import { PartCompatibility } from './part-compatibility.entity'; // 兼容性实体

@Entity('parts')
export class Part {
  @PrimaryGeneratedColumn('uuid')
  id: string; // 内部唯一ID

  @Column({ unique: true, length: 50 })
  partNumber: string; // 配件 SKU/货号

  @Column({ length: 255 })
  partName: string; // 配件名称

  @Column({ type: 'text', nullable: true })
  description: string; // 配件描述

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number; // 配件销售价格

  @Column({ default: 0 })
  stock: number; // 库存数量

  @Column({ length: 255, nullable: true })
  imageUrl: string; // 配件图片URL

  @Column({ length: 50, nullable: true })
  categoryId: string; // 配件分类ID (如果需要独立的配件分类模块)

  @Column({ length: 50, nullable: true })
  brandId: string; // 所属品牌ID

  @ManyToOne(() => Brand, brand => brand.parts)
  @JoinColumn({ name: 'brandId' })
  brand: Brand;

  @OneToMany(() => PartCompatibility, compatibility => compatibility.part)
  compatibilities: PartCompatibility[]; // 配件兼容性列表

  @Column({ default: true })
  isActive: boolean; // 是否上架/可用

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}
```

**2. 创建 `Brand` 实体 (src/part/entities/brand.entity.ts):**

```typescript
// src/part/entities/brand.entity.ts
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Part } from './part.entity';
import { ProductModel } from './product-model.entity';

@Entity('brands')
export class Brand {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, length: 100 })
  brandName: string; // 品牌名称

  @Column({ type: 'text', nullable: true })
  description: string; // 品牌描述

  @Column({ length: 255, nullable: true })
  logoUrl: string; // 品牌Logo

  @OneToMany(() => Part, part => part.brand)
  parts: Part[]; // 该品牌下的所有配件

  @OneToMany(() => ProductModel, model => model.brand)
  models: ProductModel[]; // 该品牌下的所有产品型号

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}
```

**3. 创建 `ProductModel` 实体 (src/part/entities/product-model.entity.ts):**

```typescript
// src/part/entities/product-model.entity.ts
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, OneToMany } from 'typeorm';
import { Brand } from './brand.entity';
import { PartCompatibility } from './part-compatibility.entity';

@Entity('product_models')
export class ProductModel {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  modelName: string; // 型号名称 (e.g., iPhone 15 Pro Max)

  @Column({ length: 50, nullable: true })
  brandId: string; // 所属品牌ID

  @ManyToOne(() => Brand, brand => brand.models)
  @JoinColumn({ name: 'brandId' })
  brand: Brand;

  @Column({ type: 'text', nullable: true })
  description: string;

  @OneToMany(() => PartCompatibility, compatibility => compatibility.productModel)
  compatibleParts: PartCompatibility[]; // 兼容此型号的配件

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}
```

**4. 创建 `PartCompatibility` 实体 (src/part/entities/part-compatibility.entity.ts):**

```typescript
// src/part/entities/part-compatibility.entity.ts
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, Unique } from 'typeorm';
import { Part } from './part.entity';
import { ProductModel } from './product-model.entity';

@Entity('part_compatibilities')
@Unique(['partId', 'productModelId']) // 确保一个配件与一个型号的兼容性记录唯一
export class PartCompatibility {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50 })
  partId: string; // 配件ID

  @ManyToOne(() => Part, part => part.compatibilities, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'partId' })
  part: Part;

  @Column({ length: 50 })
  productModelId: string; // 产品型号ID

  @ManyToOne(() => ProductModel, model => model.compatibleParts, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'productModelId' })
  productModel: ProductModel;

  @Column({ default: true })
  isCompatible: boolean; // 是否兼容 (未来可扩展为兼容性类型)

  @Column({ type: 'text', nullable: true })
  notes: string; // 兼容性备注

  // 不需要CreateDateColumn和UpdateDateColumn，因为这是关联表
}
```

#### **6.1.2 模块、服务与控制器**

我们将为 `Part`、`Brand` 和 `ProductModel` 分别创建模块、服务和控制器，以实现 CRUD 功能。`PartCompatibility` 的管理将集成到 `Part` 或 `ProductModel` 的管理中。

**1. 创建 Part 模块：**

```bash
nest generate module part
nest generate service part
nest generate controller part
```

**2. 配置 PartModule (src/part/part.module.ts):**

```typescript
// src/part/part.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PartService } from './part.service';
import { PartController } from './part.controller';
import { Part } from './entities/part.entity';
import { Brand } from './entities/brand.entity';
import { ProductModel } from './entities/product-model.entity';
import { PartCompatibility } from './entities/part-compatibility.entity';
import { BrandService } from './brand.service'; // 假设有 BrandService
import { ProductModelService } from './product-model.service'; // 假设有 ProductModelService

@Module({
  imports: [
    TypeOrmModule.forFeature([Part, Brand, ProductModel, PartCompatibility]),
  ],
  controllers: [PartController],
  providers: [PartService, BrandService, ProductModelService], // 暂时都在这里提供
  exports: [PartService, BrandService, ProductModelService], // 如果其他模块需要查询配件信息
})
export class PartModule {}
```

**3. 创建 Brand 和 ProductModel 的 Service 和 Controller：**

为了简化，这里不再详细列出 `BrandService/Controller` 和 `ProductModelService/Controller` 的完整代码，其结构与之前 ServiceCategory 类似，包含基本的 CRUD 方法。它们都将注入各自的 Repository。

例如，`BrandService` 骨架：

```typescript
// src/part/brand.service.ts
import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Brand } from './entities/brand.entity';
import { CreateBrandDto, UpdateBrandDto } from './dto/brand.dto';

@Injectable()
export class BrandService {
  constructor(
    @InjectRepository(Brand)
    private brandRepository: Repository<Brand>,
  ) {}

  async create(createBrandDto: CreateBrandDto): Promise<Brand> {
    const existingBrand = await this.brandRepository.findOne({ where: { brandName: createBrandDto.brandName } });
    if (existingBrand) {
      throw new BadRequestException(`品牌 '${createBrandDto.brandName}' 已存在`);
    }
    const newBrand = this.brandRepository.create(createBrandDto);
    return this.brandRepository.save(newBrand);
  }

  async findAll(): Promise<Brand[]> {
    return this.brandRepository.find();
  }

  async findOne(id: string): Promise<Brand> {
    const brand = await this.brandRepository.findOne({ where: { id } });
    if (!brand) {
      throw new NotFoundException(`品牌 ID 为 ${id} 未找到`);
    }
    return brand;
  }

  async update(id: string, updateBrandDto: UpdateBrandDto): Promise<Brand> {
    const brand = await this.brandRepository.findOne({ where: { id } });
    if (!brand) {
      throw new NotFoundException(`品牌 ID 为 ${id} 未找到`);
    }
    Object.assign(brand, updateBrandDto);
    return this.brandRepository.save(brand);
  }

  async remove(id: string): Promise<void> {
    const result = await this.brandRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`品牌 ID 为 ${id} 未找到`);
    }
  }
}
```

同样创建 `src/part/product-model.service.ts` 和 `src/part/product-model.controller.ts`。

**4. 实现 PartService (src/part/part.service.ts):**

```typescript
// src/part/part.service.ts
import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Part } from './entities/part.entity';
import { CreatePartDto, UpdatePartDto, AddPartCompatibilityDto } from './dto/part.dto';
import { BrandService } from './brand.service';
import { ProductModelService } from './product-model.service';
import { PartCompatibility } from './entities/part-compatibility.entity';

@Injectable()
export class PartService {
  constructor(
    @InjectRepository(Part)
    private partRepository: Repository<Part>,
    @InjectRepository(PartCompatibility)
    private partCompatibilityRepository: Repository<PartCompatibility>,
    private brandService: BrandService,
    private productModelService: ProductModelService,
  ) {}

  /**
   * 创建配件 (管理员)
   */
  async create(createPartDto: CreatePartDto): Promise<Part> {
    const { partNumber, brandId } = createPartDto;

    const existingPart = await this.partRepository.findOne({ where: { partNumber } });
    if (existingPart) {
      throw new BadRequestException(`配件货号 '${partNumber}' 已存在`);
    }

    if (brandId) {
      await this.brandService.findOne(brandId); // 验证品牌是否存在
    }

    const newPart = this.partRepository.create(createPartDto);
    return this.partRepository.save(newPart);
  }

  /**
   * 获取所有配件 (可筛选、分页)
   */
  async findAll(query?: { isActive?: boolean; brandId?: string; categoryId?: string; keywords?: string }): Promise<Part[]> {
    const whereCondition: any = {};
    if (query?.isActive !== undefined) {
      whereCondition.isActive = query.isActive;
    }
    if (query?.brandId) {
      whereCondition.brandId = query.brandId;
    }
    if (query?.categoryId) {
      whereCondition.categoryId = query.categoryId;
    }

    const queryBuilder = this.partRepository.createQueryBuilder('part')
      .leftJoinAndSelect('part.brand', 'brand')
      .leftJoinAndSelect('part.compatibilities', 'compatibilities')
      .leftJoinAndSelect('compatibilities.productModel', 'productModel');

    if (query?.keywords) {
      queryBuilder.andWhere('(part.partName LIKE :keywords OR part.partNumber LIKE :keywords OR part.description LIKE :keywords)', { keywords: `%${query.keywords}%` });
    }

    queryBuilder.where(whereCondition);

    return queryBuilder.getMany();
  }

  /**
   * 根据ID获取配件详情
   */
  async findOne(id: string): Promise<Part> {
    const part = await this.partRepository.findOne({ where: { id }, relations: ['brand', 'compatibilities', 'compatibilities.productModel'] });
    if (!part) {
      throw new NotFoundException(`配件 ID 为 ${id} 未找到`);
    }
    return part;
  }

  /**
   * 更新配件信息 (管理员)
   */
  async update(id: string, updatePartDto: UpdatePartDto): Promise<Part> {
    const part = await this.partRepository.findOne({ where: { id } });
    if (!part) {
      throw new NotFoundException(`配件 ID 为 ${id} 未找到`);
    }

    if (updatePartDto.brandId && updatePartDto.brandId !== part.brandId) {
      await this.brandService.findOne(updatePartDto.brandId); // 验证新品牌是否存在
    }
    // 检查货号是否重复（如果修改了货号）
    if (updatePartDto.partNumber && updatePartDto.partNumber !== part.partNumber) {
        const existing = await this.partRepository.findOne({ where: { partNumber: updatePartDto.partNumber } });
        if (existing) {
            throw new BadRequestException(`配件货号 '${updatePartDto.partNumber}' 已存在`);
        }
    }

    Object.assign(part, updatePartDto);
    return this.partRepository.save(part);
  }

  /**
   * 删除配件 (管理员)
   */
  async remove(id: string): Promise<void> {
    const result = await this.partRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`配件 ID 为 ${id} 未找到`);
    }
  }

  /**
   * 更新库存 (管理员)
   */
  async updateStock(id: string, quantity: number): Promise<Part> {
    const part = await this.partRepository.findOne({ where: { id } });
    if (!part) {
      throw new NotFoundException(`配件 ID 为 ${id} 未找到`);
    }
    if (part.stock + quantity < 0) {
        throw new BadRequestException('库存不足，无法进行此操作');
    }
    part.stock += quantity; // quantity 可以是正数（入库）或负数（出库）
    return this.partRepository.save(part);
  }

  /**
   * 添加配件兼容性 (管理员)
   */
  async addCompatibility(addDto: AddPartCompatibilityDto): Promise<PartCompatibility> {
    const { partId, productModelId, notes } = addDto;

    await this.findOne(partId); // 验证配件是否存在
    await this.productModelService.findOne(productModelId); // 验证产品型号是否存在

    const existingCompatibility = await this.partCompatibilityRepository.findOne({
      where: { partId, productModelId },
    });
    if (existingCompatibility) {
      throw new BadRequestException('该配件与该型号的兼容性记录已存在');
    }

    const newCompatibility = this.partCompatibilityRepository.create({
      partId,
      productModelId,
      isCompatible: true, // 默认兼容
      notes,
    });
    return this.partCompatibilityRepository.save(newCompatibility);
  }

  /**
   * 移除配件兼容性 (管理员)
   */
  async removeCompatibility(compatibilityId: string): Promise<void> {
    const result = await this.partCompatibilityRepository.delete(compatibilityId);
    if (result.affected === 0) {
      throw new NotFoundException(`配件兼容性记录 ID 为 ${compatibilityId} 未找到`);
    }
  }
}
```

**5. 实现 PartController (src/part/part.controller.ts):**

```typescript
// src/part/part.controller.ts
import { Controller, Post, Get, Put, Delete, Body, Param, HttpStatus, UseGuards, Query } from '@nestjs/common';
import { PartService } from './part.service';
import { CreatePartDto, UpdatePartDto, UpdatePartStockDto, AddPartCompatibilityDto } from './dto/part.dto';
import { AuthGuard } from '@nestjs/passport';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';

@Controller('api/parts')
export class PartController {
  constructor(private readonly partService: PartService) {}

  /**
   * 创建配件 (仅限管理员)
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @Post()
  async create(@Body() createPartDto: CreatePartDto) {
    const part = await this.partService.create(createPartDto);
    return {
      statusCode: HttpStatus.CREATED,
      message: '配件创建成功',
      data: part,
    };
  }

  /**
   * 获取所有配件列表 (用户、技师、管理员可见)
   * 支持筛选 (isActive, brandId, categoryId, keywords)
   */
  @Get()
  async findAll(@Query() query: any) {
    const parts = await this.partService.findAll(query);
    return {
      statusCode: HttpStatus.OK,
      data: parts,
    };
  }

  /**
   * 根据ID获取配件详情 (用户、技师、管理员可见)
   */
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const part = await this.partService.findOne(id);
    return {
      statusCode: HttpStatus.OK,
      data: part,
    };
  }

  /**
   * 更新配件信息 (仅限管理员)
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @Put(':id')
  async update(@Param('id') id: string, @Body() updatePartDto: UpdatePartDto) {
    const updatedPart = await this.partService.update(id, updatePartDto);
    return {
      statusCode: HttpStatus.OK,
      message: '配件信息更新成功',
      data: updatedPart,
    };
  }

  /**
   * 删除配件 (仅限管理员)
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.partService.remove(id);
    return {
      statusCode: HttpStatus.NO_CONTENT,
      message: '配件删除成功',
    };
  }

  /**
   * 更新配件库存 (管理员)
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @Put(':id/stock')
  async updateStock(@Param('id') id: string, @Body() updateStockDto: UpdatePartStockDto) {
    const updatedPart = await this.partService.updateStock(id, updateStockDto.quantity);
    return {
      statusCode: HttpStatus.OK,
      message: '配件库存更新成功',
      data: updatedPart,
    };
  }

  /**
   * 添加配件兼容性 (管理员)
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @Post('compatibility')
  async addCompatibility(@Body() addCompatibilityDto: AddPartCompatibilityDto) {
    const compatibility = await this.partService.addCompatibility(addCompatibilityDto);
    return {
      statusCode: HttpStatus.CREATED,
      message: '配件兼容性添加成功',
      data: compatibility,
    };
  }

  /**
   * 移除配件兼容性 (管理员)
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @Delete('compatibility/:id')
  async removeCompatibility(@Param('id') id: string) {
    await this.partService.removeCompatibility(id);
    return {
      statusCode: HttpStatus.NO_CONTENT,
      message: '配件兼容性移除成功',
    };
  }

  // --- Brand & ProductModel 的 Controller (类似 PartController，此处省略具体实现) ---
  // @Controller('api/brands') ...
  // @Controller('api/product-models') ...
}
```

**6. 定义配件相关 DTOs (src/part/dto/part.dto.ts):**

```typescript
// src/part/dto/part.dto.ts
import { IsNotEmpty, IsString, MaxLength, IsNumber, IsOptional, Min, IsBoolean, IsUrl, IsUUID } from 'class-validator';

export class CreatePartDto {
  @IsNotEmpty({ message: '配件货号不能为空' })
  @IsString({ message: '配件货号必须是字符串' })
  @MaxLength(50, { message: '配件货号长度不能超过50个字符' })
  partNumber: string;

  @IsNotEmpty({ message: '配件名称不能为空' })
  @IsString({ message: '配件名称必须是字符串' })
  @MaxLength(255, { message: '配件名称长度不能超过255个字符' })
  partName: string;

  @IsOptional()
  @IsString({ message: '配件描述必须是字符串' })
  description?: string;

  @IsNotEmpty({ message: '价格不能为空' })
  @IsNumber({}, { message: '价格必须是数字' })
  @Min(0, { message: '价格不能为负数' })
  price: number;

  @IsOptional()
  @IsNumber({}, { message: '库存必须是数字' })
  @Min(0, { message: '库存不能为负数' })
  stock?: number;

  @IsOptional()
  @IsUrl({}, { message: '图片URL格式不正确' })
  imageUrl?: string;

  @IsOptional()
  @IsString({ message: '分类ID必须是字符串' })
  categoryId?: string; // 如果有独立的配件分类

  @IsOptional()
  @IsUUID('4', { message: '品牌ID格式不正确' })
  brandId?: string; // 关联品牌

  @IsOptional()
  @IsBoolean({ message: '是否上架必须是布尔值' })
  isActive?: boolean;
}

export class UpdatePartDto {
  @IsOptional()
  @IsString({ message: '配件货号必须是字符串' })
  @MaxLength(50, { message: '配件货号长度不能超过50个字符' })
  partNumber?: string;

  @IsOptional()
  @IsString({ message: '配件名称必须是字符串' })
  @MaxLength(255, { message: '配件名称长度不能超过255个字符' })
  partName?: string;

  @IsOptional()
  @IsString({ message: '配件描述必须是字符串' })
  description?: string;

  @IsOptional()
  @IsNumber({}, { message: '价格必须是数字' })
  @Min(0, { message: '价格不能为负数' })
  price?: number;

  @IsOptional()
  @IsNumber({}, { message: '库存必须是数字' })
  @Min(0, { message: '库存不能为负数' })
  stock?: number; // 注意：直接修改库存通常通过updateStock方法，这里作为可选字段方便管理

  @IsOptional()
  @IsUrl({}, { message: '图片URL格式不正确' })
  imageUrl?: string;

  @IsOptional()
  @IsString({ message: '分类ID必须是字符串' })
  categoryId?: string;

  @IsOptional()
  @IsUUID('4', { message: '品牌ID格式不正确' })
  brandId?: string;

  @IsOptional()
  @IsBoolean({ message: '是否上架必须是布尔值' })
  isActive?: boolean;
}

export class UpdatePartStockDto {
  @IsNotEmpty({ message: '库存变动数量不能为空' })
  @IsNumber({}, { message: '库存变动数量必须是数字' })
  quantity: number; // 正数表示入库，负数表示出库
}

export class AddPartCompatibilityDto {
  @IsNotEmpty({ message: '配件ID不能为空' })
  @IsUUID('4', { message: '配件ID格式不正确' })
  partId: string;

  @IsNotEmpty({ message: '产品型号ID不能为空' })
  @IsUUID('4', { message: '产品型号ID格式不正确' })
  productModelId: string;

  @IsOptional()
  @IsString({ message: '备注必须是字符串' })
  notes?: string;
}
```

同时，您需要为 `Brand` 和 `ProductModel` 创建类似的 DTO 文件，例如 `src/part/dto/brand.dto.ts` 和 `src/part/dto/product-model.dto.ts`。

**7. 在 `AppModule` 中导入 `PartModule`：**

```typescript
// src/app.module.ts
import { Module } from '@nestjs/common';
// ... 其他导入
import { ServiceCategoryModule } from './serviceCategory/service-category.module';
import { ServiceModule } from './service/service.module';
import { OrderModule } from './order/order.module';
import { PartModule } from './part/part.module'; // 导入配件模块

@Module({
  imports: [
    // ... TypeOrmModule.forRoot 和 ConfigModule.forRoot
    UserModule,
    TechnicianModule,
    AuthModule,
    ServiceCategoryModule,
    ServiceModule,
    OrderModule,
    PartModule, // 导入配件模块
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
```

---

### **6.2 评价模块 (Review Module)**

评价模块允许用户对完成的服务进行评分和评论，是提高服务质量和用户满意度的重要机制。

#### **6.2.1 数据库实体设计**

**1. 创建 `Review` 实体 (src/review/entities/review.entity.ts):**

```typescript
// src/review/entities/review.entity.ts
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../user/entities/user.entity';
import { Order } from '../../order/entities/order.entity';
import { Technician } from '../../technician/entities/technician.entity';
import { Service } from '../../service/entities/service.entity'; // 如果评价针对服务

@Entity('reviews')
export class Review {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50 })
  orderId: string; // 关联的订单ID

  @OneToOne(() => Order) // 假设一个订单只能有一条用户评价
  @JoinColumn({ name: 'orderId' })
  order: Order;

  @Column({ length: 50 })
  userId: string; // 评价人ID

  @ManyToOne(() => User, user => user.reviews)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ length: 50, nullable: true })
  technicianId: string; // 被评价技师ID

  @ManyToOne(() => Technician, technician => technician.receivedReviews, { nullable: true })
  @JoinColumn({ name: 'technicianId' })
  technician: Technician;

  @Column({ length: 50, nullable: true })
  serviceId: string; // 被评价的服务ID (可以从订单中获取，也可以直接针对服务评价)

  @ManyToOne(() => Service, service => service.reviews, { nullable: true })
  @JoinColumn({ name: 'serviceId' })
  service: Service;

  @Column({ type: 'int' })
  rating: number; // 评分 (1-5 星)

  @Column({ type: 'text', nullable: true })
  content: string; // 评价内容

  @Column({ type: 'json', nullable: true })
  imageUrls: string[]; // 评价图片 (JSON 数组)

  @Column({ default: false })
  isAnonymous: boolean; // 是否匿名评价

  @Column({ default: false })
  isPublished: boolean; // 评价是否已发布 (可能需要审核)

  @Column({ type: 'text', nullable: true })
  adminReply: string; // 管理员回复

  @Column({ type: 'text', nullable: true })
  technicianReply: string; // 技师回复

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}
```

**2. 在 `Order` 实体中添加评价状态字段（可选）：**

为了避免重复评价，可以在 `Order` 实体中添加一个字段来标记订单是否已被评价。

```typescript
// src/order/entities/order.entity.ts (在 Order 实体中添加或修改)
// ... 其他字段

@Column({ default: false })
hasReviewed: boolean; // 订单是否已被评价

// ...
```

#### **6.2.2 模块、服务与控制器**

**1. 创建 Review 模块：**

```bash
nest generate module review
nest generate service review
nest generate controller review
```

**2. 配置 ReviewModule (src/review/review.module.ts):**

```typescript
// src/review/review.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ReviewService } from './review.service';
import { ReviewController } from './review.controller';
import { Review } from './entities/review.entity';
import { OrderModule } from '../order/order.module'; // 需要访问订单信息
import { UserModule } from '../user/user.module'; // 需要访问用户信息
import { TechnicianModule } from '../technician/technician.module'; // 需要访问技师信息
import { ServiceModule } from '../service/service.module'; // 需要访问服务信息

@Module({
  imports: [
    TypeOrmModule.forFeature([Review]),
    OrderModule,
    UserModule,
    TechnicianModule,
    ServiceModule,
  ],
  controllers: [ReviewController],
  providers: [ReviewService],
  exports: [ReviewService], // 如果其他模块需要统计评价等
})
export class ReviewModule {}
```

**3. 实现 ReviewService (src/review/review.service.ts):**

```typescript
// src/review/review.service.ts
import { Injectable, BadRequestException, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Review } from './entities/review.entity';
import { CreateReviewDto, UpdateReviewDto, ReplyReviewDto } from './dto/review.dto';
import { OrderService, OrderStatus } from '../order/order.service';
import { UserService } from '../user/user.service';
import { TechnicianService } from '../technician/technician.service';

@Injectable()
export class ReviewService {
  constructor(
    @InjectRepository(Review)
    private reviewRepository: Repository<Review>,
    private orderService: OrderService,
    private userService: UserService,
    private technicianService: TechnicianService,
  ) {}

  /**
   * 用户创建评价
   * @param userId 评价用户ID
   * @param createReviewDto 评价数据
   */
  async create(userId: string, createReviewDto: CreateReviewDto): Promise<Review> {
    const { orderId, rating, content, imageUrls, isAnonymous } = createReviewDto;

    const order = await this.orderService.getOrderDetail(orderId);
    if (!order) {
      throw new NotFoundException(`订单 ID 为 ${orderId} 未找到`);
    }
    if (order.userId !== userId) {
      throw new UnauthorizedException('您无权评价此订单');
    }
    if (order.status !== OrderStatus.COMPLETED) {
      throw new BadRequestException('只有已完成的订单才能评价');
    }
    if (order.hasReviewed) {
      throw new BadRequestException('该订单已评价过');
    }

    // 获取技师ID和服务ID (从订单中获取)
    const technicianId = order.technicianId;
    const serviceId = order.serviceId;

    const newReview = this.reviewRepository.create({
      orderId,
      userId,
      technicianId,
      serviceId,
      rating,
      content,
      imageUrls,
      isAnonymous,
      isPublished: true, // 默认立即发布，或者设置为false等待审核
    });

    const savedReview = await this.reviewRepository.save(newReview);

    // 更新订单的评价状态
    order.hasReviewed = true;
    await this.orderService['orderRepository'].save(order); // 直接访问 repository 来更新订单

    // TODO: 更新技师的平均评分 (可以在技师服务中实现)

    return savedReview;
  }

  /**
   * 获取单条评价详情
   * @param id 评价ID
   */
  async findOne(id: string): Promise<Review> {
    const review = await this.reviewRepository.findOne({
      where: { id },
      relations: ['user', 'order', 'technician', 'service'],
    });
    if (!review) {
      throw new NotFoundException(`评价 ID 为 ${id} 未找到`);
    }
    return review;
  }

  /**
   * 获取评价列表 (可根据用户、技师、服务过滤，支持分页)
   * @param query 查询参数 (userId, technicianId, serviceId, rating, keywords, page, limit)
   */
  async findAll(query?: {
    userId?: string;
    technicianId?: string;
    serviceId?: string;
    rating?: number;
    keywords?: string;
    isPublished?: boolean;
    page?: number;
    limit?: number;
  }): Promise<Review[]> {
    const { userId, technicianId, serviceId, rating, keywords, isPublished, page = 1, limit = 10 } = query;
    const offset = (page - 1) * limit;

    const queryBuilder = this.reviewRepository.createQueryBuilder('review')
      .leftJoinAndSelect('review.user', 'user')
      .leftJoinAndSelect('review.order', 'order')
      .leftJoinAndSelect('review.technician', 'technician')
      .leftJoinAndSelect('review.service', 'service');

    if (userId) {
      queryBuilder.andWhere('review.userId = :userId', { userId });
    }
    if (technicianId) {
      queryBuilder.andWhere('review.technicianId = :technicianId', { technicianId });
    }
    if (serviceId) {
      queryBuilder.andWhere('review.serviceId = :serviceId', { serviceId });
    }
    if (rating) {
      queryBuilder.andWhere('review.rating = :rating', { rating });
    }
    if (keywords) {
      queryBuilder.andWhere('(review.content LIKE :keywords OR user.username LIKE :keywords)', { keywords: `%${keywords}%` });
    }
    if (isPublished !== undefined) {
      queryBuilder.andWhere('review.isPublished = :isPublished', { isPublished });
    }

    queryBuilder.orderBy('review.createTime', 'DESC')
      .offset(offset)
      .limit(limit);

    return queryBuilder.getMany();
  }

  /**
   * 管理员/技师回复评价
   * @param id 评价ID
   * @param replyContent 回复内容
   * @param isTechnicianReply 是否技师回复 (否则为管理员回复)
   * @param replierId 回复人ID (管理员ID或技师ID)
   */
  async reply(id: string, replyContent: string, isTechnicianReply: boolean, replierId: string): Promise<Review> {
    const review = await this.reviewRepository.findOne({ where: { id }, relations: ['technician'] });
    if (!review) {
      throw new NotFoundException(`评价 ID 为 ${id} 未找到`);
    }

    if (isTechnicianReply) {
      if (review.technicianId !== replierId) {
        throw new UnauthorizedException('您无权回复此评价');
      }
      review.technicianReply = replyContent;
    } else {
      // 假设管理员可以回复所有评价
      review.adminReply = replyContent;
    }

    return this.reviewRepository.save(review);
  }

  /**
   * 管理员审核评价 (发布/取消发布)
   * @param id 评价ID
   * @param isPublished 是否发布
   */
  async updatePublishStatus(id: string, isPublished: boolean): Promise<Review> {
    const review = await this.reviewRepository.findOne({ where: { id } });
    if (!review) {
      throw new NotFoundException(`评价 ID 为 ${id} 未找到`);
    }
    review.isPublished = isPublished;
    return this.reviewRepository.save(review);
  }

  /**
   * 管理员删除评价
   * @param id 评价ID
   */
  async remove(id: string): Promise<void> {
    const result = await this.reviewRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`评价 ID 为 ${id} 未找到`);
    }
    // TODO: 删除评价后，需要重新计算技师的平均评分
  }
}
```

**4. 实现 ReviewController (src/review/review.controller.ts):**

```typescript
// src/review/review.controller.ts
import { Controller, Post, Get, Put, Delete, Body, Param, HttpStatus, UseGuards, Query } from '@nestjs/common';
import { ReviewService } from './review.service';
import { CreateReviewDto, ReplyReviewDto, UpdateReviewPublishStatusDto } from './dto/review.dto';
import { AuthGuard } from '@nestjs/passport';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { CurrentUser } from '../auth/current-user.decorator'; // 导入自定义装饰器

@Controller('api/reviews')
export class ReviewController {
  constructor(private readonly reviewService: ReviewService) {}

  /**
   * 用户创建评价
   */
  @UseGuards(AuthGuard('jwt'))
  @Post()
  async create(@CurrentUser('userId') userId: string, @Body() createReviewDto: CreateReviewDto) {
    const review = await this.reviewService.create(userId, createReviewDto);
    return {
      statusCode: HttpStatus.CREATED,
      message: '评价提交成功',
      data: review,
    };
  }

  /**
   * 获取单条评价详情 (所有用户可见已发布的评价)
   */
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const review = await this.reviewService.findOne(id);
    // 如果评价未发布，非管理员不能查看
    if (!review.isPublished && !this.isUserAdminOrTechnician(review.technicianId)) { // 假设有 isAdminOrTechnician 辅助函数
      throw new UnauthorizedException('评价尚未发布或您无权查看');
    }
    return {
      statusCode: HttpStatus.OK,
      data: review,
    };
  }

  /**
   * 获取评价列表 (所有用户可见已发布的评价)
   * 管理员可查看所有评价 (isPublished=false 也可见)
   * 技师可查看分配给自己的评价 (isPublished=false 也可见)
   */
  @Get()
  @UseGuards(AuthGuard('jwt'), RolesGuard) // 可选：如果希望未登录用户也能看已发布的评价，则移除 guards
  async findAll(@CurrentUser() user: any, @Query() query: any) {
    // 默认只查询已发布的评价
    let isPublishedFilter = true;
    if (user && (user.role === 'admin' || user.role === 'customer_service')) {
        // 管理员或客服可以查看所有评价，包括未发布的
        isPublishedFilter = query.isPublished === 'true' ? true : query.isPublished === 'false' ? false : undefined;
    } else if (user && user.role === 'technician') {
        // 技师只能查看自己的评价（无论是否发布）
        query.technicianId = user.technicianId;
        isPublishedFilter = undefined; // 技师查看自己的评价时忽略发布状态
    }

    const reviews = await this.reviewService.findAll({ ...query, isPublished: isPublishedFilter });
    return {
      statusCode: HttpStatus.OK,
      data: reviews,
    };
  }

  /**
   * 技师回复评价
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('technician')
  @Post(':id/reply/technician')
  async replyByTechnician(
    @Param('id') id: string,
    @CurrentUser('technicianId') technicianId: string,
    @Body() replyDto: ReplyReviewDto,
  ) {
    const updatedReview = await this.reviewService.reply(id, replyDto.replyContent, true, technicianId);
    return {
      statusCode: HttpStatus.OK,
      message: '技师回复成功',
      data: updatedReview,
    };
  }

  /**
   * 管理员回复评价
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin', 'customer_service')
  @Post(':id/reply/admin')
  async replyByAdmin(
    @Param('id') id: string,
    @CurrentUser('userId') adminId: string, // 使用管理员的 userId
    @Body() replyDto: ReplyReviewDto,
  ) {
    const updatedReview = await this.reviewService.reply(id, replyDto.replyContent, false, adminId);
    return {
      statusCode: HttpStatus.OK,
      message: '管理员回复成功',
      data: updatedReview,
    };
  }

  /**
   * 管理员审核评价 (发布/取消发布)
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin', 'customer_service')
  @Put(':id/publish')
  async updatePublishStatus(@Param('id') id: string, @Body() statusDto: UpdateReviewPublishStatusDto) {
    const updatedReview = await this.reviewService.updatePublishStatus(id, statusDto.isPublished);
    return {
      statusCode: HttpStatus.OK,
      message: `评价已${statusDto.isPublished ? '发布' : '取消发布'}`,
      data: updatedReview,
    };
  }

  /**
   * 管理员删除评价
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.reviewService.remove(id);
    return {
      statusCode: HttpStatus.NO_CONTENT,
      message: '评价删除成功',
    };
  }

  // 辅助函数 (实际项目中可能在 Auth 模块中或 Guard 中处理)
  private isUserAdminOrTechnician(technicianId: string): boolean {
    // 这是一个简化示例，实际需要从 JWT payload 中获取用户角色和ID进行判断
    // return user.role === 'admin' || (user.role === 'technician' && user.technicianId === technicianId);
    return false; // 默认返回 false
  }
}
```

**5. 定义评价相关 DTOs (src/review/dto/review.dto.ts):**

```typescript
// src/review/dto/review.dto.ts
import { IsNotEmpty, IsString, IsNumber, Min, Max, IsOptional, IsBoolean, IsArray, IsUrl, IsUUID } from 'class-validator';

export class CreateReviewDto {
  @IsNotEmpty({ message: '订单ID不能为空' })
  @IsUUID('4', { message: '订单ID格式不正确' })
  orderId: string;

  @IsNotEmpty({ message: '评分不能为空' })
  @IsNumber({}, { message: '评分必须是数字' })
  @Min(1, { message: '评分不能小于1星' })
  @Max(5, { message: '评分不能大于5星' })
  rating: number;

  @IsOptional()
  @IsString({ message: '评价内容必须是字符串' })
  content?: string;

  @IsOptional()
  @IsArray({ message: '图片URL必须是数组' })
  @IsUrl({}, { each: true, message: '每张图片URL格式不正确' })
  imageUrls?: string[];

  @IsOptional()
  @IsBoolean({ message: '是否匿名必须是布尔值' })
  isAnonymous?: boolean;
}

export class UpdateReviewDto {
  // 暂时不提供用户修改评价的功能，如果需要可添加
  @IsOptional()
  @IsNumber({}, { message: '评分必须是数字' })
  @Min(1, { message: '评分不能小于1星' })
  @Max(5, { message: '评分不能大于5星' })
  rating?: number;

  @IsOptional()
  @IsString({ message: '评价内容必须是字符串' })
  content?: string;

  @IsOptional()
  @IsArray({ message: '图片URL必须是数组' })
  @IsUrl({}, { each: true, message: '每张图片URL格式不正确' })
  imageUrls?: string[];

  @IsOptional()
  @IsBoolean({ message: '是否匿名必须是布尔值' })
  isAnonymous?: boolean;
}

export class ReplyReviewDto {
  @IsNotEmpty({ message: '回复内容不能为空' })
  @IsString({ message: '回复内容必须是字符串' })
  replyContent: string;
}

export class UpdateReviewPublishStatusDto {
  @IsNotEmpty({ message: '发布状态不能为空' })
  @IsBoolean({ message: '发布状态必须是布尔值' })
  isPublished: boolean;
}
```

**6. 在 `AppModule` 中导入 `ReviewModule`：**

```typescript
// src/app.module.ts
import { Module } from '@nestjs/common';
// ... 其他导入
import { ServiceCategoryModule } from './serviceCategory/service-category.module';
import { ServiceModule } from './service/service.module';
import { OrderModule } from './order/order.module';
import { PartModule } from './part/part.module';
import { ReviewModule } from './review/review.module'; // 导入评价模块

@Module({
  imports: [
    // ... TypeOrmModule.forRoot 和 ConfigModule.forRoot
    UserModule,
    TechnicianModule,
    AuthModule,
    ServiceCategoryModule,
    ServiceModule,
    OrderModule,
    PartModule,
    ReviewModule, // 导入评价模块
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
```

**重要提示：**

* 在创建新的实体后，请务必运行 TypeORM 迁移命令来更新数据库结构：
    * `npx typeorm migration:generate -n AddPartAndReviewModules`
    * `npx typeorm migration:run`
* 技师和管理员在回复评价时，需要从 `CurrentUser` 装饰器中获取相应的 ID。请确保您的 `CurrentUser` 装饰器能够正确解析 JWT Payload 中的 `userId` 和 `technicianId`。

---

**测试与验证：**

1.  **启动应用**并确保数据库已通过迁移更新。
2.  **创建品牌 (管理员权限)：**
    * `POST /api/brands`，Body: `{ "brandName": "美的", "description": "家用电器品牌" }`
3.  **创建产品型号 (管理员权限)：**
    * `POST /api/product-models`，Body: `{ "modelName": "KFR-35GW/BP3DN8Y-TP", "brandId": "your_brand_id" }`
4.  **创建配件 (管理员权限)：**
    * `POST /api/parts`，Body: `{ "partNumber": "COMP-001", "partName": "空调压缩机", "price": 800.00, "stock": 50, "brandId": "your_brand_id" }`
5.  **添加配件兼容性 (管理员权限)：**
    * `POST /api/parts/compatibility`，Body: `{ "partId": "your_part_id", "productModelId": "your_model_id", "notes": "适用于2023年款该型号空调" }`
6.  **用户评价订单：**
    * 确保订单已完成 (`OrderStatus.COMPLETED`) 且未被评价过。
    * 使用普通用户账号登录。
    * `POST /api/reviews`，Body: `{ "orderId": "your_order_id", "rating": 5, "content": "技师服务很好，非常专业！", "imageUrls": ["http://example.com/review_photo.jpg"], "isAnonymous": false }`
7.  **管理员/技师查看评价列表：**
    * `GET /api/reviews`
    * `GET /api/reviews?technicianId={your_technician_id}` (技师可查看自己的评价)
    * `GET /api/reviews?isPublished=false` (管理员可查看未发布的评价)
8.  **技师回复评价：**
    * 使用被评价技师账号登录。
    * `POST /api/reviews/{reviewId}/reply/technician`，Body: `{ "replyContent": "感谢您的肯定，我们会继续努力！" }`
9.  **管理员回复评价：**
    * 使用管理员账号登录。
    * `POST /api/reviews/{reviewId}/reply/admin`，Body: `{ "replyContent": "您好，感谢您的反馈！" }`
10. **管理员审核评价 (发布/取消发布)：**
    * `PUT /api/reviews/{reviewId}/publish`，Body: `{ "isPublished": false }` 或 `{ "isPublished": true }`

---

**本篇总结：**

在第六篇教程中，我们成功地为“家维在线系统”引入了**配件管理**和**评价模块**。我们设计了详细的配件、品牌、型号及兼容性实体，并实现了相应的 CRUD API 和库存管理。同时，我们构建了用户评价服务、技师和管理员回复评价以及管理员审核评价的功能。这两个模块的加入，使得平台在提供维修服务的同时，能够更好地管理物资流转，并建立用户信任与服务质量反馈机制。

**展望下一篇：**

在下一篇（第七篇）中，我们将专注于**财务管理与消息通知**。我们将探讨技师佣金计算、提现流程，以及系统内部消息通知机制，这些都是保证平台正常运营的关键环节。

---

您对这第六篇的详细内容和代码实现满意吗？如果满意，我们就可以准备开始第七篇了。