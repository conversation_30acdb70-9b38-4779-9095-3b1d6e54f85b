### **《新一代在线家政维修服务平台：从需求到实现的企业级开发实战》**

#### **第二篇：数据库设计与核心实体建模**

**摘要：** 基于需求规格说明书的详细分析，本篇将构建一个支持多角色协同、AI智能匹配、创新增值功能的完整数据模型。我们将设计用户体系、服务商管理、服务分类、订单流程、支付结算等核心实体，并使用TypeORM实现企业级的数据库架构。

---

## **2.1 需求分析与数据模型设计**

### **核心业务实体分析**

基于需求规格说明书，我们的平台需要支持以下核心业务：

**用户体系：**
- 终端用户（客户）：寻找和预订家政维修服务
- 服务提供商：提供具体家政维修服务的个人或商家
- 平台管理员：负责平台运营、管理和维护

**业务流程：**
- 服务发现与匹配：AI智能推荐、地理位置匹配
- 预订与排期：灵活的时间选择和排期管理
- 订单全周期管理：创建、跟踪、状态更新、完成
- 支付与结算：托管支付、自动结算、佣金计算
- 评价与反馈：多维度评价、信誉体系

### **实体关系设计（ERD）**

```mermaid
erDiagram
    USERS {
        bigint id PK
        string username
        string phone
        string email
        string password_hash
        enum role
        enum status
        string avatar_url
        string real_name
        string id_card
        timestamp created_at
        timestamp updated_at
    }

    USER_PROFILES {
        bigint id PK
        bigint user_id FK
        string nickname
        enum gender
        date birth_date
        text address
        string city
        string district
        decimal longitude
        decimal latitude
        json preferences
        timestamp created_at
        timestamp updated_at
    }

    SERVICE_PROVIDERS {
        bigint id PK
        bigint user_id FK
        string business_name
        string business_license
        enum certification_status
        text certification_note
        int service_radius
        decimal base_longitude
        decimal base_latitude
        json working_hours
        text introduction
        int experience_years
        int total_orders
        decimal rating_average
        int rating_count
        timestamp created_at
        timestamp updated_at
    }

    PROVIDER_CERTIFICATIONS {
        bigint id PK
        bigint provider_id FK
        string certification_type
        string certification_name
        string certificate_number
        string certificate_url
        date issue_date
        date expire_date
        enum status
        timestamp created_at
    }

    SERVICE_CATEGORIES {
        bigint id PK
        bigint parent_id FK
        string name
        string code
        text description
        string icon_url
        int sort_order
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    SERVICE_ITEMS {
        bigint id PK
        bigint category_id FK
        string name
        text description
        enum pricing_type
        decimal base_price
        string unit
        int duration_minutes
        text requirements
        json images
        json tags
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    ORDERS {
        bigint id PK
        string order_no
        bigint customer_id FK
        bigint provider_id FK
        bigint service_item_id FK
        enum status
        string service_name
        text service_description
        timestamp appointment_time
        int estimated_duration
        text service_address
        string contact_name
        string contact_phone
        decimal longitude
        decimal latitude
        decimal base_price
        decimal additional_fee
        decimal discount_amount
        decimal total_amount
        decimal platform_fee
        timestamp created_at
        timestamp confirmed_at
        timestamp started_at
        timestamp completed_at
        timestamp cancelled_at
        text customer_note
        text provider_note
        text cancel_reason
    }

    USERS ||--|| USER_PROFILES : has
    USERS ||--o| SERVICE_PROVIDERS : becomes
    SERVICE_PROVIDERS ||--o{ PROVIDER_CERTIFICATIONS : has
    SERVICE_CATEGORIES ||--o{ SERVICE_CATEGORIES : contains
    SERVICE_CATEGORIES ||--o{ SERVICE_ITEMS : contains
    USERS ||--o{ ORDERS : places
    SERVICE_PROVIDERS ||--o{ ORDERS : handles
    SERVICE_ITEMS ||--o{ ORDERS : provides
```

## **2.2 TypeORM实体定义**

### **用户基础信息实体**

```typescript
// src/database/entities/user.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  OneToMany,
  Index,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { UserProfile } from './user-profile.entity';
import { ServiceProvider } from './service-provider.entity';
import { Order } from './order.entity';

export enum UserRole {
  CUSTOMER = 'customer',
  PROVIDER = 'provider',
  ADMIN = 'admin',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BANNED = 'banned',
}

@Entity('users')
@Index(['phone'])
@Index(['email'])
@Index(['role'])
export class User {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ length: 50, unique: true })
  username: string;

  @Column({ length: 20, unique: true })
  phone: string;

  @Column({ length: 100, unique: true, nullable: true })
  email: string;

  @Column({ name: 'password_hash', length: 255 })
  @Exclude()
  passwordHash: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.CUSTOMER,
  })
  role: UserRole;

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
  })
  status: UserStatus;

  @Column({ name: 'avatar_url', length: 500, nullable: true })
  avatarUrl: string;

  @Column({ name: 'real_name', length: 50, nullable: true })
  realName: string;

  @Column({ name: 'id_card', length: 18, nullable: true })
  @Exclude()
  idCard: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @OneToOne(() => UserProfile, (profile) => profile.user, { cascade: true })
  profile: UserProfile;

  @OneToOne(() => ServiceProvider, (provider) => provider.user)
  serviceProvider: ServiceProvider;

  @OneToMany(() => Order, (order) => order.customer)
  orders: Order[];

  constructor(partial: Partial<User>) {
    Object.assign(this, partial);
  }
}
```

### **用户详细信息实体**

```typescript
// src/database/entities/user-profile.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  UNKNOWN = 'unknown',
}

@Entity('user_profiles')
@Index(['longitude', 'latitude'])
export class UserProfile {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'user_id', type: 'bigint' })
  userId: number;

  @Column({ length: 50, nullable: true })
  nickname: string;

  @Column({
    type: 'enum',
    enum: Gender,
    default: Gender.UNKNOWN,
  })
  gender: Gender;

  @Column({ name: 'birth_date', type: 'date', nullable: true })
  birthDate: Date;

  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ length: 50, nullable: true })
  city: string;

  @Column({ length: 50, nullable: true })
  district: string;

  @Column({ type: 'decimal', precision: 10, scale: 7, nullable: true })
  longitude: number;

  @Column({ type: 'decimal', precision: 10, scale: 7, nullable: true })
  latitude: number;

  @Column({ type: 'json', nullable: true })
  preferences: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @OneToOne(() => User, (user) => user.profile, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;
}
```

### **服务商信息实体**

```typescript
// src/database/entities/service-provider.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { ProviderCertification } from './provider-certification.entity';
import { ProviderService } from './provider-service.entity';
import { Order } from './order.entity';

export enum CertificationStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

@Entity('service_providers')
@Index(['certification_status'])
@Index(['base_longitude', 'base_latitude'])
@Index(['rating_average'])
export class ServiceProvider {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'user_id', type: 'bigint' })
  userId: number;

  @Column({ name: 'business_name', length: 100, nullable: true })
  businessName: string;

  @Column({ name: 'business_license', length: 100, nullable: true })
  businessLicense: string;

  @Column({
    name: 'certification_status',
    type: 'enum',
    enum: CertificationStatus,
    default: CertificationStatus.PENDING,
  })
  certificationStatus: CertificationStatus;

  @Column({ name: 'certification_note', type: 'text', nullable: true })
  certificationNote: string;

  @Column({ name: 'service_radius', type: 'int', default: 10 })
  serviceRadius: number;

  @Column({ name: 'base_longitude', type: 'decimal', precision: 10, scale: 7, nullable: true })
  baseLongitude: number;

  @Column({ name: 'base_latitude', type: 'decimal', precision: 10, scale: 7, nullable: true })
  baseLatitude: number;

  @Column({ name: 'working_hours', type: 'json', nullable: true })
  workingHours: Record<string, any>;

  @Column({ type: 'text', nullable: true })
  introduction: string;

  @Column({ name: 'experience_years', type: 'int', default: 0 })
  experienceYears: number;

  @Column({ name: 'total_orders', type: 'int', default: 0 })
  totalOrders: number;

  @Column({ name: 'rating_average', type: 'decimal', precision: 3, scale: 2, default: 0.00 })
  ratingAverage: number;

  @Column({ name: 'rating_count', type: 'int', default: 0 })
  ratingCount: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @OneToOne(() => User, (user) => user.serviceProvider, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany(() => ProviderCertification, (cert) => cert.provider, { cascade: true })
  certifications: ProviderCertification[];

  @OneToMany(() => ProviderService, (service) => service.provider, { cascade: true })
  services: ProviderService[];

  @OneToMany(() => Order, (order) => order.provider)
  orders: Order[];
}
```

### **服务商认证实体**

```typescript
// src/database/entities/provider-certification.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ServiceProvider } from './service-provider.entity';

export enum CertificationStatusEnum {
  VALID = 'valid',
  EXPIRED = 'expired',
  REVOKED = 'revoked',
}

@Entity('provider_certifications')
@Index(['provider_id'])
@Index(['certification_type'])
export class ProviderCertification {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'provider_id', type: 'bigint' })
  providerId: number;

  @Column({ name: 'certification_type', length: 50 })
  certificationType: string;

  @Column({ name: 'certification_name', length: 100 })
  certificationName: string;

  @Column({ name: 'certificate_number', length: 100, nullable: true })
  certificateNumber: string;

  @Column({ name: 'certificate_url', length: 500, nullable: true })
  certificateUrl: string;

  @Column({ name: 'issue_date', type: 'date', nullable: true })
  issueDate: Date;

  @Column({ name: 'expire_date', type: 'date', nullable: true })
  expireDate: Date;

  @Column({
    type: 'enum',
    enum: CertificationStatusEnum,
    default: CertificationStatusEnum.VALID,
  })
  status: CertificationStatusEnum;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => ServiceProvider, (provider) => provider.certifications, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'provider_id' })
  provider: ServiceProvider;
}
```

## **2.3 数据库关系与约束**

### **服务分类与项目实体**

```typescript
// src/database/entities/service-category.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { ServiceItem } from './service-item.entity';

@Entity('service_categories')
@Index(['parent_id'])
@Index(['code'])
@Index(['is_active'])
export class ServiceCategory {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'parent_id', type: 'bigint', nullable: true })
  parentId: number;

  @Column({ length: 100 })
  name: string;

  @Column({ length: 50, unique: true })
  code: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'icon_url', length: 500, nullable: true })
  iconUrl: string;

  @Column({ name: 'sort_order', type: 'int', default: 0 })
  sortOrder: number;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => ServiceCategory, (category) => category.children)
  @JoinColumn({ name: 'parent_id' })
  parent: ServiceCategory;

  @OneToMany(() => ServiceCategory, (category) => category.parent)
  children: ServiceCategory[];

  @OneToMany(() => ServiceItem, (item) => item.category)
  items: ServiceItem[];
}
```

通过本篇教程，我们建立了一个完整、灵活、可扩展的数据库架构，完全基于需求规格说明书的业务需求。这个设计不仅满足当前功能，还为AI智能匹配、创新增值服务等高级功能预留了充足空间。

---

**[请告诉我"继续"，我将提供第三篇：用户管理与多角色认证系统。]**
