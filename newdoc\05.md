好的，我们继续进行定制化系列教程的第五篇。在之前的篇章中，我们已经完成了项目初始化、数据库基础设计、用户和技师的身份认证与权限管理，以及服务与订单的基础管理。现在，我们将进入更复杂的业务流程——**维修工作流与派单**。

---

### **《新一代在线家政维修服务平台：从需求到实现的高效开发实战》**

#### **定制化系列教程 第五篇：核心业务模块二：维修工作流与派单**

**摘要：** 本篇教程将深入讲解家维在线系统的核心操作流程。我们将实现订单的智能/手动派发给技师，以及技师在接到订单后的服务全生命周期管理，包括接受/拒绝订单、开始服务、提交现场评估和报价、上传照片、完成服务等。这将是连接用户需求与技师服务的关键环节。

---

### **5.1 派单模块 (Dispatch Module)**

派单是连接用户订单与可用技师的关键环节。初期我们先实现简单的派单逻辑，后续可以考虑引入更复杂的智能匹配算法。

在第四篇的 `OrderService` 中，我们已经实现了技师接受/拒绝订单以及管理员手动派单的功能。这里我们将对其进行进一步完善和扩展，并考虑如何获取可用的技师列表。

#### **5.1.1 派单策略：手动派单与初步逻辑**

最简单的派单策略是管理员手动选择一个技师进行派单。更进一步，我们可以根据服务类型、技师的地理位置、空闲时间、技能标签等进行初步筛选。

**1. 完善 `OrderService` 中的派单相关逻辑：**

在 `src/order/order.service.ts` 中，我们已经有 `updateOrderStatusByAdmin` 方法可以实现管理员手动派单。为了更好地管理派单，我们可以增加一个专门的方法来处理派单逻辑。

```typescript
// src/order/order.service.ts (在 OrderService 中添加或修改)

// ... 其他导入和 OrderService 构造函数

@Injectable()
export class OrderService {
  constructor(
    @InjectRepository(Order)
    private orderRepository: Repository<Order>,
    private userService: UserService,
    private serviceService: ServiceService,
    private technicianService: TechnicianService, // 注入技师服务
  ) {}

  // ... (之前的 createOrder, getUserOrders, getOrderDetail, cancelOrder, simulatePaymentSuccess, getAllOrders, applyAfterSales)

  /**
   * 管理员手动派单
   * @param orderNumber 订单号
   * @param technicianId 目标技师ID
   * @returns 更新后的订单
   */
  async assignOrderToTechnician(orderNumber: string, technicianId: string): Promise<Order> {
    const order = await this.orderRepository.findOne({ where: { orderNumber } });
    if (!order) {
      throw new NotFoundException(`订单号 ${orderNumber} 未找到`);
    }

    if (order.status !== OrderStatus.PENDING_ASSIGNMENT) {
      throw new BadRequestException(`订单当前状态为 "${order.status}"，无法派单。必须是待安排状态。`);
    }

    const technician = await this.technicianService.findOne(technicianId);
    if (!technician) {
      throw new NotFoundException(`技师 ID 为 ${technicianId} 不存在`);
    }
    if (technician.status !== 1) { // 假设技师状态 1 为可用
        throw new BadRequestException(`技师 ${technician.username} 当前不可用。`);
    }
    // 可以在此添加更多复杂的检查，例如技师服务区域、技能是否匹配服务类型、技师日程冲突等

    order.technicianId = technicianId;
    order.technicianAccepted = false; // 派单后技师需重新接受
    order.status = OrderStatus.PENDING_ASSIGNMENT; // 派单后订单状态仍为待安排 (等待技师接受)
    order.dispatchTime = new Date(); // 记录派单时间

    return this.orderRepository.save(order);
  }

  // ... (getPendingTechnicianOrders, acceptOrder, rejectOrder, updateOrderStatusByAdmin, startService, completeService)
}
```

**2. 在 `OrderController` 中添加管理员派单 API：**

```typescript
// src/order/order.controller.ts (在 OrderController 中添加)

// ... 其他导入

@Controller('api/order')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  // ... (之前的用户端 API 和技师端 API)

  // --- 管理员端 API ---

  // ... (getAllOrders, updateOrderStatusByAdmin)

  /**
   * 管理员手动派单
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin', 'customer_service') // 管理员和客服可以派单
  @Post('admin/:orderNumber/assign/:technicianId')
  async assignOrder(
    @Param('orderNumber') orderNumber: string,
    @Param('technicianId') technicianId: string,
  ) {
    const updatedOrder = await this.orderService.assignOrderToTechnician(orderNumber, technicianId);
    return {
      statusCode: HttpStatus.OK,
      message: '订单派单成功，等待技师接受',
      data: updatedOrder,
    };
  }
}
```

#### **5.1.2 获取可用技师列表 (管理员端)**

为了管理员能方便地进行派单，需要提供一个接口来获取可用的技师列表。这部分逻辑可以在 `TechnicianService` 中实现。

**1. 在 `TechnicianService` 中添加获取可用技师的方法：**

```typescript
// src/technician/technician.service.ts (在 TechnicianService 中添加)
// ... 其他导入

@Injectable()
export class TechnicianService {
  constructor(
    @InjectRepository(Technician)
    private technicianRepository: Repository<Technician>,
  ) {}

  // ... (之前的 findOne, create, update, remove, login 等方法)

  /**
   * 获取所有可用技师列表 (可根据服务类型或区域筛选)
   * @param serviceCategoryIds 可选：按服务分类ID过滤
   * @param area 可选：按服务区域过滤
   */
  async findAvailableTechnicians(serviceCategoryIds?: string[], area?: string): Promise<Technician[]> {
    const queryBuilder = this.technicianRepository.createQueryBuilder('technician')
      .where('technician.status = :status', { status: 1 }) // 假设 1 表示可用状态
      .andWhere('technician.isApproved = :isApproved', { isApproved: true }); // 确保已审核

    if (serviceCategoryIds && serviceCategoryIds.length > 0) {
      // 假设技师实体中有 skills 或 serviceCategories 字段来关联其擅长的服务
      // 这里需要根据您的技师实体设计来调整查询逻辑
      // 简单示例：如果技师有 tags 字段包含服务分类ID
      queryBuilder.andWhere('JSON_CONTAINS(technician.tags, JSON_ARRAY(:...serviceCategoryIds))', { serviceCategoryIds });
    }
    if (area) {
      // 假设技师实体有 serviceAreas 字段
      queryBuilder.andWhere('technician.serviceAreas LIKE :area', { area: `%${area}%` });
    }

    return queryBuilder.getMany();
  }
}
```

**注意：** 上述 `findAvailableTechnicians` 中的 `JSON_CONTAINS` 和 `LIKE` 语句需要根据您的 `Technician` 实体中如何存储技师技能或服务区域的字段类型来调整。如果技能是单独的关联表，则需要使用 `leftJoin` 和 `where` 条件。

**2. 在 `TechnicianController` 中添加获取可用技师的 API：**

```typescript
// src/technician/technician.controller.ts (在 TechnicianController 中添加)

// ... 其他导入

@Controller('api/technician')
export class TechnicianController {
  constructor(private readonly technicianService: TechnicianService) {}

  // ... (之前的登录、注册、获取信息等 API)

  /**
   * 管理员获取可用技师列表 (可带筛选条件)
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin', 'customer_service')
  @Get('admin/available')
  async getAvailableTechnicians(
    @Query('serviceCategoryIds') serviceCategoryIds?: string,
    @Query('area') area?: string,
  ) {
    let categoryIdsArray: string[] | undefined;
    if (serviceCategoryIds) {
      categoryIdsArray = serviceCategoryIds.split(','); // 假设传入的是逗号分隔的ID字符串
    }

    const technicians = await this.technicianService.findAvailableTechnicians(categoryIdsArray, area);
    return {
      statusCode: HttpStatus.OK,
      data: technicians,
    };
  }
}
```

### **5.2 维修工作流模块 (Repair Workflow Module)**

维修工作流是订单从被技师接受到最终完成收款的全过程。我们将在 `OrderService` 中定义并实现订单状态的流转逻辑，以及技师在不同阶段的操作。

#### **5.2.1 订单状态流转逻辑**

在 `OrderService` 中，我们已经定义了 `OrderStatus` 枚举。现在我们将细化每个状态之间的转换以及对应的业务逻辑。

* **`PENDING_PAYMENT` (待支付)**: 用户创建订单后的初始状态。
* **`PENDING_ASSIGNMENT` (待安排/待派单)**: 支付成功后，等待管理员派单或技师接受。
* **`PENDING_SERVICE` (待服务)**: 技师接受订单后，等待上门服务。
* **`IN_SERVICE` (服务中)**: 技师抵达现场并开始服务。
* **`COMPLETED` (已完成)**: 技师完成服务并提交最终结果。
* **`CANCELED` (已取消)**: 用户或管理员取消订单。
* **`AFTER_SALES` (售后中)**: 用户发起售后申请。
* **`REFUNDING` (退款中)**: 进入退款流程。
* **`REFUNDED` (已退款)**: 退款完成。

#### **5.2.2 技师端工作流操作**

在第四篇中，我们已经实现了技师的**接受/拒绝订单**、**开始服务**和**完成服务**。现在，我们将补充**提交现场评估/报价**和**上传照片**。

**1. 完善 `OrderService` 中的技师工作流方法：**

在 `src/order/order.service.ts` 中添加新的方法：

```typescript
// src/order/order.service.ts (在 OrderService 中添加或修改)

// ... (之前的 createOrder, getUserOrders, getOrderDetail, cancelOrder, simulatePaymentSuccess, getAllOrders, applyAfterSales,
//        getPendingTechnicianOrders, acceptOrder, rejectOrder, assignOrderToTechnician, updateOrderStatusByAdmin, startService, completeService)

  /**
   * 技师提交现场评估和报价
   * 只能在“待服务”或“服务中”状态下进行
   * @param orderNumber 订单号
   * @param technicianId 技师ID
   * @param assessmentDetails 现场评估详情
   * @param quotedPrice 报价金额
   * @param quotedParts 配件清单及价格 (可选)
   * @returns 更新后的订单
   */
  async submitFieldAssessmentAndQuote(
    orderNumber: string,
    technicianId: string,
    assessmentDetails: string,
    quotedPrice: number,
    quotedParts?: { partName: string; quantity: number; unitPrice: number }[], // 可以是更复杂的DTO
  ): Promise<Order> {
    const order = await this.orderRepository.findOne({
      where: {
        orderNumber,
        technicianId,
        status: OrderStatus.PENDING_SERVICE || OrderStatus.IN_SERVICE, // 允许在待服务或服务中提交
      },
    });

    if (!order) {
      throw new NotFoundException('订单未找到，或您无权操作，或订单状态不允许提交评估/报价');
    }

    if (quotedPrice <= 0) {
        throw new BadRequestException('报价金额必须大于0');
    }

    order.assessmentDetails = assessmentDetails;
    order.quotedPrice = quotedPrice;
    order.quotedParts = quotedParts; // 存储配件信息
    order.quoteTime = new Date(); // 记录报价时间
    // 此时订单状态可能仍为待服务或服务中，等待用户确认报价
    // 可以在这里增加一个状态：等待用户确认报价 (OrderStatus.PENDING_QUOTE_CONFIRMATION)
    // 这里简化处理，不改变主状态，只记录报价信息，用户端根据 quotedPrice 显示待确认
    return this.orderRepository.save(order);
  }

  /**
   * 技师上传服务相关照片 (如服务前、中、后的照片)
   * @param orderNumber 订单号
   * @param technicianId 技师ID
   * @param photoUrls 新增的照片URL列表
   * @returns 更新后的订单
   */
  async uploadServicePhotos(
    orderNumber: string,
    technicianId: string,
    photoUrls: string[],
  ): Promise<Order> {
    const order = await this.orderRepository.findOne({
      where: {
        orderNumber,
        technicianId,
        status: OrderStatus.IN_SERVICE || OrderStatus.COMPLETED, // 允许在服务中或完成后上传
      },
    });

    if (!order) {
      throw new NotFoundException('订单未找到，或您无权操作，或订单状态不允许上传照片');
    }

    if (!photoUrls || photoUrls.length === 0) {
      throw new BadRequestException('照片URL列表不能为空');
    }

    // 合并现有照片和新上传的照片，并去重
    const currentPhotos = order.servicePhotos || [];
    order.servicePhotos = [...new Set([...currentPhotos, ...photoUrls])]; // 假设 servicePhotos 是 string[] 类型

    return this.orderRepository.save(order);
  }

  /**
   * 用户确认报价
   * @param orderNumber 订单号
   * @param userId 用户ID
   * @param isAccepted 是否接受报价
   * @returns 更新后的订单
   */
  async confirmQuote(orderNumber: string, userId: string, isAccepted: boolean): Promise<Order> {
    const order = await this.orderRepository.findOne({ where: { orderNumber, userId } });
    if (!order) {
      throw new NotFoundException('订单未找到或您无权操作');
    }

    if (!order.quotedPrice) {
      throw new BadRequestException('当前订单没有待确认的报价');
    }

    if (isAccepted) {
      // 用户接受报价，可以更新订单金额为报价金额，并继续服务
      order.amount = order.quotedPrice; // 更新订单金额为报价金额
      order.quoteAccepted = true;
      // 状态可能从 PENDING_SERVICE 或 IN_SERVICE 变为 IN_SERVICE （如果之前不是的话）
      if (order.status === OrderStatus.PENDING_SERVICE) {
        order.status = OrderStatus.IN_SERVICE; // 接受报价后，正式进入服务中
      }
      // 这里可以考虑生成一个新的支付单或要求用户支付差额
    } else {
      // 用户拒绝报价
      order.quoteAccepted = false;
      order.quotedPrice = null; // 清除报价
      order.assessmentDetails = null; // 清除评估
      order.quotedParts = null;
      // 订单状态可能回到 PENDING_SERVICE 或甚至 CANCELED (如果用户选择不服务)
      // 根据业务需求决定拒绝报价后的状态，例如可以回到待安排，或直接变为取消
      // 简单处理：如果拒绝，回到待安排，让管理员重新处理或用户取消
      order.status = OrderStatus.PENDING_ASSIGNMENT;
      order.remarks = `用户拒绝了技师的报价：${order.quotedPrice}元。`;
    }

    return this.orderRepository.save(order);
  }

  /**
   * 技师确认收款（服务完成后的最终确认）
   * @param orderNumber 订单号
   * @param technicianId 技师ID
   */
  async confirmPaymentReceived(orderNumber: string, technicianId: string): Promise<Order> {
    const order = await this.orderRepository.findOne({
      where: {
        orderNumber,
        technicianId,
        status: OrderStatus.COMPLETED, // 确保订单已完成
        payStatus: PayStatus.PAID, // 确保用户已支付
      },
    });

    if (!order) {
      throw new NotFoundException('订单未找到，或您无权操作，或订单状态不允许确认收款');
    }

    // 这里假设技师确认收款后，佣金计算等财务流程启动
    // 可以在这里更新一个字段表示技师已确认收款
    order.technicianConfirmedPayment = true; // 新增一个字段表示技师已确认收款

    return this.orderRepository.save(order);
  }
}
```

**2. 在 `OrderController` 中添加相应的 API：**

```typescript
// src/order/order.controller.ts (在 OrderController 中添加或修改)

// ... 其他导入

@Controller('api/order')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  // ... (之前的用户端 API, 技师端 API, 管理员端 API)

  // --- 技师端工作流 API ---

  // ... (getPendingTechnicianOrders, acceptOrder, rejectOrder, startService, completeService)

  /**
   * 技师提交现场评估和报价
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('technician')
  @Post('technician/:orderNumber/assessment')
  async submitFieldAssessment(
    @CurrentUser('technicianId') technicianId: string,
    @Param('orderNumber') orderNumber: string,
    @Body() assessmentDto: TechnicianAssessmentDto, // 新的 DTO
  ) {
    const updatedOrder = await this.orderService.submitFieldAssessmentAndQuote(
      orderNumber,
      technicianId,
      assessmentDto.assessmentDetails,
      assessmentDto.quotedPrice,
      assessmentDto.quotedParts,
    );
    return {
      statusCode: HttpStatus.OK,
      message: '现场评估和报价已提交',
      data: updatedOrder,
    };
  }

  /**
   * 技师上传服务照片
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('technician')
  @Post('technician/:orderNumber/upload-photos')
  async uploadServicePhotos(
    @CurrentUser('technicianId') technicianId: string,
    @Param('orderNumber') orderNumber: string,
    @Body() photoUploadDto: { photoUrls: string[] }, // 简单 DTO
  ) {
    const updatedOrder = await this.orderService.uploadServicePhotos(
      orderNumber,
      technicianId,
      photoUploadDto.photoUrls,
    );
    return {
      statusCode: HttpStatus.OK,
      message: '服务照片上传成功',
      data: updatedOrder,
    };
  }

  /**
   * 技师确认收款
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('technician')
  @Post('technician/:orderNumber/confirm-payment')
  async confirmPaymentReceived(
    @CurrentUser('technicianId') technicianId: string,
    @Param('orderNumber') orderNumber: string,
  ) {
    const updatedOrder = await this.orderService.confirmPaymentReceived(orderNumber, technicianId);
    return {
      statusCode: HttpStatus.OK,
      message: '技师已确认收款',
      data: updatedOrder,
    };
  }

  // --- 用户端工作流 API (用户确认报价) ---
  /**
   * 用户确认技师报价
   */
  @UseGuards(AuthGuard('jwt'))
  @Post(':orderNumber/confirm-quote')
  async confirmQuote(
    @CurrentUser('userId') userId: string,
    @Param('orderNumber') orderNumber: string,
    @Body('isAccepted') isAccepted: boolean,
  ) {
    const updatedOrder = await this.orderService.confirmQuote(orderNumber, userId, isAccepted);
    return {
      statusCode: HttpStatus.OK,
      message: isAccepted ? '您已接受技师报价' : '您已拒绝技师报价',
      data: updatedOrder,
    };
  }
}
```

**3. 定义新的订单相关 DTOs (src/order/dto/):**

我们需要为技师提交评估和上传照片，以及用户确认报价定义新的 DTO。

```typescript
// src/order/dto/technician-assessment.dto.ts
import { IsNotEmpty, IsString, IsNumber, IsOptional, IsArray, Min } from 'class-validator';
import { Type } from 'class-transformer'; // 用于嵌套对象转换

// 定义配件 DTO (如果需要更细致的配件信息)
export class QuotedPartDto {
  @IsNotEmpty({ message: '配件名称不能为空' })
  @IsString({ message: '配件名称必须是字符串' })
  partName: string;

  @IsNotEmpty({ message: '数量不能为空' })
  @IsNumber({}, { message: '数量必须是数字' })
  @Min(1, { message: '数量不能小于1' })
  quantity: number;

  @IsNotEmpty({ message: '单价不能为空' })
  @IsNumber({}, { message: '单价必须是数字' })
  @Min(0, { message: '单价不能为负数' })
  unitPrice: number;
}

export class TechnicianAssessmentDto {
  @IsNotEmpty({ message: '评估详情不能为空' })
  @IsString({ message: '评估详情必须是字符串' })
  assessmentDetails: string;

  @IsNotEmpty({ message: '报价金额不能为空' })
  @IsNumber({}, { message: '报价金额必须是数字' })
  @Min(0, { message: '报价金额不能为负数' })
  quotedPrice: number;

  @IsOptional()
  @IsArray()
  @Type(() => QuotedPartDto) // 嵌套 DTO 转换
  quotedParts?: QuotedPartDto[];
}
```

```typescript
// src/order/dto/photo-upload.dto.ts
import { IsNotEmpty, IsArray, IsUrl } from 'class-validator';

export class PhotoUploadDto {
  @IsNotEmpty({ message: '照片URL列表不能为空' })
  @IsArray({ message: '照片URL必须是数组' })
  @IsUrl({}, { each: true, message: '每张照片URL格式不正确' })
  photoUrls: string[];
}
```

**4. 更新 `Order` 实体 (src/order/entities/order.entity.ts) 以支持新增字段：**

```typescript
// src/order/entities/order.entity.ts (在 Order 实体中添加或修改)

import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../user/entities/user.entity';
import { Service } from '../../service/entities/service.entity';
import { Technician } from '../../technician/entities/technician.entity';
import { OrderStatus, PayStatus } from '../order.service'; // 导入枚举

@Entity('orders')
export class Order {
  @PrimaryGeneratedColumn('uuid')
  id: string; // 内部唯一ID

  @Column({ unique: true, length: 50 })
  orderNumber: string; // 订单号，业务ID

  @Column({ length: 50 })
  userId: string;

  @ManyToOne(() => User, user => user.orders)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ length: 50 })
  serviceId: string;

  @ManyToOne(() => Service, service => service.orders)
  @JoinColumn({ name: 'serviceId' })
  service: Service;

  @Column({ length: 100 })
  serviceName: string; // 冗余，方便查询显示

  @Column({ type: 'datetime' })
  appointmentTime: Date; // 预约服务时间

  @Column({ length: 255 })
  address: string;

  @Column({ length: 50 })
  contact: string;

  @Column({ length: 20 })
  phone: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number; // 订单初始金额

  @Column({ type: 'enum', enum: OrderStatus, default: OrderStatus.PENDING_PAYMENT })
  status: OrderStatus; // 订单状态

  @Column({ type: 'enum', enum: PayStatus, default: PayStatus.UNPAID })
  payStatus: PayStatus; // 支付状态 (0:未支付, 1:已支付, 2:支付失败)

  @Column({ type: 'text', nullable: true })
  remarks: string; // 订单备注

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;

  @Column({ type: 'datetime', nullable: true })
  payTime: Date; // 支付时间

  @Column({ length: 50, nullable: true })
  technicianId: string; // 派单技师ID

  @ManyToOne(() => Technician, technician => technician.assignedOrders)
  @JoinColumn({ name: 'technicianId' })
  technician: Technician;

  @Column({ default: false })
  technicianAccepted: boolean; // 技师是否已接受派单

  @Column({ type: 'datetime', nullable: true })
  dispatchTime: Date; // 派单时间

  // --- 新增的维修工作流字段 ---
  @Column({ type: 'text', nullable: true })
  assessmentDetails: string; // 现场评估详情

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  quotedPrice: number; // 技师报价金额

  @Column({ type: 'json', nullable: true }) // 存储 JSON 格式的配件列表
  quotedParts: { partName: string; quantity: number; unitPrice: number }[];

  @Column({ type: 'datetime', nullable: true })
  quoteTime: Date; // 报价时间

  @Column({ default: false })
  quoteAccepted: boolean; // 用户是否接受报价

  @Column({ type: 'datetime', nullable: true })
  actualServiceStartTime: Date; // 实际服务开始时间

  @Column({ type: 'datetime', nullable: true })
  actualServiceEndTime: Date; // 实际服务结束时间

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  actualAmount: number; // 实际服务金额 (可能与amount不同，包含了报价/配件费)

  @Column({ type: 'text', nullable: true })
  serviceDetails: string; // 技师提交的服务总结

  @Column({ type: 'json', nullable: true }) // 存储 JSON 格式的图片URL数组
  servicePhotos: string[]; // 服务过程中的照片（如：before, during, after）

  @Column({ type: 'json', nullable: true }) // 存储 JSON 格式的图片URL数组
  completionPhotos: string[]; // 服务完成后的照片

  @Column({ default: false })
  technicianConfirmedPayment: boolean; // 技师是否已确认收款
}
```

**重要提示：**

* 在 `Order` 实体中添加新的列后，您需要运行 TypeORM 的迁移命令来更新数据库结构。
    * 首先，生成迁移文件：`npx typeorm migration:generate -n AddWorkflowFieldsToOrder`
    * 然后，执行迁移：`npx typeorm migration:run`
* `quotedParts` 和 `servicePhotos` / `completionPhotos` 列使用了 `type: 'json'`。这在 MySQL 5.7+ 和 PostgreSQL 中是支持的。如果您的 MySQL 版本较低，或者您倾向于更结构化的数据，您可能需要为配件和照片创建独立的关联表。这里为了教程简化，先使用 JSON 类型。
* `Technician` 实体也需要添加一个 `isApproved` 字段，用于管理员审核技师，并修改其 `status` 字段以支持可用/不可用状态，如果之前没有的话。

---

**测试与验证：**

1.  **启动应用**并确保数据库已通过迁移更新。
2.  **管理员获取可用技师列表：**
    * `GET /api/technician/admin/available`
3.  **管理员手动派单：**
    * 确保订单已支付并处于 `PENDING_ASSIGNMENT` 状态。
    * `POST /api/order/admin/{orderNumber}/assign/{technicianId}` (替换 `{orderNumber}` 和 `{technicianId}`)
4.  **技师接受订单：**
    * 使用被派单的技师账号登录。
    * `POST /api/order/technician/{orderNumber}/accept`
5.  **技师开始服务：**
    * `POST /api/order/technician/{orderNumber}/start-service`
6.  **技师提交现场评估和报价：**
    * `POST /api/order/technician/{orderNumber}/assessment`
        * Body: `{ "assessmentDetails": "检测到压缩机故障，需更换，建议加氟。", "quotedPrice": 300.00, "quotedParts": [ { "partName": "压缩机", "quantity": 1, "unitPrice": 150 }, { "partName": "氟利昂", "quantity": 1, "unitPrice": 50 } ] }`
7.  **用户确认报价：**
    * 使用订单所属用户账号登录。
    * `POST /api/order/{orderNumber}/confirm-quote`
        * Body: `{ "isAccepted": true }` 或 `{ "isAccepted": false }`
8.  **技师上传服务照片：**
    * `POST /api/order/technician/{orderNumber}/upload-photos`
        * Body: `{ "photoUrls": ["http://your-image-server.com/photo_before.jpg", "http://your-image-server.com/photo_during.jpg"] }`
9.  **技师完成服务：**
    * `POST /api/order/technician/{orderNumber}/complete-service`
        * Body: `{ "actualAmount": 300.00, "serviceDetails": "已更换压缩机，加氟并测试，运行正常。", "completionPhotos": ["http://your-image-server.com/photo_after.jpg"] }`
10. **技师确认收款：**
    * `POST /api/order/technician/{orderNumber}/confirm-payment`

---

**本篇总结：**

在第五篇教程中，我们成功地构建了“家维在线系统”的**维修工作流与派单**模块。我们实现了管理员手动派单的功能，并细化了技师从接受订单到完成服务、提交评估、报价和上传照片的整个业务流程。通过对订单状态的精确管理和技师操作的API化，系统能够更好地追踪服务进度。

**展望下一篇：**

在下一篇（第六篇）中，我们将继续深入，重点关注**配件与评价模块**。我们将设计并实现配件的库存管理、价格管理、品牌型号关联，以及用户对服务的评价功能，从而完善整个服务闭环。

---

您对这第五篇的详细内容和代码实现满意吗？如果满意，我们就可以准备开始第六篇了。