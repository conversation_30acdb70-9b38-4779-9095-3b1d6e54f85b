好的，我们继续进行定制化系列教程的第三篇。在上一篇中，我们已经搭建了用户模块的基础CRUD API。但是，这些API目前都是公开的，任何人都可以访问。在本篇中，我们将引入身份认证和权限控制，确保只有经过验证的用户才能访问受保护的资源。

---

### **《新一代在线家政维修服务平台：从需求到实现的高效开发实战》**

#### **定制化系列教程 第三篇：安全与认证——JWT与RBAC实现**

**摘要：** 本篇教程将深入讲解如何为“家维在线系统”实现用户身份认证和权限管理。我们将使用 **JWT (JSON Web Tokens)** 作为主要的认证机制，并结合 **RBAC (Role-Based Access Control)** 来实现精细化的权限控制，确保系统安全和数据隔离。

---

### **3.1 用户身份认证 (JWT)**

JWT 是一种开放标准 (RFC 7519)，它定义了一种紧凑且自包含的方式，用于在各方之间安全地传输信息，因为它可以通过数字签名进行验证和信任。

**JWT 工作原理概览：**

1.  **用户登录：** 用户使用凭据（手机号/密码）向服务器发送登录请求。
2.  **服务器验证：** 服务器验证凭据。
3.  **生成JWT：** 如果验证成功，服务器使用密钥生成一个JWT。这个JWT通常包含用户的ID、角色等信息（称为Claims）。
4.  **返回JWT：** 服务器将JWT返回给客户端。
5.  **客户端存储：** 客户端（浏览器或移动应用）将JWT存储起来（通常在LocalStorage或SessionStorage中）。
6.  **后续请求：** 客户端在每次后续请求中，将JWT放在HTTP请求头 `Authorization` 字段中（通常格式为 `Bearer <JWT>`）发送给服务器。
7.  **服务器验证JWT：** 服务器接收到请求后，会验证JWT的签名和有效性（是否过期、是否被篡改等）。
8.  **访问资源：** 如果JWT有效，服务器允许客户端访问受保护的资源；否则，返回未授权错误。

**集成 NestJS `Passport` 和 `JWT` 策略：**

NestJS 推荐使用 `Passport` 库进行身份认证。`Passport` 是一个灵活且模块化的认证中间件，它支持多种认证策略（如JWT、本地策略、OAuth等）。

**1. 安装必要的包：**

在项目根目录（`home-repair-backend`）下，运行以下命令：

```bash
npm install @nestjs/passport passport passport-jwt @nestjs/jwt jsonwebtoken
npm install -D @types/passport-jwt @types/jsonwebtoken
```
* `@nestjs/passport`: NestJS 对 Passport 的集成。
* `passport`: Passport 核心库。
* `passport-jwt`: Passport 的 JWT 认证策略。
* `@nestjs/jwt`: NestJS 对 `jsonwebtoken` 库的封装，用于生成和验证JWT。
* `jsonwebtoken`: 用于实际生成和验证JWT的库。

**2. 配置 JWT 密钥：**

我们不应该将密钥硬编码到代码中。使用环境变量是最佳实践。
在项目根目录下创建 `.env` 文件（如果还没有的话），并添加一个密钥：

```
# .env
JWT_SECRET=superSecretJWTKeyForHomeRepairSystemDevelopment # 替换为更强壮的随机字符串，生产环境务必复杂
```
要使用 `.env` 文件，我们需要安装 `@nestjs/config` 包。

```bash
npm install @nestjs/config
```

然后在 `src/app.module.ts` 中导入 `ConfigModule`：

```typescript
// src/app.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config'; // 导入 ConfigModule
// ... 其他实体和模块导入

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true, // 使 ConfigModule 在整个应用中可用
      envFilePath: '.env', // 指定 .env 文件的路径
    }),
    TypeOrmModule.forRoot({
      // ... 数据库配置
    }),
    UserModule, // 导入 UserModule
    // ... 其他模块
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
```

**3. 创建 Auth 模块：**

我们将专门创建一个 `AuthModule` 来处理认证逻辑。

```bash
nest generate module auth
nest generate service auth
```

**4. 定义 JWT 策略 (src/auth/jwt.strategy.ts)：**

JWT 策略负责从请求中提取 JWT，验证它，并解析出用户信息。

```typescript
// src/auth/jwt.strategy.ts
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config'; // 导入 ConfigService
import { UserService } from '../user/user.service'; // 导入 UserService
import { User } from '../user/entities/user.entity'; // 导入 User 实体

// 定义 JWT Payload 接口
export interface JwtPayload {
  userId: string;
  phone: string;
  username: string;
  // 可以根据需要添加其他信息，例如角色
  roles?: string[];
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService, // 注入 ConfigService
    private userService: UserService, // 注入 UserService
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(), // 从 Authorization header 中提取 Token
      ignoreExpiration: false, // 不忽略过期时间
      secretOrKey: configService.get<string>('JWT_SECRET'), // 从环境变量获取 JWT 密钥
    });
  }

  // 验证方法：当 JWT 被成功验证和解码后，此方法会被调用
  // payload 是解码后的 JWT 内容
  async validate(payload: JwtPayload): Promise<User> {
    const user = await this.userService.findOne(payload.userId); // 根据 userId 查找用户
    if (!user) {
      throw new UnauthorizedException('用户认证失败');
    }
    // 返回的用户对象会附加到请求对象（req.user）上
    // 注意：这里可以根据需要返回 User 实体，或者只返回部分信息
    return user;
  }
}
```

**5. 配置 AuthModule (src/auth/auth.module.ts)：**

AuthModule 将导入 JWT 策略、Passport 模块和 JWT 模块，并提供 `AuthService`。

```typescript
// src/auth/auth.module.ts
import { Module } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config'; // 导入 ConfigService

import { AuthService } from './auth.service';
import { JwtStrategy } from './jwt.strategy';
import { UserService } from '../user/user.service'; // 导入 UserService
import { UserModule } from '../user/user.module'; // 导入 UserModule

@Module({
  imports: [
    UserModule, // 导入 UserModule 以便 AuthModule 可以使用 UserService
    PassportModule,
    JwtModule.registerAsync({ // 异步注册 JwtModule
      imports: [ConfigModule], // 依赖 ConfigModule
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'), // 从 ConfigService 获取密钥
        signOptions: { expiresIn: '1h' }, // JWT过期时间，例如1小时
      }),
      inject: [ConfigService], // 注入 ConfigService
    }),
  ],
  providers: [AuthService, JwtStrategy], // 注册 AuthService 和 JwtStrategy
  exports: [AuthService, JwtModule], // 导出 AuthService 和 JwtModule，以便其他模块使用
})
export class AuthModule {}
```

**6. 更新 AuthService (src/auth/auth.service.ts)：**

`AuthService` 将包含登录逻辑，生成 JWT。

```typescript
// src/auth/auth.service.ts
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { UserService } from '../user/user.service';
import { JwtService } from '@nestjs/jwt';
import { User } from '../user/entities/user.entity'; // 导入 User 实体
import { JwtPayload } from './jwt.strategy'; // 导入 JwtPayload 接口

@Injectable()
export class AuthService {
  constructor(
    private userService: UserService,
    private jwtService: JwtService,
  ) {}

  /**
   * 验证用户并生成JWT Token
   * @param phone 用户手机号
   * @param pass 用户密码
   * @returns 包含访问令牌的对象
   */
  async login(phone: string, pass: string): Promise<{ access_token: string, user: Omit<User, 'password'> }> {
    const user = await this.userService.validateUser(phone, pass); // 调用 UserService 验证密码
    if (!user) {
      throw new UnauthorizedException('手机号或密码错误');
    }
    // 如果验证成功，生成 JWT payload
    const payload: JwtPayload = {
      userId: user.userId,
      phone: user.phone,
      username: user.username,
      // 可以根据业务需求添加角色信息，例如：roles: ['user']
    };
    // 签名生成 JWT token
    return {
      access_token: this.jwtService.sign(payload),
      user: user, // 返回用户部分信息，不含密码
    };
  }
}
```

**7. 重构 UserController 中的登录 API：**

现在我们将使用 `AuthService` 来处理登录。

```typescript
// src/user/user.controller.ts (仅更新 login 部分)
// ... 其他导入保持不变
import { AuthService } from '../auth/auth.service'; // 导入 AuthService
import { AuthGuard } from '@nestjs/passport'; // 导入 AuthGuard

@Controller('api/user')
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly authService: AuthService, // 注入 AuthService
  ) {}

  // ... register 方法保持不变

  /**
   * 用户登录接口 (POST /api/user/login)
   * 重构后，由 AuthService 处理登录验证和 JWT 生成
   */
  @Post('login')
  async login(@Body() loginUserDto: LoginUserDto) {
    // 调用 AuthService 的 login 方法
    const result = await this.authService.login(loginUserDto.phone, loginUserDto.password);
    return {
      statusCode: HttpStatus.OK,
      message: '登录成功',
      data: result.user, // 返回用户部分信息
      token: result.access_token, // 返回 JWT Token
    };
  }

  /**
   * 获取用户信息接口 (GET /api/user/info)
   * 现在使用 JWT 守卫保护
   */
  @UseGuards(AuthGuard('jwt')) // 使用 JWT 守卫保护此路由
  @Get('info')
  async getUserInfo(@Request() req) {
    // req.user 会被 JwtStrategy.validate 方法填充，包含用户实体
    const user = req.user as User;
    const { password, ...result } = user; // 移除密码
    return {
      statusCode: HttpStatus.OK,
      data: result
    };
  }

  /**
   * 更新用户信息接口 (PUT /api/user/info)
   * 使用 JWT 守卫保护
   */
  @UseGuards(AuthGuard('jwt'))
  @Put('info')
  async updateUserInfo(@Request() req, @Body() updateDto: UpdateUserDto) {
    const userId = (req.user as User).userId; // 从 req.user 中获取 userId
    const updatedUser = await this.userService.updateUserInfo(userId, updateDto);
    const { password, ...result } = updatedUser; // 移除密码
    return {
      statusCode: HttpStatus.OK,
      message: '更新成功',
      data: result
    };
  }

  /**
   * 添加地址接口 (POST /api/user/address)
   * 使用 JWT 守卫保护
   */
  @UseGuards(AuthGuard('jwt'))
  @Post('address')
  async addAddress(@Request() req, @Body() addAddressDto: AddAddressDto) {
    const userId = (req.user as User).userId; // 从 req.user 中获取 userId
    const user = await this.userService.addAddress(userId, addAddressDto);
    const { password, ...result } = user; // 移除密码
    return {
      statusCode: HttpStatus.CREATED,
      message: '地址添加成功',
      data: result.addresses // 返回更新后的地址列表
    };
  }

  /**
   * 获取地址列表接口 (GET /api/user/addresses)
   * 使用 JWT 守卫保护
   */
  @UseGuards(AuthGuard('jwt'))
  @Get('addresses')
  async getAddresses(@Request() req) {
    const userId = (req.user as User).userId; // 从 req.user 中获取 userId
    const addresses = await this.userService.getAddresses(userId);
    return {
      statusCode: HttpStatus.OK,
      data: addresses
    };
  }

  /**
   * 修改密码接口 (PUT /api/user/password)
   * 使用 JWT 守卫保护
   */
  @UseGuards(AuthGuard('jwt'))
  @Put('password')
  async updatePassword(@Request() req, @Body() updatePasswordDto: UpdatePasswordDto) {
    const userId = (req.user as User).userId; // 从 req.user 中获取 userId
    await this.userService.updatePassword(userId, updatePasswordDto.oldPassword, updatePasswordDto.newPassword);
    return {
      statusCode: HttpStatus.OK,
      message: '密码修改成功'
    };
  }
}
```

**8. 在 `AppModule` 中导入 `AuthModule`：**

```typescript
// src/app.module.ts (部分内容，仅展示 imports 数组)
import { AuthModule } from './auth/auth.module'; // 导入 AuthModule

@Module({
  imports: [
    // ... TypeOrmModule.forRoot 和 ConfigModule.forRoot
    UserModule,
    AuthModule, // 在这里导入 AuthModule
    // ... 其他模块
  ],
  // ... 其他部分保持不变
})
export class AppModule {}
```

**测试 JWT 认证：**

重新启动您的 NestJS 应用 (`npm run start:dev`)。

1.  **用户注册 (POST `http://localhost:3000/api/user/register`)：**
    * 注册一个新用户，记住手机号和密码。

2.  **用户登录 (POST `http://localhost:3000/api/user/login`)：**
    * 使用注册的手机号和密码进行登录。
    * **预期响应：** 成功后会返回 `access_token` (JWT Token)。复制这个 Token。

3.  **获取用户信息 (GET `http://localhost:3000/api/user/info`)：**
    * **Header:**
        * `Content-Type: application/json`
        * `Authorization: Bearer <您刚刚复制的JWT Token>`
    * **Body:** (空)
    * **预期响应：** 成功返回用户信息。如果您不提供 `Authorization` 头或提供无效的 Token，会收到 `401 Unauthorized` 错误。

至此，您的用户认证系统已经基本搭建完成。

### **3.2 角色与权限管理 (RBAC)**

RBAC (Role-Based Access Control) 是一种授权机制，它通过将权限分配给角色，然后将角色分配给用户来管理访问权限。

**设计思路：**

* **角色 (Roles)：** 用户、技师、管理员。
* **权限 (Permissions)：** 例如：`create_order` (创建订单), `manage_users` (管理用户), `assign_orders` (分配订单) 等。
* **角色-权限关联：** 定义每个角色拥有哪些权限。
* **用户-角色关联：** 定义每个用户拥有哪些角色。

**数据库层面设计 (已在第二篇的实体中体现，但需要进一步完善和使用)：**

在 `User` 和 `Technician` 实体中，我们目前还没有直接的 `roles` 字段。我们可以选择：

1.  **在 User/Technician 实体中添加 `roles` 字段 (简单的 JSON 数组)：**
    例如，在 `User` 实体中添加 `@Column({ type: 'json', nullable: true }) roles: string[];`。
    这种方式简单快捷，适合角色不多的情况。
2.  **创建独立的 `Role` 实体和 `UserRole` 关联实体：**
    这种方式更灵活，适合复杂的权限体系，但会增加表数量和查询复杂性。

考虑到当前阶段和您文档中对角色的简单描述，我们采用第一种，即在 `User` 和 `Technician` 实体中直接添加一个 JSON 类型的 `roles` 数组字段。

**1. 更新 `User` 和 `Technician` 实体：**

在 `src/user/entities/user.entity.ts` 和 `src/technician/entities/technician.entity.ts` 中添加 `roles` 字段。

```typescript
// src/user/entities/user.entity.ts (添加 roles 字段)
import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('users')
export class User {
  // ... 其他字段
  @Column({ type: 'json', nullable: true })
  roles: string[]; // 例如: ['user', 'admin']
}
```

```typescript
// src/technician/entities/technician.entity.ts (添加 roles 字段)
import { Entity, PrimaryColumn, Column, CreateDateColumn } from 'typeorm';

@Entity('technicians')
export class Technician {
  // ... 其他字段
  @Column({ type: 'json', nullable: true })
  roles: string[]; // 例如: ['technician']
}
```
**重要：** 修改实体后，请确保您的 NestJS 应用重新启动，以便 `synchronize: true` 能够更新数据库表结构，添加 `roles` 列。

**2. 自定义 NestJS `Guard` 实现基于角色的访问控制：**

我们将创建一个 `RolesGuard` 和一个 `@Roles()` 装饰器。

**a. 定义 `Roles` 装饰器 (src/auth/roles.decorator.ts):**

```typescript
// src/auth/roles.decorator.ts
import { SetMetadata } from '@nestjs/common';

export const ROLES_KEY = 'roles'; // 定义用于存储元数据的键

export const Roles = (...roles: string[]) => SetMetadata(ROLES_KEY, roles);
```

**b. 创建 `RolesGuard` (src/auth/roles.guard.ts):**

```typescript
// src/auth/roles.guard.ts
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core'; // 导入 Reflector
import { ROLES_KEY } from './roles.decorator'; // 导入 ROLES_KEY
import { User } from '../user/entities/user.entity'; // 导入 User 实体（或一个更通用的 UserBase 接口）

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(ROLES_KEY, [
      context.getHandler(), // 获取路由处理器上的元数据
      context.getClass(),    // 获取控制器类上的元数据
    ]);

    if (!requiredRoles) {
      return true; // 如果路由没有设置 @Roles() 装饰器，则允许访问
    }

    const { user } = context.switchToHttp().getRequest(); // 从请求中获取用户对象 (由 JwtStrategy 附加)

    if (!user || !user.roles) {
      return false; // 如果没有用户对象或用户没有角色，则拒绝访问
    }

    // 检查用户是否拥有所需角色中的任何一个
    // 例如：requiredRoles = ['admin', 'manager'], user.roles = ['user', 'admin'] -> true
    return requiredRoles.some((role) => user.roles.includes(role));
  }
}
```

**c. 在 AuthModule 中注册 `RolesGuard` (src/auth/auth.module.ts):**

`RolesGuard` 是一个提供者，需要在 AuthModule 中注册。

```typescript
// src/auth/auth.module.ts (部分内容)
import { APP_GUARD } from '@nestjs/core'; // 导入 APP_GUARD Token

@Module({
  imports: [
    // ...
  ],
  providers: [
    AuthService,
    JwtStrategy,
    RolesGuard, // 注册 RolesGuard
    // 另一种全局注册方式 (不推荐此处，因为我们希望按需应用 RolesGuard)
    // {
    //   provide: APP_GUARD,
    //   useClass: RolesGuard,
    // },
  ],
  exports: [AuthService, JwtModule],
})
export class AuthModule {}
```

**3. 在控制器中应用权限守卫：**

现在我们可以在控制器方法上使用 `@UseGuards(RolesGuard)` 和 `@Roles()` 装饰器。

```typescript
// src/user/user.controller.ts (部分内容，仅展示如何使用守卫和装饰器)
import { Controller, Post, Body, Get, Param, Put, UseGuards, Request, HttpStatus } from '@nestjs/common';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { LoginUserDto } from './dto/login-user.dto';
import { AddAddressDto } from './dto/add-address.dto';
import { UpdatePasswordDto } from './dto/update-password.dto';

import { AuthService } from '../auth/auth.service';
import { AuthGuard } from '@nestjs/passport'; // 导入 AuthGuard
import { RolesGuard } from '../auth/roles.guard'; // 导入 RolesGuard
import { Roles } from '../auth/roles.decorator'; // 导入 Roles 装饰器
import { User } from './entities/user.entity'; // 导入 User 实体，用于 req.user 类型提示

@Controller('api/user')
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly authService: AuthService,
  ) {}

  // 注册和登录接口不需要身份认证，但登录后会返回 Token
  @Post('register')
  async register(@Body() createUserDto: CreateUserDto) {
    // ...
  }

  @Post('login')
  async login(@Body() loginUserDto: LoginUserDto) {
    // ...
  }

  // 假设这些接口需要用户身份，并且我们想演示如何限制某些操作给特定角色
  // 例如：只有管理员可以更新用户信息，用户自己也可以更新
  // 这里我们假设以下 API 只需要 "user" 角色才能访问（即任何登录用户）
  // 实际情况更复杂，例如，用户只能更新自己的信息，管理员可以更新所有用户信息

  @UseGuards(AuthGuard('jwt')) // 首先通过 JWT 认证
  @Get('info')
  @Roles('user', 'technician', 'admin') // 任何拥有这些角色的用户都可以访问
  async getUserInfo(@Request() req) {
    const user = req.user as User;
    const { password, ...result } = user;
    return {
      statusCode: HttpStatus.OK,
      data: result
    };
  }

  @UseGuards(AuthGuard('jwt'), RolesGuard) // 先 JWT 认证，再角色授权
  @Put('info')
  @Roles('user', 'admin') // 允许用户更新自己信息，管理员也可以
  async updateUserInfo(@Request() req, @Body() updateDto: UpdateUserDto) {
    const userId = (req.user as User).userId;
    // 实际的业务逻辑中，用户只能更新自己的信息。管理员可以更新指定用户的信息。
    // 这里我们先简化为用户只能更新 req.user 对应的用户。
    const updatedUser = await this.userService.updateUserInfo(userId, updateDto);
    const { password, ...result } = updatedUser;
    return {
      statusCode: HttpStatus.OK,
      message: '更新成功',
      data: result
    };
  }

  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Post('address')
  @Roles('user') // 只有普通用户可以添加地址
  async addAddress(@Request() req, @Body() addAddressDto: AddAddressDto) {
    const userId = (req.user as User).userId;
    const user = await this.userService.addAddress(userId, addAddressDto);
    const { password, ...result } = user;
    return {
      statusCode: HttpStatus.CREATED,
      message: '地址添加成功',
      data: result.addresses
    };
  }

  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Get('addresses')
  @Roles('user') // 只有普通用户可以获取地址列表
  async getAddresses(@Request() req) {
    const userId = (req.user as User).userId;
    const addresses = await this.userService.getAddresses(userId);
    return {
      statusCode: HttpStatus.OK,
      data: addresses
    };
  }

  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Put('password')
  @Roles('user', 'technician', 'admin') // 所有角色都可以修改密码
  async updatePassword(@Request() req, @Body() updatePasswordDto: UpdatePasswordDto) {
    const userId = (req.user as User).userId;
    await this.userService.updatePassword(userId, updatePasswordDto.oldPassword, updatePasswordDto.newPassword);
    return {
      statusCode: HttpStatus.OK,
      message: '密码修改成功'
    };
  }
}
```

**测试 RBAC：**

1.  **修改用户角色：** 在您的数据库中，找到您刚刚注册的 `users` 表中的用户记录。手动将 `roles` 字段修改为 `["user"]`。
    例如，使用 MySQL Workbench 或 DBeaver 执行：
    ```sql
    UPDATE users SET roles = JSON_ARRAY('user') WHERE userId = 'U1716616000000_example'; -- 替换为你的 userId
    ```
2.  **重新登录：** 再次调用登录接口，获取新的 JWT Token（因为 `roles` 字段在 JWT Payload 中）。
3.  **测试受保护的 API：**
    * **GET `/api/user/info`：** 使用新的 JWT Token 访问。如果 `roles` 包含 `user`，应该能正常访问。
    * **POST `/api/user/address`：** 使用新的 JWT Token 访问。如果 `roles` 包含 `user`，应该能正常添加地址。
    * **尝试访问一个只允许 `admin` 访问的接口 (假设你为 `updateUserInfo` 添加 `@Roles('admin')`):**
        * 将用户 `roles` 改为 `["user"]`。
        * 尝试调用 `PUT /api/user/info`。
        * **预期响应：** `403 Forbidden`。
        * 将用户 `roles` 改为 `["admin"]`。
        * 重新登录获取新的 Token。
        * 尝试调用 `PUT /api/user/info`。
        * **预期响应：** `200 OK`。

### **3.3 密码安全**

* **使用 `bcrypt` 进行密码哈希存储：**
    在第二篇的 `UserService` 中已经实现了，每次用户注册或修改密码时，密码都会被哈希存储到数据库中。登录时，原始密码会与哈希值进行比较。
    这是业界最佳实践，即使数据库泄露，攻击者也无法直接获取用户密码。

* **安全密码传输 (HTTPS 重要性)：**
    虽然 `bcrypt` 保护了数据库中的密码，但密码在客户端和服务器之间传输时，仍然需要保护。
    **生产环境务必部署 HTTPS。** HTTPS (HTTP Secure) 使用 SSL/TLS 协议对通信进行加密，防止中间人攻击窃听敏感信息，包括密码。在本地开发时，我们通常使用 HTTP，但在部署到公网时，这是必不可少的。

### **3.4 技师身份认证**

技师的认证流程与用户基本相同，只是可能需要独立的登录接口，并且在 JWT Payload 中会包含技师的 `technicianId` 和 `roles`（例如 `['technician']`）。

**实现步骤：**

1.  **创建 `TechnicianAuthModule` 或在现有 `AuthModule` 中扩展：**
    为了模块化，可以创建一个独立的 `TechnicianAuthModule`。或者在 `AuthModule` 中添加技师相关的逻辑。这里为了教程简化，我们假设在 `AuthModule` 中扩展。
2.  **创建 `TechnicianAuthService`：**
    与 `UserService` 类似，`TechnicianService` 将负责技师的注册、查找等。`AuthService` 可以添加 `loginTechnician` 方法。
3.  **创建 `TechnicianJwtStrategy` (可选，或在 `JwtStrategy` 中处理)：**
    如果技师和用户有完全不同的 JWT 签发和验证逻辑，可以创建新的策略。但通常情况下，JWT 的结构是通用的，可以在同一个 `JwtStrategy` 中处理不同类型的用户（通过 `payload` 中的 `type` 或 `roles` 区分）。
    这里，我们可以让 `JwtStrategy` 返回一个通用接口 `AuthUser`，其中包含 `userId` 或 `technicianId` 以及 `roles`。
    **修改 `JwtStrategy` 的 `validate` 方法，使其能处理不同类型的用户：**
    ```typescript
    // src/auth/jwt.strategy.ts (修改 validate 方法)
    import { Injectable, UnauthorizedException } from '@nestjs/common';
    import { PassportStrategy } from '@nestjs/passport';
    import { ExtractJwt, Strategy } from 'passport-jwt';
    import { ConfigService } from '@nestjs/config';
    import { UserService } from '../user/user.service';
    import { User } from '../user/entities/user.entity';
    import { TechnicianService } from '../technician/technician.service'; // 导入 TechnicianService
    import { Technician } from '../technician/entities/technician.entity'; // 导入 Technician 实体

    // 定义一个通用的认证用户接口
    export interface AuthenticatedUser extends Omit<User, 'password'>, Omit<Technician, 'password'> {
      userId?: string;
      technicianId?: string;
      roles: string[];
      // 可以添加其他区分用户/技师的字段
    }

    export interface JwtPayload {
      userId?: string; // 用户ID
      technicianId?: string; // 技师ID
      phone: string;
      username?: string; // 技师可能用 name 字段
      name?: string;
      roles: string[];
    }

    @Injectable()
    export class JwtStrategy extends PassportStrategy(Strategy) {
      constructor(
        private configService: ConfigService,
        private userService: UserService,
        private technicianService: TechnicianService, // 注入 TechnicianService
      ) {
        super({
          jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
          ignoreExpiration: false,
          secretOrKey: configService.get<string>('JWT_SECRET'),
        });
      }

      async validate(payload: JwtPayload): Promise<AuthenticatedUser> {
        if (payload.userId) { // 如果是普通用户
          const user = await this.userService.findOne(payload.userId);
          if (!user || !user.roles.some(role => payload.roles.includes(role))) { // 验证角色一致性
            throw new UnauthorizedException('用户认证失败或角色不匹配');
          }
          const { password, ...result } = user;
          return { ...result, roles: user.roles }; // 返回包含角色信息的 AuthenticatedUser
        } else if (payload.technicianId) { // 如果是技师
          const technician = await this.technicianService.findOne(payload.technicianId); // 假设有 findOne 方法
          if (!technician || !technician.roles.some(role => payload.roles.includes(role))) {
            throw new UnauthorizedException('技师认证失败或角色不匹配');
          }
          const { password, ...result } = technician; // 假设技师也有密码字段
          return { ...result, roles: technician.roles };
        }
        throw new UnauthorizedException('无效的身份类型');
      }
    }
    ```
4.  **创建 `TechnicianModule` 和 `TechnicianService`：**
    与用户模块类似，您需要创建 `src/technician/technician.module.ts`, `src/technician/technician.service.ts`, `src/technician/technician.controller.ts`。
    `TechnicianService` 将包含 `registerTechnician`, `validateTechnician`, `findOne`, `updateTechnicianInfo` 等方法，并使用 `bcrypt` 处理密码。
    `TechnicianModule` 中也需要 `TypeOrmModule.forFeature([Technician])`。
5.  **在 `AuthService` 中添加 `loginTechnician` 方法：**
    ```typescript
    // src/auth/auth.service.ts (添加 loginTechnician 方法)
    import { Injectable, UnauthorizedException } from '@nestjs/common';
    import { UserService } from '../user/user.service';
    import { JwtService } from '@nestjs/jwt';
    import { User } from '../user/entities/user.entity';
    import { TechnicianService } from '../technician/technician.service'; // 导入 TechnicianService
    import { Technician } from '../technician/entities/technician.entity'; // 导入 Technician 实体
    import { JwtPayload, AuthenticatedUser } from './jwt.strategy'; // 导入 JwtPayload 和 AuthenticatedUser

    @Injectable()
    export class AuthService {
      constructor(
        private userService: UserService,
        private technicianService: TechnicianService, // 注入 TechnicianService
        private jwtService: JwtService,
      ) {}

      // ... login (for User) 方法保持不变

      /**
       * 验证技师并生成JWT Token
       * @param phone 技师手机号
       * @param pass 技师密码
       * @returns 包含访问令牌的对象
       */
      async loginTechnician(phone: string, pass: string): Promise<{ access_token: string, technician: Omit<Technician, 'password'> }> {
        const technician = await this.technicianService.validateTechnician(phone, pass); // 调用 TechnicianService 验证
        if (!technician) {
          throw new UnauthorizedException('技师手机号或密码错误');
        }
        const payload: JwtPayload = {
          technicianId: technician.technicianId,
          phone: technician.phone,
          name: technician.name,
          roles: technician.roles || ['technician'], // 默认技师角色
        };
        return {
          access_token: this.jwtService.sign(payload),
          technician: technician,
        };
      }
    }
    ```
6.  **创建 `TechnicianController`：**
    提供技师注册、登录、信息获取等接口。登录接口将调用 `AuthService.loginTechnician`。
    其他受保护的接口同样使用 `@UseGuards(AuthGuard('jwt'), RolesGuard)` 和 `@Roles('technician')`。

**技师模块 (src/technician/) 的基本结构：**

* **`src/technician/technician.module.ts`**
    ```typescript
    import { Module } from '@nestjs/common';
    import { TypeOrmModule } from '@nestjs/typeorm';
    import { TechnicianService } from './technician.service';
    import { TechnicianController } from './technician.controller';
    import { Technician } from './entities/technician.entity';

    @Module({
      imports: [TypeOrmModule.forFeature([Technician])],
      controllers: [TechnicianController],
      providers: [TechnicianService],
      exports: [TechnicianService], // 导出以便 AuthModule 使用
    })
    export class TechnicianModule {}
    ```

* **`src/technician/technician.service.ts`**
    ```typescript
    import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
    import { InjectRepository } from '@nestjs/typeorm';
    import { Repository } from 'typeorm';
    import { Technician } from './entities/technician.entity';
    import * as bcrypt from 'bcrypt';

    @Injectable()
    export class TechnicianService {
      constructor(
        @InjectRepository(Technician)
        private technicianRepository: Repository<Technician>,
      ) {}

      async register(registerDto: any): Promise<Technician> {
        const { phone, password, name } = registerDto;

        const existingTechnician = await this.technicianRepository.findOne({ where: { phone } });
        if (existingTechnician) {
          throw new BadRequestException('技师手机号已被注册');
        }

        const hashedPassword = await bcrypt.hash(password, 10);

        const newTechnician = this.technicianRepository.create({
          technicianId: `T${Date.now()}${Math.floor(Math.random() * 1000)}`,
          name,
          phone,
          password: hashedPassword,
          registerTime: new Date(),
          status: 1, // 默认正常状态
          roles: ['technician'], // 默认角色
          skills: [], // 初始为空
          experience: 0,
          idCard: '', // 需后续完善，这里暂为空
        });

        return this.technicianRepository.save(newTechnician);
      }

      async validateTechnician(phone: string, pass: string): Promise<Omit<Technician, 'password'>> {
        const technician = await this.technicianRepository.findOne({ where: { phone } });
        if (technician && (await bcrypt.compare(pass, technician.password))) {
          const { password, ...result } = technician;
          return result;
        }
        return null;
      }

      async findOne(technicianId: string): Promise<Technician> {
        const technician = await this.technicianRepository.findOne({ where: { technicianId } });
        if (!technician) {
          throw new NotFoundException('技师未找到');
        }
        return technician;
      }

      // ... 其他技师相关的 CRUD 方法，如 updateTechnicianInfo, getOrdersForTechnician 等
    }
    ```

* **`src/technician/technician.controller.ts`**
    ```typescript
    import { Controller, Post, Body, Get, UseGuards, Request, HttpStatus } from '@nestjs/common';
    import { TechnicianService } from './technician.service';
    import { AuthService } from '../auth/auth.service';
    import { CreateTechnicianDto } from './dto/create-technician.dto';
    import { LoginTechnicianDto } from './dto/login-technician.dto';
    import { AuthGuard } from '@nestjs/passport';
    import { RolesGuard } from '../auth/roles.guard';
    import { Roles } from '../auth/roles.decorator';
    import { Technician } from './entities/technician.entity';
    import { AuthenticatedUser } from '../auth/jwt.strategy'; // 导入 AuthenticatedUser

    @Controller('api/technician')
    export class TechnicianController {
      constructor(
        private readonly technicianService: TechnicianService,
        private readonly authService: AuthService,
      ) {}

      @Post('register')
      async register(@Body() createTechnicianDto: CreateTechnicianDto) {
        const technician = await this.technicianService.register(createTechnicianDto);
        const { password, ...result } = technician;
        return {
          statusCode: HttpStatus.CREATED,
          message: '技师注册成功',
          data: result,
        };
      }

      @Post('login')
      async login(@Body() loginTechnicianDto: LoginTechnicianDto) {
        const result = await this.authService.loginTechnician(loginTechnicianDto.phone, loginTechnicianDto.password);
        return {
          statusCode: HttpStatus.OK,
          message: '技师登录成功',
          data: result.technician,
          token: result.access_token,
        };
      }

      @UseGuards(AuthGuard('jwt'), RolesGuard)
      @Get('info')
      @Roles('technician', 'admin') // 技师或管理员可以获取技师信息
      async getTechnicianInfo(@Request() req) {
        // req.user 现在是 AuthenticatedUser 类型
        const technicianId = (req.user as AuthenticatedUser).technicianId;
        const technician = await this.technicianService.findOne(technicianId);
        const { password, ...result } = technician;
        return {
          statusCode: HttpStatus.OK,
          data: result,
        };
      }

      // ... 技师的其他受保护接口，如更新技师信息、获取待处理订单等
    }
    ```

* **`src/technician/dto/create-technician.dto.ts`**
    ```typescript
    import { IsNotEmpty, IsString, MinLength, IsMobilePhone } from 'class-validator';

    export class CreateTechnicianDto {
      @IsNotEmpty({ message: '姓名不能为空' })
      @IsString({ message: '姓名必须是字符串' })
      name: string;

      @IsNotEmpty({ message: '手机号不能为空' })
      @IsString({ message: '手机号必须是字符串' })
      phone: string;

      @IsNotEmpty({ message: '密码不能为空' })
      @IsString({ message: '密码必须是字符串' })
      @MinLength(6, { message: '密码至少包含6个字符' })
      password: string;
      // ... 其他注册所需字段
    }
    ```
* **`src/technician/dto/login-technician.dto.ts`**
    ```typescript
    import { IsNotEmpty, IsString } from 'class-validator';

    export class LoginTechnicianDto {
      @IsNotEmpty({ message: '手机号不能为空' })
      @IsString({ message: '手机号必须是字符串' })
      phone: string;

      @IsNotEmpty({ message: '密码不能为空' })
      @IsString({ message: '密码必须是字符串' })
      password: string;
    }
    ```
* **在 `AppModule` 中导入 `TechnicianModule`：**
    ```typescript
    // src/app.module.ts
    import { Module } from '@nestjs/common';
    // ... 其他导入
    import { TechnicianModule } from './technician/technician.module'; // 导入 TechnicianModule

    @Module({
      imports: [
        // ...
        UserModule,
        TechnicianModule, // 导入 TechnicianModule
        AuthModule,
      ],
      // ...
    })
    export class AppModule {}
    ```

**测试技师认证：**

1.  **注册技师 (POST `http://localhost:3000/api/technician/register`)：**
    * Body: `{ "name": "维修师傅A", "phone": "13987654321", "password": "tech_pass" }`
    * 获取技师信息。
2.  **登录技师 (POST `http://localhost:3000/api/technician/login`)：**
    * Body: `{ "phone": "13987654321", "password": "tech_pass" }`
    * 获取技师 JWT Token。
3.  **获取技师信息 (GET `http://localhost:3000/api/technician/info`)：**
    * Header: `Authorization: Bearer <技师的JWT Token>`
    * 预期：成功返回技师信息。

### **第三篇总结：**

在本篇教程中，您成功地：

* 集成了 JWT 认证机制，实现了用户登录并获取 JWT Token，并使用 JWT 守卫保护了核心 API。
* 实现了基于角色的访问控制 (RBAC)，通过 `@Roles()` 装饰器和 `RolesGuard` 来限制不同角色对接口的访问。
* 完善了密码安全机制，强调了 HTTPS 的重要性。
* 扩展了认证系统，使其能够同时支持用户和技师的身份认证。

至此，您的家维在线系统后端具备了基本的身份认证和授权功能，安全性得到了显著提升。

---

**[请告诉我“继续”，我们将进入第四篇：核心业务模块一：服务与订单管理。]**