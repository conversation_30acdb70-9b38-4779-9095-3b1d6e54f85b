好的，我们进入第七篇。这是企业微信集成最核心的部分之一：当工单状态发生变化时，能够实时、精准地向相关人员发送消息通知。

---

### **《从零构建家政维修平台：打造稳健高效的混合架构后台应用》**

#### **第七篇：企业微信集成——消息通知与工单状态联动**

**摘要：** 实时消息通知是家政维修平台高效运作的关键。本篇教程将指导你如何在NestJS后端实现企业微信应用消息的推送功能，并将其与工单状态的变更进行联动。无论是新工单派发、工单状态更新，还是服务完成，都能通过企业微信及时通知到客服、调度和维修工人。

---

**一、企业微信应用消息API概览**

企业微信允许应用向企业成员发送多种类型的消息，包括文本、图片、图文消息等。这里我们主要聚焦于文本消息，因为它最简单、直接且能满足大部分通知需求。

1.  **消息发送API：**
    * 消息发送需要 `access_token`，这个 `access_token` 是通过 `corpid` 和**你的应用Secret**获取的（与通讯录管理Secret不同）。
    * 需要指定接收者（`touser`：成员UserID列表，`toparty`：部门ID列表，`totag`：标签ID列表），以及消息内容和 `agentid`（你的应用ID）。
    * **文档参考：** 务必查阅最新的企业微信开发文档“发送应用消息”部分。`https://developer.work.weixin.qq.com/document/path/90236`

2.  **消息类型：** 文本消息、图片消息、语音、视频、文件、文本卡片、图文消息等。我们主要使用文本消息。

**二、在NestJS后端增强企业微信服务**

我们将在 `WecomService` 中添加发送消息的方法。

1.  **更新 `WecomService`：**
    * 确保 `WecomService` 中已经包含了获取应用 `AccessToken` 的方法（我们在上一篇已包含）。
    * 添加 `sendTextMessage` 方法，用于封装调用企业微信发送消息的API。

    ```typescript
    // src/wecom/wecom.service.ts (在之前的代码基础上增加)

    import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
    import { HttpService } from '@nestjs/axios';
    import { ConfigService } from '@nestjs/config';
    import { firstValueFrom } from 'rxjs';
    import {
      WECOM_ACCESS_TOKEN_URL,
      // ... 其他常量 ...
      WECOM_MESSAGE_SEND_URL, // 导入消息发送URL
    } from './wecom.constants';
    import { InjectRepository } from '@nestjs/typeorm';
    import { Repository } from 'typeorm';
    import { User, UserRole } from '../user/user.entity';

    @Injectable()
    export class WecomService {
      private readonly logger = new Logger(WecomService.name);
      // ... 其他属性（contactSyncAccessToken, appAccessToken, corpId, contactSyncSecret, agentId, agentSecret） ...

      constructor(
        private readonly httpService: HttpService,
        private readonly configService: ConfigService,
        @InjectRepository(User)
        private usersRepository: Repository<User>,
      ) {
        // ... 构造函数初始化逻辑（获取AccessToken等） ...
      }

      // ... getContactSyncAccessToken 和 getAppAccessToken 方法 ...
      // ... syncWeComUsers 方法 ...

      /**
       * 向企业微信成员发送文本消息
       * @param userIds 接收消息的企业微信UserID列表，多个用'|'分隔，例如 'UserID1|UserID2'
       * @param content 消息内容
       * @param safe 是否保密消息 (0非保密，1保密)
       */
      async sendTextMessage(userIds: string, content: string, safe: 0 | 1 = 0): Promise<any> {
        this.logger.log(`Attempting to send message to: ${userIds}`);
        if (!this.appAccessToken) {
          await this.getAppAccessToken(); // 确保AccessToken已获取
        }

        const messageData = {
          touser: userIds, // 成员UserID列表
          msgtype: 'text',
          agentid: this.agentId, // 应用AgentId
          text: {
            content: content,
          },
          safe: safe, // 是否保密
        };

        try {
          const url = `${WECOM_MESSAGE_SEND_URL}?access_token=${this.appAccessToken}`;
          const response = await firstValueFrom(this.httpService.post(url, messageData));

          if (response.data.errcode === 0) {
            this.logger.log(`Message sent successfully to ${userIds}.`);
            return response.data;
          } else {
            this.logger.error(`Failed to send message to ${userIds}: ${response.data.errmsg}`);
            // TODO: 根据错误码判断是否需要刷新AccessToken
            throw new InternalServerErrorException(`Failed to send WeCom message: ${response.data.errmsg}`);
          }
        } catch (error) {
          this.logger.error(`Error sending WeCom message to ${userIds}:`, error.message);
          throw new InternalServerErrorException('Error sending WeCom message.');
        }
      }

      /**
       * 根据本地用户角色，获取对应企业微信User ID列表
       * @param roles 要查找的角色列表
       * @returns 匹配角色的企业微信User ID列表，用'|'分隔
       */
      async getUserWxWorkUserIdsByRoles(roles: UserRole[]): Promise<string> {
        const users = await this.usersRepository.find({
          where: roles.map(role => ({ role: role, wxWorkUserId: In(NotNull()) })), // 确保wxWorkUserId非空
          select: ['wxWorkUserId'], // 只查询wxWorkUserId字段
        });
        const userIds = users.map(user => user.wxWorkUserId).filter(Boolean); // 过滤空值
        return userIds.join('|');
      }

      /**
       * 根据本地用户ID获取对应的企业微信User ID
       * @param userId 本地数据库中的用户ID
       * @returns 企业微信User ID 或 null
       */
      async getWxWorkUserIdByLocalUserId(userId: number): Promise<string | null> {
        const user = await this.usersRepository.findOne({ where: { id: userId }, select: ['wxWorkUserId'] });
        return user?.wxWorkUserId || null;
      }
    }
    ```
    * **导入 `In` 和 `NotNull`：** 在 `TypeORM` 0.3.x 版本中，`In` 和 `NotNull` 可以从 `typeorm` 包中导入。
        ```typescript
        // 确保在 WecomService 文件的顶部导入
        import { In, Not, IsNull, NotNull } from 'typeorm'; // 根据需要导入
        ```

**三、工单状态变更时触发企业微信通知**

这是核心的业务联动逻辑，我们将修改 `OrderService`，在工单状态更新时调用 `WecomService` 发送通知。

1.  **修改 `OrderService`：**
    * 注入 `WecomService`。
    * 在 `dispatchOrder` 和 `updateOrderStatus` 方法中，添加发送通知的逻辑。

    ```typescript
    // src/order/order.service.ts (在之前的代码基础上增加和修改)

    import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
    import { InjectRepository } from '@nestjs/typeorm';
    import { Repository } from 'typeorm';
    import { Order, OrderStatus } from './order.entity';
    import { User, UserRole } from '../user/user.entity';
    import { WecomService } from '../wecom/wecom.service'; // 导入WecomService

    @Injectable()
    export class OrderService {
      private readonly logger = new Logger(OrderService.name);

      constructor(
        @InjectRepository(Order)
        private ordersRepository: Repository<Order>,
        @InjectRepository(User)
        private usersRepository: Repository<User>,
        private readonly wecomService: WecomService, // 注入WecomService
      ) {}

      // ... createOrder, findOrderById, findAllOrders 方法 ...

      // 派发工单（给维修工人）
      async dispatchOrder(orderId: number, workerId: number, dispatcherId?: number): Promise<Order> {
        const order = await this.findOrderById(orderId);
        if (order.status !== OrderStatus.Pending) {
          throw new BadRequestException('Only pending orders can be dispatched.');
        }

        const worker = await this.usersRepository.findOne({ where: { id: workerId, role: UserRole.Worker } });
        if (!worker || !worker.wxWorkUserId) { // 检查维修工人是否存在且有企微ID
          throw new NotFoundException(`Worker with ID ${workerId} not found, not a worker role, or missing WeCom UserID.`);
        }

        order.worker = worker;
        order.workerId = worker.id;
        order.status = OrderStatus.Dispatched;
        order.dispatchTime = new Date();
        if (dispatcherId) {
          const dispatcher = await this.usersRepository.findOne({ where: { id: dispatcherId, role: UserRole.Dispatcher } });
          if (dispatcher) {
              order.dispatcher = dispatcher;
              order.dispatcherId = dispatcher.id;
          }
        }
        const updatedOrder = await this.ordersRepository.save(order);

        // **发送企业微信通知：新工单已派发给维修工人**
        const workerWxId = worker.wxWorkUserId;
        const notificationContent = `您好，您有一个新工单需要处理：\n工单号：${order.id}\n服务类型：${order.serviceType}\n客户姓名：${order.customerName}\n联系电话：${order.customerPhone}\n地址：${order.address}\n描述：${order.description}\n请尽快联系客户并处理。`;
        await this.wecomService.sendTextMessage(workerWxId, notificationContent);
        this.logger.log(`Sent new order notification to worker ${worker.username} (${workerWxId}) for order ${order.id}`);

        // TODO: 可选：通知相关客服或调度员
        // const dispatcherAndCSRWxIds = await this.wecomService.getUserWxWorkUserIdsByRoles([UserRole.Dispatcher, UserRole.CustomerService]);
        // if (dispatcherAndCSRWxIds) {
        //   await this.wecomService.sendTextMessage(dispatcherAndCSRWxIds, `工单 ${order.id} 已派发给 ${worker.username}。`);
        // }


        return updatedOrder;
      }

      // 更新工单状态（如进行中、已完成、已取消）
      async updateOrderStatus(orderId: number, newStatus: OrderStatus): Promise<Order> {
        const order = await this.findOrderById(orderId);
        const oldStatus = order.status; // 记录旧状态
        if (oldStatus === newStatus) {
            this.logger.log(`Order ${orderId} status unchanged: ${newStatus}`);
            return order; // 状态未变，直接返回
        }

        order.status = newStatus;
        if (newStatus === OrderStatus.Completed) {
          order.completeTime = new Date();
        }
        const updatedOrder = await this.ordersRepository.save(order);

        // **发送企业微信通知：工单状态更新**
        let notificationContent = '';
        let recipientWxIds = '';

        if (newStatus === OrderStatus.InProgress && updatedOrder.workerId) {
          // 维修工人点击“开始服务”
          const workerWxId = await this.wecomService.getWxWorkUserIdByLocalUserId(updatedOrder.workerId);
          if (workerWxId) {
            notificationContent = `工单 ${order.id} 状态已更新为“维修中”。`;
            recipientWxIds = workerWxId;
          }
        } else if (newStatus === OrderStatus.Completed) {
          // 维修工人点击“完成服务”
          const workerWxId = await this.wecomService.getWxWorkUserIdByLocalUserId(updatedOrder.workerId);
          if (workerWxId) {
              notificationContent = `工单 ${order.id} 已完成。请确认。`;
              recipientWxIds = workerWxId; // 通知工人自己
          }
          // 同时通知客服和调度员
          const csrAndDispatcherWxIds = await this.wecomService.getUserWxWorkUserIdsByRoles([UserRole.CustomerService, UserRole.Dispatcher]);
          if (csrAndDispatcherWxIds) {
              await this.wecomService.sendTextMessage(csrAndDispatcherWxIds, `工单 ${order.id} (${order.serviceType}) 已由 ${updatedOrder.worker?.username || '未知工人'} 完成，请尽快进行费用结算。`);
          }

        } else if (newStatus === OrderStatus.Canceled) {
          // 工单被取消
          notificationContent = `工单 ${order.id} 已被取消。`;
          // 通知所有相关人员
          const allRelevantWxIds = await this.wecomService.getUserWxWorkUserIdsByRoles([UserRole.CustomerService, UserRole.Dispatcher, UserRole.Worker]);
          if (allRelevantWxIds) {
              await this.wecomService.sendTextMessage(allRelevantWxIds, `工单 ${order.id} (${order.serviceType}) 已被取消。`);
          }
        }
        // TODO: 其他状态变更的通知逻辑

        if (notificationContent && recipientWxIds) {
          await this.wecomService.sendTextMessage(recipientWxIds, notificationContent);
          this.logger.log(`Sent status update notification for order ${order.id} to ${recipientWxIds}.`);
        }

        return updatedOrder;
      }
      // ... 其他方法 ...
    }
    ```
    * **导入 `WecomService`：** 确保在 `OrderService` 的构造函数中注入 `WecomService`。
    * **逻辑细化：**
        * **派发工单时：** 向 `workerId` 对应的企业微信成员发送新工单通知，包含工单详情。
        * **更新工单状态时：** 根据新的状态（例如 `InProgress`、`Completed`、`Canceled`），向相应的成员（维修工人、客服、调度员）发送不同的通知内容。
        * 我们使用 `getWxWorkUserIdByLocalUserId` 和 `getUserWxWorkUserIdsByRoles` 方法来方便地获取接收者的企业微信UserID。
    * **错误处理：** 在实际生产中，发送消息失败时，应有更健壮的错误处理机制，如记录日志、重试机制等。

**四、测试工单通知功能**

1.  **准备数据：**
    * 确保你已经在企业微信管理后台中创建了测试成员（例如一个客服、一个调度、一个维修工人），并且**他们的手机号或邮箱与你在本地数据库 `users` 表中对应的用户匹配**。
    * 运行上一篇的通讯录同步API (`POST /wecom/sync-users`)，确保本地 `users` 表中**已关联**了这些测试成员的 `wx_work_userid`。
    * 在本地数据库中创建一些测试工单。
2.  **启动后端应用：** 运行 `npm run start:dev`。
3.  **使用API测试工具：**
    * **登录你的管理员或调度员账号，获取JWT Token。**
    * **派发工单：**
        * URL: `http://localhost:3000/orders/:orderId/dispatch` (替换 `:orderId`)
        * Method: `PUT`
        * Headers: `Authorization: Bearer YOUR_DISPATCHER_TOKEN`
        * Body (JSON): `{ "workerId": YOUR_LOCAL_WORKER_USER_ID }`
        * 发送请求后，观察对应的维修工人的企业微信是否收到新工单通知。
    * **更新工单状态：**
        * URL: `http://localhost:3000/orders/:orderId/status`
        * Method: `PUT`
        * Headers: `Authorization: Bearer YOUR_WORKER_OR_DISPATCHER_TOKEN`
        * Body (JSON): `{ "newStatus": "in_progress" }` 或 `{ "newStatus": "completed" }`
        * 观察相关人员（维修工人、客服、调度员）的企业微信是否收到状态更新通知。

通过本篇教程，你的家政维修平台后端已经具备了与企业微信的深度集成能力，能够根据业务流程的变化（特别是工单状态），智能地向相关人员推送实时通知。这大大提升了内部协同效率，确保了信息的及时传递。

---

**[请告诉我“继续”，我将提供第八篇：前端管理后台基础搭建（React + Ant Design）。]**