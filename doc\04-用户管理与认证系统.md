### **《新一代在线家政维修服务平台：从零构建企业级O2O服务生态》**

#### **第四篇：用户管理与认证系统构建——打造安全可靠的用户体系**

**摘要：** 用户管理是平台的核心基础，直接关系到系统的安全性和用户体验。本篇将详细讲解如何构建一个支持多角色、多认证方式的用户管理系统，包括用户注册、登录认证、权限控制、密码安全等关键功能的实现。

---

**一、用户实体设计与实现**

### 1. 用户实体定义

首先创建用户相关的实体类，映射数据库表结构：

```typescript
// src/modules/users/entities/user.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  OneToMany,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { UserProfile } from './user-profile.entity';
import { ServiceProvider } from '../../providers/entities/service-provider.entity';

export enum UserRole {
  CUSTOMER = 'customer',
  PROVIDER = 'provider',
  ADMIN = 'admin',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BANNED = 'banned',
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ length: 50, unique: true })
  username: string;

  @Column({ length: 20, unique: true })
  phone: string;

  @Column({ length: 100, unique: true, nullable: true })
  email: string;

  @Column({ name: 'password_hash', length: 255 })
  @Exclude()
  passwordHash: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.CUSTOMER,
  })
  role: UserRole;

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
  })
  status: UserStatus;

  @Column({ name: 'avatar_url', length: 500, nullable: true })
  avatarUrl: string;

  @Column({ name: 'real_name', length: 50, nullable: true })
  realName: string;

  @Column({ name: 'id_card', length: 18, nullable: true })
  @Exclude()
  idCard: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @OneToOne(() => UserProfile, (profile) => profile.user, { cascade: true })
  profile: UserProfile;

  @OneToOne(() => ServiceProvider, (provider) => provider.user)
  serviceProvider: ServiceProvider;

  constructor(partial: Partial<User>) {
    Object.assign(this, partial);
  }
}
```

### 2. 用户详细信息实体

```typescript
// src/modules/users/entities/user-profile.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  UNKNOWN = 'unknown',
}

@Entity('user_profiles')
export class UserProfile {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'user_id', type: 'bigint' })
  userId: number;

  @Column({ length: 50, nullable: true })
  nickname: string;

  @Column({
    type: 'enum',
    enum: Gender,
    default: Gender.UNKNOWN,
  })
  gender: Gender;

  @Column({ name: 'birth_date', type: 'date', nullable: true })
  birthDate: Date;

  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ length: 50, nullable: true })
  city: string;

  @Column({ length: 50, nullable: true })
  district: string;

  @Column({ type: 'decimal', precision: 10, scale: 7, nullable: true })
  longitude: number;

  @Column({ type: 'decimal', precision: 10, scale: 7, nullable: true })
  latitude: number;

  @Column({ type: 'json', nullable: true })
  preferences: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @OneToOne(() => User, (user) => user.profile, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;
}
```

**二、用户服务层实现**

### 1. 用户数据传输对象

```typescript
// src/modules/users/dto/create-user.dto.ts
import { IsEmail, IsEnum, IsOptional, IsPhoneNumber, IsString, MinLength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { UserRole } from '../entities/user.entity';

export class CreateUserDto {
  @ApiProperty({ description: '用户名', example: 'john_doe' })
  @IsString()
  @MinLength(3)
  username: string;

  @ApiProperty({ description: '手机号', example: '13800138000' })
  @IsPhoneNumber('CN')
  phone: string;

  @ApiProperty({ description: '邮箱', example: '<EMAIL>', required: false })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({ description: '密码', example: 'password123' })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({ description: '用户角色', enum: UserRole, example: UserRole.CUSTOMER })
  @IsEnum(UserRole)
  @IsOptional()
  role?: UserRole = UserRole.CUSTOMER;

  @ApiProperty({ description: '真实姓名', example: '张三', required: false })
  @IsString()
  @IsOptional()
  realName?: string;
}

// src/modules/users/dto/update-user.dto.ts
import { PartialType, OmitType } from '@nestjs/swagger';
import { CreateUserDto } from './create-user.dto';

export class UpdateUserDto extends PartialType(
  OmitType(CreateUserDto, ['password'] as const)
) {}

// src/modules/users/dto/login.dto.ts
export class LoginDto {
  @ApiProperty({ description: '用户名或手机号', example: 'john_doe' })
  @IsString()
  usernameOrPhone: string;

  @ApiProperty({ description: '密码', example: 'password123' })
  @IsString()
  password: string;
}
```

### 2. 用户服务实现

```typescript
// src/modules/users/users.service.ts
import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { User, UserRole, UserStatus } from './entities/user.entity';
import { UserProfile } from './entities/user-profile.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserProfile)
    private readonly userProfileRepository: Repository<UserProfile>,
  ) {}

  /**
   * 创建新用户
   */
  async create(createUserDto: CreateUserDto): Promise<User> {
    const { username, phone, email, password, ...userData } = createUserDto;

    // 检查用户名是否已存在
    const existingUser = await this.userRepository.findOne({
      where: [{ username }, { phone }, ...(email ? [{ email }] : [])],
    });

    if (existingUser) {
      if (existingUser.username === username) {
        throw new ConflictException('用户名已存在');
      }
      if (existingUser.phone === phone) {
        throw new ConflictException('手机号已存在');
      }
      if (existingUser.email === email) {
        throw new ConflictException('邮箱已存在');
      }
    }

    // 加密密码
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const user = this.userRepository.create({
      username,
      phone,
      email,
      passwordHash,
      ...userData,
    });

    const savedUser = await this.userRepository.save(user);

    // 创建用户详细信息
    const profile = this.userProfileRepository.create({
      userId: savedUser.id,
      nickname: username,
    });
    await this.userProfileRepository.save(profile);

    return this.findById(savedUser.id);
  }

  /**
   * 根据ID查找用户
   */
  async findById(id: number): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['profile'],
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    return user;
  }

  /**
   * 根据用户名或手机号查找用户
   */
  async findByUsernameOrPhone(usernameOrPhone: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: [{ username: usernameOrPhone }, { phone: usernameOrPhone }],
      relations: ['profile'],
    });
  }

  /**
   * 验证用户密码
   */
  async validatePassword(user: User, password: string): Promise<boolean> {
    return bcrypt.compare(password, user.passwordHash);
  }

  /**
   * 更新用户信息
   */
  async update(id: number, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findById(id);

    // 检查用户名和手机号的唯一性
    if (updateUserDto.username || updateUserDto.phone || updateUserDto.email) {
      const existingUser = await this.userRepository.findOne({
        where: [
          ...(updateUserDto.username ? [{ username: updateUserDto.username }] : []),
          ...(updateUserDto.phone ? [{ phone: updateUserDto.phone }] : []),
          ...(updateUserDto.email ? [{ email: updateUserDto.email }] : []),
        ],
      });

      if (existingUser && existingUser.id !== id) {
        throw new ConflictException('用户名、手机号或邮箱已存在');
      }
    }

    await this.userRepository.update(id, updateUserDto);
    return this.findById(id);
  }

  /**
   * 更新用户状态
   */
  async updateStatus(id: number, status: UserStatus): Promise<User> {
    await this.userRepository.update(id, { status });
    return this.findById(id);
  }

  /**
   * 分页查询用户列表
   */
  async findAll(
    page: number = 1,
    limit: number = 10,
    role?: UserRole,
    status?: UserStatus,
  ) {
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.profile', 'profile')
      .orderBy('user.createdAt', 'DESC');

    if (role) {
      queryBuilder.andWhere('user.role = :role', { role });
    }

    if (status) {
      queryBuilder.andWhere('user.status = :status', { status });
    }

    const [users, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      data: users,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * 删除用户（软删除）
   */
  async remove(id: number): Promise<void> {
    const user = await this.findById(id);
    await this.userRepository.update(id, { status: UserStatus.INACTIVE });
  }
}
```

**三、认证系统实现**

### 1. JWT策略配置

```typescript
// src/modules/auth/strategies/jwt.strategy.ts
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../../users/users.service';

export interface JwtPayload {
  sub: number;
  username: string;
  role: string;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly configService: ConfigService,
    private readonly usersService: UsersService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
    });
  }

  async validate(payload: JwtPayload) {
    const user = await this.usersService.findById(payload.sub);
    
    if (!user || user.status !== 'active') {
      throw new UnauthorizedException('用户不存在或已被禁用');
    }

    return user;
  }
}
```

### 2. 本地认证策略

```typescript
// src/modules/auth/strategies/local.strategy.ts
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { AuthService } from '../auth.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly authService: AuthService) {
    super({
      usernameField: 'usernameOrPhone',
      passwordField: 'password',
    });
  }

  async validate(usernameOrPhone: string, password: string) {
    const user = await this.authService.validateUser(usernameOrPhone, password);
    
    if (!user) {
      throw new UnauthorizedException('用户名或密码错误');
    }

    return user;
  }
}
```

### 3. 认证服务实现

```typescript
// src/modules/auth/auth.service.ts
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { User } from '../users/entities/user.entity';
import { LoginDto } from './dto/login.dto';
import { JwtPayload } from './strategies/jwt.strategy';

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
  ) {}

  /**
   * 验证用户凭据
   */
  async validateUser(usernameOrPhone: string, password: string): Promise<User | null> {
    const user = await this.usersService.findByUsernameOrPhone(usernameOrPhone);
    
    if (!user) {
      return null;
    }

    const isPasswordValid = await this.usersService.validatePassword(user, password);
    
    if (!isPasswordValid) {
      return null;
    }

    if (user.status !== 'active') {
      throw new UnauthorizedException('账户已被禁用');
    }

    return user;
  }

  /**
   * 用户登录
   */
  async login(user: User) {
    const payload: JwtPayload = {
      sub: user.id,
      username: user.username,
      role: user.role,
    };

    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user.id,
        username: user.username,
        phone: user.phone,
        email: user.email,
        role: user.role,
        status: user.status,
        profile: user.profile,
      },
    };
  }

  /**
   * 获取当前用户信息
   */
  async getProfile(userId: number) {
    return this.usersService.findById(userId);
  }
}
```

通过本篇教程，我们构建了一个完整的用户管理和认证系统，包括用户注册、登录、权限验证等核心功能。这为后续的业务模块开发提供了安全可靠的用户基础。

---

**[请告诉我"继续"，我将提供第五篇：服务商管理与审核系统。]**
