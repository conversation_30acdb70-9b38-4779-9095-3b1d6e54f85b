### **《新一代在线家政维修服务平台：从需求到实现的企业级开发实战》**

#### **第四篇：服务商管理与多阶段审核系统**

**摘要：** 基于需求规格说明书中的服务商管理需求（FR-PM-001至FR-PM-008），本篇将构建一个完整的服务商管理和多阶段审核系统。我们将实现服务商注册、资质认证、自动审核、人工审核、技能管理等核心功能，确保平台服务商的质量和可信度。

---

## **4.1 服务商注册与资料管理**

### **服务商模块结构创建**

```bash
# 创建服务商管理模块
nest g module modules/providers
nest g controller modules/providers
nest g service modules/providers

# 创建审核模块
nest g module modules/review
nest g service modules/review
```

### **服务商数据传输对象**

```typescript
// src/modules/providers/dto/create-provider.dto.ts
import { IsString, IsOptional, IsNumber, IsArray, ValidateNested, IsB<PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class WorkingHoursDto {
  @ApiProperty({ description: '星期几 (0-6, 0为周日)', example: 1 })
  @IsNumber()
  @Min(0)
  @Max(6)
  dayOfWeek: number;

  @ApiProperty({ description: '开始时间', example: '09:00' })
  @IsString()
  startTime: string;

  @ApiProperty({ description: '结束时间', example: '18:00' })
  @IsString()
  endTime: string;

  @ApiProperty({ description: '是否可用', example: true })
  @IsOptional()
  @IsBoolean()
  isAvailable?: boolean = true;
}

export class CreateProviderDto {
  @ApiProperty({ description: '商户名称', example: '张师傅家电维修' })
  @IsOptional()
  @IsString()
  businessName?: string;

  @ApiProperty({ description: '营业执照号', example: '91110000123456789X' })
  @IsOptional()
  @IsString()
  businessLicense?: string;

  @ApiProperty({ description: '服务半径(公里)', example: 15 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  serviceRadius?: number = 10;

  @ApiProperty({ description: '服务基地经度', example: 116.397128 })
  @IsOptional()
  @IsNumber()
  baseLongitude?: number;

  @ApiProperty({ description: '服务基地纬度', example: 39.916527 })
  @IsOptional()
  @IsNumber()
  baseLatitude?: number;

  @ApiProperty({ description: '工作时间设置', type: [WorkingHoursDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkingHoursDto)
  workingHours?: WorkingHoursDto[];

  @ApiProperty({ description: '服务商介绍', example: '专业家电维修，10年经验，服务过1000+家庭' })
  @IsOptional()
  @IsString()
  introduction?: string;

  @ApiProperty({ description: '从业年限', example: 10 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(50)
  experienceYears?: number = 0;
}

// src/modules/providers/dto/create-certification.dto.ts
import { IsString, IsOptional, IsDateString, IsUrl } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCertificationDto {
  @ApiProperty({ description: '认证类型', example: 'skill', enum: ['skill', 'identity', 'business'] })
  @IsString()
  certificationType: string;

  @ApiProperty({ description: '认证名称', example: '家电维修技师证' })
  @IsString()
  certificationName: string;

  @ApiProperty({ description: '证书编号', example: 'CERT123456' })
  @IsOptional()
  @IsString()
  certificateNumber?: string;

  @ApiProperty({ description: '证书图片URL', example: 'https://example.com/cert.jpg' })
  @IsOptional()
  @IsUrl()
  certificateUrl?: string;

  @ApiProperty({ description: '颁发日期', example: '2020-01-01' })
  @IsOptional()
  @IsDateString()
  issueDate?: string;

  @ApiProperty({ description: '过期日期', example: '2025-01-01' })
  @IsOptional()
  @IsDateString()
  expireDate?: string;
}

// src/modules/providers/dto/update-provider.dto.ts
import { PartialType } from '@nestjs/swagger';
import { CreateProviderDto } from './create-provider.dto';

export class UpdateProviderDto extends PartialType(CreateProviderDto) {}
```

### **服务商服务层实现**

```typescript
// src/modules/providers/providers.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ServiceProvider, CertificationStatus } from '../../database/entities/service-provider.entity';
import { ProviderCertification } from '../../database/entities/provider-certification.entity';
import { User, UserRole } from '../../database/entities/user.entity';
import { CreateProviderDto } from './dto/create-provider.dto';
import { UpdateProviderDto } from './dto/update-provider.dto';
import { CreateCertificationDto } from './dto/create-certification.dto';

@Injectable()
export class ProvidersService {
  constructor(
    @InjectRepository(ServiceProvider)
    private readonly providerRepository: Repository<ServiceProvider>,
    @InjectRepository(ProviderCertification)
    private readonly certificationRepository: Repository<ProviderCertification>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * 创建服务商档案
   * 实现需求：FR-PM-001
   */
  async create(userId: number, createProviderDto: CreateProviderDto): Promise<ServiceProvider> {
    // 检查用户是否存在且角色正确
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    if (user.role !== UserRole.PROVIDER && user.role !== UserRole.CUSTOMER) {
      throw new BadRequestException('用户角色不正确');
    }

    // 检查用户是否已经是服务商
    const existingProvider = await this.providerRepository.findOne({
      where: { userId },
    });

    if (existingProvider) {
      throw new ConflictException('用户已经是服务商');
    }

    // 如果用户原来是客户，升级为服务商
    if (user.role === UserRole.CUSTOMER) {
      await this.userRepository.update(userId, { role: UserRole.PROVIDER });
    }

    const provider = this.providerRepository.create({
      userId,
      ...createProviderDto,
      workingHours: createProviderDto.workingHours || this.getDefaultWorkingHours(),
    });

    return this.providerRepository.save(provider);
  }

  /**
   * 获取默认工作时间
   */
  private getDefaultWorkingHours() {
    return Array.from({ length: 7 }, (_, index) => ({
      dayOfWeek: index,
      startTime: '09:00',
      endTime: '18:00',
      isAvailable: index >= 1 && index <= 5, // 周一到周五
    }));
  }

  /**
   * 根据ID查找服务商
   */
  async findById(id: number): Promise<ServiceProvider> {
    const provider = await this.providerRepository.findOne({
      where: { id },
      relations: ['user', 'user.profile', 'certifications', 'services', 'services.serviceItem'],
    });

    if (!provider) {
      throw new NotFoundException('服务商不存在');
    }

    return provider;
  }

  /**
   * 根据用户ID查找服务商
   */
  async findByUserId(userId: number): Promise<ServiceProvider | null> {
    return this.providerRepository.findOne({
      where: { userId },
      relations: ['user', 'user.profile', 'certifications', 'services'],
    });
  }

  /**
   * 更新服务商信息
   * 实现需求：FR-PM-002
   */
  async update(id: number, updateProviderDto: UpdateProviderDto): Promise<ServiceProvider> {
    const provider = await this.findById(id);
    
    await this.providerRepository.update(id, updateProviderDto);
    return this.findById(id);
  }

  /**
   * 添加认证信息
   * 实现需求：FR-PM-003
   */
  async addCertification(
    providerId: number,
    createCertificationDto: CreateCertificationDto,
  ): Promise<ProviderCertification> {
    const provider = await this.findById(providerId);

    const certification = this.certificationRepository.create({
      providerId,
      ...createCertificationDto,
      issueDate: createCertificationDto.issueDate ? new Date(createCertificationDto.issueDate) : null,
      expireDate: createCertificationDto.expireDate ? new Date(createCertificationDto.expireDate) : null,
    });

    const savedCertification = await this.certificationRepository.save(certification);

    // 触发自动审核
    await this.triggerAutoReview(providerId);

    return savedCertification;
  }

  /**
   * 触发自动审核
   */
  private async triggerAutoReview(providerId: number): Promise<void> {
    const provider = await this.findById(providerId);
    
    // 如果已经通过审核，不再重新审核
    if (provider.certificationStatus === CertificationStatus.APPROVED) {
      return;
    }

    // 基础信息完整性检查
    const hasBasicInfo = provider.businessName && 
                        provider.introduction && 
                        provider.baseLongitude && 
                        provider.baseLatitude;

    // 认证信息检查
    const hasCertifications = provider.certifications && provider.certifications.length > 0;

    if (!hasBasicInfo || !hasCertifications) {
      await this.providerRepository.update(providerId, {
        certificationStatus: CertificationStatus.PENDING,
        certificationNote: '请完善基础信息和认证材料',
      });
      return;
    }

    // 检查必要的认证类型
    const hasIdentityCert = provider.certifications.some(cert => cert.certificationType === 'identity');
    const hasSkillCert = provider.certifications.some(cert => cert.certificationType === 'skill');

    if (!hasIdentityCert) {
      await this.providerRepository.update(providerId, {
        certificationStatus: CertificationStatus.REJECTED,
        certificationNote: '缺少身份认证材料',
      });
      return;
    }

    if (!hasSkillCert) {
      await this.providerRepository.update(providerId, {
        certificationStatus: CertificationStatus.REJECTED,
        certificationNote: '缺少技能认证材料',
      });
      return;
    }

    // 通过自动审核，进入人工审核阶段
    await this.providerRepository.update(providerId, {
      certificationStatus: CertificationStatus.PENDING,
      certificationNote: '自动审核通过，等待人工审核',
    });
  }

  /**
   * 分页查询服务商列表
   */
  async findAll(
    page: number = 1,
    limit: number = 10,
    status?: CertificationStatus,
    city?: string,
    search?: string,
  ) {
    const queryBuilder = this.providerRepository
      .createQueryBuilder('provider')
      .leftJoinAndSelect('provider.user', 'user')
      .leftJoinAndSelect('user.profile', 'profile')
      .leftJoinAndSelect('provider.certifications', 'certifications')
      .orderBy('provider.createdAt', 'DESC');

    if (status) {
      queryBuilder.andWhere('provider.certificationStatus = :status', { status });
    }

    if (city) {
      queryBuilder.andWhere('profile.city = :city', { city });
    }

    if (search) {
      queryBuilder.andWhere(
        '(provider.businessName LIKE :search OR user.realName LIKE :search OR user.phone LIKE :search)',
        { search: `%${search}%` }
      );
    }

    const [providers, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      data: providers,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * 搜索附近的服务商
   * 实现需求：FR-PM-006
   */
  async findNearbyProviders(
    longitude: number,
    latitude: number,
    radius: number = 10,
    serviceItemId?: number,
  ) {
    const queryBuilder = this.providerRepository
      .createQueryBuilder('provider')
      .leftJoinAndSelect('provider.user', 'user')
      .leftJoinAndSelect('provider.services', 'services')
      .leftJoinAndSelect('services.serviceItem', 'serviceItem')
      .where('provider.certificationStatus = :status', { status: CertificationStatus.APPROVED })
      .andWhere('provider.baseLongitude IS NOT NULL')
      .andWhere('provider.baseLatitude IS NOT NULL')
      .andWhere(
        `(6371 * acos(cos(radians(:latitude)) * cos(radians(provider.baseLatitude)) * 
         cos(radians(provider.baseLongitude) - radians(:longitude)) + 
         sin(radians(:latitude)) * sin(radians(provider.baseLatitude)))) <= :radius`,
        { latitude, longitude, radius },
      );

    if (serviceItemId) {
      queryBuilder.andWhere('services.serviceItemId = :serviceItemId', { serviceItemId });
      queryBuilder.andWhere('services.isAvailable = :isAvailable', { isAvailable: true });
    }

    return queryBuilder
      .addSelect(
        `(6371 * acos(cos(radians(:latitude)) * cos(radians(provider.baseLatitude)) * 
         cos(radians(provider.baseLongitude) - radians(:longitude)) + 
         sin(radians(:latitude)) * sin(radians(provider.baseLatitude))))`,
        'distance'
      )
      .orderBy('provider.ratingAverage', 'DESC')
      .addOrderBy('provider.totalOrders', 'DESC')
      .addOrderBy('distance', 'ASC')
      .getMany();
  }

  /**
   * 更新服务商评分
   * 实现需求：FR-PM-008
   */
  async updateRating(providerId: number, newRating: number): Promise<void> {
    const provider = await this.findById(providerId);
    
    const totalRating = provider.ratingAverage * provider.ratingCount + newRating;
    const newRatingCount = provider.ratingCount + 1;
    const newRatingAverage = totalRating / newRatingCount;

    await this.providerRepository.update(providerId, {
      ratingAverage: Math.round(newRatingAverage * 100) / 100,
      ratingCount: newRatingCount,
      totalOrders: provider.totalOrders + 1,
    });
  }

  /**
   * 获取服务商统计信息
   */
  async getProviderStats(providerId: number) {
    const provider = await this.findById(providerId);
    
    // 这里可以添加更多统计信息，如月收入、服务时长等
    return {
      totalOrders: provider.totalOrders,
      ratingAverage: provider.ratingAverage,
      ratingCount: provider.ratingCount,
      experienceYears: provider.experienceYears,
      certificationStatus: provider.certificationStatus,
      serviceRadius: provider.serviceRadius,
    };
  }
}
```

## **4.2 多阶段审核工作流**

### **审核服务实现**

```typescript
// src/modules/review/review.service.ts
import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ServiceProvider, CertificationStatus } from '../../database/entities/service-provider.entity';
import { ProvidersService } from '../providers/providers.service';

export interface ReviewDecision {
  status: CertificationStatus;
  note?: string;
  reviewerId: number;
}

export interface AutoReviewResult {
  passed: boolean;
  status: CertificationStatus;
  note: string;
  issues: string[];
}

@Injectable()
export class ReviewService {
  constructor(
    @InjectRepository(ServiceProvider)
    private readonly providerRepository: Repository<ServiceProvider>,
    private readonly providersService: ProvidersService,
  ) {}

  /**
   * 自动审核检查
   * 实现需求：FR-PM-004
   */
  async autoReview(providerId: number): Promise<AutoReviewResult> {
    const provider = await this.providersService.findById(providerId);
    const issues: string[] = [];

    // 基础信息完整性检查
    if (!provider.businessName) {
      issues.push('缺少商户名称');
    }

    if (!provider.introduction || provider.introduction.length < 20) {
      issues.push('服务介绍过于简单，请详细描述服务内容和优势');
    }

    if (!provider.baseLongitude || !provider.baseLatitude) {
      issues.push('缺少服务基地地址');
    }

    if (provider.experienceYears < 1) {
      issues.push('从业经验不足1年');
    }

    // 认证信息检查
    if (!provider.certifications || provider.certifications.length === 0) {
      issues.push('缺少认证材料');
    } else {
      const hasIdentityCert = provider.certifications.some(cert => 
        cert.certificationType === 'identity' && cert.certificateUrl
      );
      const hasSkillCert = provider.certifications.some(cert => 
        cert.certificationType === 'skill' && cert.certificateUrl
      );

      if (!hasIdentityCert) {
        issues.push('缺少身份认证材料');
      }

      if (!hasSkillCert) {
        issues.push('缺少技能认证材料');
      }

      // 检查证书是否过期
      const expiredCerts = provider.certifications.filter(cert => 
        cert.expireDate && cert.expireDate < new Date()
      );

      if (expiredCerts.length > 0) {
        issues.push(`有${expiredCerts.length}个证书已过期`);
      }
    }

    // 工作时间设置检查
    if (!provider.workingHours || Object.keys(provider.workingHours).length === 0) {
      issues.push('缺少工作时间设置');
    }

    // 判断审核结果
    if (issues.length === 0) {
      return {
        passed: true,
        status: CertificationStatus.PENDING,
        note: '自动审核通过，等待人工审核',
        issues: [],
      };
    } else if (issues.length <= 2 && !issues.some(issue => issue.includes('身份认证') || issue.includes('技能认证'))) {
      return {
        passed: false,
        status: CertificationStatus.PENDING,
        note: `存在${issues.length}个问题，请修正后重新提交`,
        issues,
      };
    } else {
      return {
        passed: false,
        status: CertificationStatus.REJECTED,
        note: `审核未通过：${issues.join('；')}`,
        issues,
      };
    }
  }

  /**
   * 人工审核
   * 实现需求：FR-PM-005
   */
  async manualReview(
    providerId: number,
    reviewerId: number,
    decision: Omit<ReviewDecision, 'reviewerId'>,
  ): Promise<ServiceProvider> {
    const provider = await this.providersService.findById(providerId);

    if (provider.certificationStatus === CertificationStatus.APPROVED) {
      throw new BadRequestException('服务商已通过审核');
    }

    // 更新审核状态
    await this.providerRepository.update(providerId, {
      certificationStatus: decision.status,
      certificationNote: decision.note || '',
    });

    // 记录审核日志（这里可以扩展为独立的审核日志表）
    console.log(`审核记录：审核员${reviewerId}对服务商${providerId}进行了${decision.status}操作`);

    return this.providersService.findById(providerId);
  }

  /**
   * 批量审核
   */
  async batchReview(
    providerIds: number[],
    reviewerId: number,
    decision: Omit<ReviewDecision, 'reviewerId'>,
  ): Promise<void> {
    for (const providerId of providerIds) {
      try {
        await this.manualReview(providerId, reviewerId, decision);
      } catch (error) {
        console.error(`批量审核失败，服务商ID: ${providerId}`, error.message);
      }
    }
  }

  /**
   * 获取待审核服务商列表
   */
  async getPendingReviews(page: number = 1, limit: number = 10) {
    return this.providersService.findAll(page, limit, CertificationStatus.PENDING);
  }

  /**
   * 重新提交审核
   */
  async resubmitReview(providerId: number): Promise<AutoReviewResult> {
    const provider = await this.providersService.findById(providerId);

    if (provider.certificationStatus === CertificationStatus.APPROVED) {
      throw new BadRequestException('服务商已通过审核，无需重新提交');
    }

    // 重置状态为待审核
    await this.providerRepository.update(providerId, {
      certificationStatus: CertificationStatus.PENDING,
      certificationNote: '重新提交审核',
    });

    // 执行自动审核
    const autoReviewResult = await this.autoReview(providerId);

    // 更新审核结果
    await this.providerRepository.update(providerId, {
      certificationStatus: autoReviewResult.status,
      certificationNote: autoReviewResult.note,
    });

    return autoReviewResult;
  }
}
```

通过本篇教程，我们构建了一个完整的服务商管理和多阶段审核系统，包括服务商注册、资质认证、自动审核、人工审核等核心功能。这确保了平台上服务商的质量和可信度，为用户提供可靠的服务保障。

---

**[请告诉我"继续"，我将提供第五篇：服务管理与智能匹配引擎。]**
