# 第十篇：用户端界面实现

## 概述

在前面的教程中，我们已经完成了React前端项目的初始化与基础配置。本篇教程将实现用户端的完整界面，包括认证相关页面、服务浏览与预订、订单管理等核心功能。

我们将实现：
- 登录注册页面设计
- 手机验证码登录
- 微信授权登录集成
- 服务分类展示
- 服务列表与搜索
- 服务详情页面
- 预订流程实现
- 订单管理界面

## 技术栈

- **UI框架**: Ant Design 5.x
- **状态管理**: Zustand + React Query
- **表单处理**: Ant Design Form
- **图标库**: @ant-design/icons
- **样式方案**: CSS Modules + Less
- **地图服务**: 高德地图API

## 页面结构规划

```
src/pages/
├── Auth/                    # 认证相关页面
│   ├── Login/              # 登录页面
│   ├── Register/           # 注册页面
│   └── ForgotPassword/     # 忘记密码页面
├── User/                   # 用户端页面
│   ├── Dashboard/          # 用户仪表盘
│   ├── Profile/            # 个人资料
│   ├── Orders/             # 订单管理
│   ├── Services/           # 服务浏览
│   └── Messages/           # 消息中心
└── Common/                 # 通用页面
    ├── ServiceDetail/      # 服务详情
    └── OrderDetail/        # 订单详情
```

---

## 10.1 认证相关页面

### 10.1.1 登录页面设计

实现现代化的登录界面，支持多种登录方式：

```typescript
// src/pages/Auth/Login/index.tsx
import React, { useState } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  Checkbox,
  Divider,
  message,
  Tabs,
  Row,
  Col,
  Card,
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  MobileOutlined,
  SafetyOutlined,
  WechatOutlined,
} from '@ant-design/icons';
import { useAuthStore } from '@stores/authStore';
import { authService } from '@services/authService';
import { VALIDATION_RULES } from '@constants/index';
import styles from './index.module.less';

interface LoginFormData {
  username: string;
  password: string;
  phone: string;
  captcha: string;
  remember: boolean;
}

const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, setLoading } = useAuthStore();
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('password');
  const [loading, setLoadingState] = useState(false);
  const [captchaLoading, setCaptchaLoading] = useState(false);

  // 获取重定向路径
  const from = (location.state as any)?.from?.pathname || '/user/dashboard';

  // 密码登录
  const handlePasswordLogin = async (values: LoginFormData) => {
    try {
      setLoadingState(true);
      setLoading(true);

      const response = await authService.login({
        username: values.username,
        password: values.password,
      });

      login(response);
      message.success('登录成功');
      navigate(from, { replace: true });
    } catch (error: any) {
      message.error(error.message || '登录失败');
    } finally {
      setLoadingState(false);
      setLoading(false);
    }
  };

  // 验证码登录
  const handleSmsLogin = async (values: LoginFormData) => {
    try {
      setLoadingState(true);
      setLoading(true);

      const response = await authService.smsLogin({
        phone: values.phone,
        captcha: values.captcha,
      });

      login(response);
      message.success('登录成功');
      navigate(from, { replace: true });
    } catch (error: any) {
      message.error(error.message || '登录失败');
    } finally {
      setLoadingState(false);
      setLoading(false);
    }
  };

  // 发送验证码
  const handleSendCaptcha = async () => {
    try {
      const phone = form.getFieldValue('phone');
      if (!phone) {
        message.error('请输入手机号');
        return;
      }

      setCaptchaLoading(true);
      await authService.sendSmsCode({ phone, type: 'login' });
      message.success('验证码已发送');
    } catch (error: any) {
      message.error(error.message || '发送失败');
    } finally {
      setCaptchaLoading(false);
    }
  };

  // 微信登录
  const handleWechatLogin = () => {
    // 跳转到微信授权页面
    window.location.href = '/api/auth/wechat';
  };

  const passwordLoginForm = (
    <Form
      form={form}
      name="password-login"
      onFinish={handlePasswordLogin}
      autoComplete="off"
      size="large"
    >
      <Form.Item
        name="username"
        rules={[VALIDATION_RULES.REQUIRED]}
      >
        <Input
          prefix={<UserOutlined />}
          placeholder="用户名/邮箱/手机号"
        />
      </Form.Item>

      <Form.Item
        name="password"
        rules={[VALIDATION_RULES.REQUIRED]}
      >
        <Input.Password
          prefix={<LockOutlined />}
          placeholder="密码"
        />
      </Form.Item>

      <Form.Item>
        <Row justify="space-between" align="middle">
          <Col>
            <Form.Item name="remember" valuePropName="checked" noStyle>
              <Checkbox>记住我</Checkbox>
            </Form.Item>
          </Col>
          <Col>
            <Link to="/forgot-password">忘记密码？</Link>
          </Col>
        </Row>
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          block
        >
          登录
        </Button>
      </Form.Item>
    </Form>
  );

  const smsLoginForm = (
    <Form
      form={form}
      name="sms-login"
      onFinish={handleSmsLogin}
      autoComplete="off"
      size="large"
    >
      <Form.Item
        name="phone"
        rules={[VALIDATION_RULES.REQUIRED, VALIDATION_RULES.PHONE]}
      >
        <Input
          prefix={<MobileOutlined />}
          placeholder="手机号"
        />
      </Form.Item>

      <Form.Item
        name="captcha"
        rules={[VALIDATION_RULES.REQUIRED]}
      >
        <Row gutter={8}>
          <Col span={16}>
            <Input
              prefix={<SafetyOutlined />}
              placeholder="验证码"
            />
          </Col>
          <Col span={8}>
            <Button
              loading={captchaLoading}
              onClick={handleSendCaptcha}
              block
            >
              获取验证码
            </Button>
          </Col>
        </Row>
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          block
        >
          登录
        </Button>
      </Form.Item>
    </Form>
  );

  return (
    <div className={styles.loginContainer}>
      <div className={styles.loginBox}>
        <Card>
          <div className={styles.header}>
            <h1>欢迎登录</h1>
            <p>家政维修服务平台</p>
          </div>

          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            centered
            items={[
              {
                key: 'password',
                label: '密码登录',
                children: passwordLoginForm,
              },
              {
                key: 'sms',
                label: '验证码登录',
                children: smsLoginForm,
              },
            ]}
          />

          <Divider>其他登录方式</Divider>

          <div className={styles.socialLogin}>
            <Button
              icon={<WechatOutlined />}
              onClick={handleWechatLogin}
              size="large"
              block
            >
              微信登录
            </Button>
          </div>

          <div className={styles.footer}>
            <span>还没有账号？</span>
            <Link to="/register">立即注册</Link>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Login;
```

```less
// src/pages/Auth/Login/index.module.less
.loginContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.loginBox {
  width: 100%;
  max-width: 400px;

  .ant-card {
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
}

.header {
  text-align: center;
  margin-bottom: 32px;

  h1 {
    font-size: 28px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
  }

  p {
    color: #6b7280;
    font-size: 14px;
    margin: 0;
  }
}

.socialLogin {
  margin-top: 16px;

  .ant-btn {
    height: 44px;
    border-radius: 8px;
    font-weight: 500;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

.footer {
  text-align: center;
  margin-top: 24px;
  color: #6b7280;

  a {
    color: #1890ff;
    text-decoration: none;
    margin-left: 8px;

    &:hover {
      text-decoration: underline;
    }
  }
}

// 响应式设计
@media (max-width: 576px) {
  .loginContainer {
    padding: 16px;
  }

  .loginBox {
    max-width: 100%;
  }
}
```

### 10.1.2 注册页面实现

实现用户注册功能，包括表单验证和协议确认：

```typescript
// src/pages/Auth/Register/index.tsx
import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  Checkbox,
  message,
  Row,
  Col,
  Card,
  Select,
  Steps,
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  MobileOutlined,
  SafetyOutlined,
  MailOutlined,
} from '@ant-design/icons';
import { authService } from '@services/authService';
import { VALIDATION_RULES } from '@constants/index';
import styles from './index.module.less';

interface RegisterFormData {
  username: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  captcha: string;
  userType: 'user' | 'service_provider';
  agreement: boolean;
}

const Register: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [captchaLoading, setCaptchaLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  // 发送验证码
  const handleSendCaptcha = async () => {
    try {
      const phone = form.getFieldValue('phone');
      if (!phone) {
        message.error('请输入手机号');
        return;
      }

      setCaptchaLoading(true);
      await authService.sendSmsCode({ phone, type: 'register' });
      message.success('验证码已发送');
    } catch (error: any) {
      message.error(error.message || '发送失败');
    } finally {
      setCaptchaLoading(false);
    }
  };

  // 注册提交
  const handleRegister = async (values: RegisterFormData) => {
    try {
      setLoading(true);

      await authService.register({
        username: values.username,
        email: values.email,
        phone: values.phone,
        password: values.password,
        captcha: values.captcha,
        userType: values.userType,
      });

      message.success('注册成功，请登录');
      navigate('/login');
    } catch (error: any) {
      message.error(error.message || '注册失败');
    } finally {
      setLoading(false);
    }
  };

  // 下一步
  const handleNext = async () => {
    try {
      if (currentStep === 0) {
        await form.validateFields(['username', 'email', 'phone']);
        setCurrentStep(1);
      } else if (currentStep === 1) {
        await form.validateFields(['password', 'confirmPassword']);
        setCurrentStep(2);
      }
    } catch (error) {
      // 验证失败，不执行下一步
    }
  };

  // 上一步
  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
  };

  const steps = [
    {
      title: '基本信息',
      content: (
        <>
          <Form.Item
            name="username"
            rules={[
              VALIDATION_RULES.REQUIRED,
              { min: 3, max: 20, message: '用户名长度应在3-20位之间' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名"
              size="large"
            />
          </Form.Item>

          <Form.Item
            name="email"
            rules={[VALIDATION_RULES.REQUIRED, VALIDATION_RULES.EMAIL]}
          >
            <Input
              prefix={<MailOutlined />}
              placeholder="邮箱地址"
              size="large"
            />
          </Form.Item>

          <Form.Item
            name="phone"
            rules={[VALIDATION_RULES.REQUIRED, VALIDATION_RULES.PHONE]}
          >
            <Input
              prefix={<MobileOutlined />}
              placeholder="手机号"
              size="large"
            />
          </Form.Item>

          <Form.Item
            name="userType"
            rules={[VALIDATION_RULES.REQUIRED]}
            initialValue="user"
          >
            <Select placeholder="选择用户类型" size="large">
              <Select.Option value="user">普通用户</Select.Option>
              <Select.Option value="service_provider">服务商</Select.Option>
            </Select>
          </Form.Item>
        </>
      ),
    },
    {
      title: '设置密码',
      content: (
        <>
          <Form.Item
            name="password"
            rules={[
              VALIDATION_RULES.REQUIRED,
              VALIDATION_RULES.PASSWORD,
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
              size="large"
            />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            dependencies={['password']}
            rules={[
              VALIDATION_RULES.REQUIRED,
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="确认密码"
              size="large"
            />
          </Form.Item>
        </>
      ),
    },
    {
      title: '验证手机',
      content: (
        <>
          <Form.Item
            name="captcha"
            rules={[VALIDATION_RULES.REQUIRED]}
          >
            <Row gutter={8}>
              <Col span={16}>
                <Input
                  prefix={<SafetyOutlined />}
                  placeholder="验证码"
                  size="large"
                />
              </Col>
              <Col span={8}>
                <Button
                  loading={captchaLoading}
                  onClick={handleSendCaptcha}
                  size="large"
                  block
                >
                  获取验证码
                </Button>
              </Col>
            </Row>
          </Form.Item>

          <Form.Item
            name="agreement"
            valuePropName="checked"
            rules={[
              {
                validator: (_, value) =>
                  value ? Promise.resolve() : Promise.reject(new Error('请同意用户协议')),
              },
            ]}
          >
            <Checkbox>
              我已阅读并同意
              <a href="/terms" target="_blank" rel="noopener noreferrer">
                《用户协议》
              </a>
              和
              <a href="/privacy" target="_blank" rel="noopener noreferrer">
                《隐私政策》
              </a>
            </Checkbox>
          </Form.Item>
        </>
      ),
    },
  ];

  return (
    <div className={styles.registerContainer}>
      <div className={styles.registerBox}>
        <Card>
          <div className={styles.header}>
            <h1>用户注册</h1>
            <p>创建您的账号</p>
          </div>

          <Steps current={currentStep} className={styles.steps}>
            {steps.map((step) => (
              <Steps.Step key={step.title} title={step.title} />
            ))}
          </Steps>

          <Form
            form={form}
            name="register"
            onFinish={handleRegister}
            autoComplete="off"
            className={styles.form}
          >
            <div className={styles.stepContent}>
              {steps[currentStep].content}
            </div>

            <div className={styles.actions}>
              {currentStep > 0 && (
                <Button onClick={handlePrev}>
                  上一步
                </Button>
              )}

              {currentStep < steps.length - 1 ? (
                <Button type="primary" onClick={handleNext}>
                  下一步
                </Button>
              ) : (
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                >
                  完成注册
                </Button>
              )}
            </div>
          </Form>

          <div className={styles.footer}>
            <span>已有账号？</span>
            <Link to="/login">立即登录</Link>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Register;
```

```less
// src/pages/Auth/Register/index.module.less
.registerContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.registerBox {
  width: 100%;
  max-width: 500px;

  .ant-card {
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
}

.header {
  text-align: center;
  margin-bottom: 32px;

  h1 {
    font-size: 28px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
  }

  p {
    color: #6b7280;
    font-size: 14px;
    margin: 0;
  }
}

.steps {
  margin-bottom: 32px;
}

.form {
  .stepContent {
    min-height: 280px;
    padding: 16px 0;
  }
}

.actions {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;

  .ant-btn {
    min-width: 100px;
  }
}

.footer {
  text-align: center;
  margin-top: 24px;
  color: #6b7280;

  a {
    color: #1890ff;
    text-decoration: none;
    margin-left: 8px;

    &:hover {
      text-decoration: underline;
    }
  }
}

// 响应式设计
@media (max-width: 576px) {
  .registerContainer {
    padding: 16px;
  }

  .registerBox {
    max-width: 100%;
  }

  .form .stepContent {
    min-height: 240px;
  }
}
```

---

## 10.2 服务浏览与预订

### 10.2.1 服务分类展示

实现服务分类的层级展示和导航：

```typescript
// src/pages/User/Services/index.tsx
import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Input,
  Select,
  Button,
  Spin,
  Empty,
  Pagination,
  Breadcrumb,
  Space,
  Tag,
  Rate,
  Image,
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { serviceService } from '@services/serviceService';
import { ServiceCategory, Service } from '@types/index';
import styles from './index.module.less';

const { Search } = Input;
const { Option } = Select;

interface ServiceFilters {
  categoryId?: string;
  keyword?: string;
  priceRange?: [number, number];
  sortBy?: 'price' | 'rating' | 'distance';
  sortOrder?: 'asc' | 'desc';
}

const UserServices: React.FC = () => {
  const navigate = useNavigate();
  const [filters, setFilters] = useState<ServiceFilters>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(12);
  const [selectedCategory, setSelectedCategory] = useState<ServiceCategory | null>(null);

  // 获取服务分类
  const { data: categories, isLoading: categoriesLoading } = useQuery({
    queryKey: ['service-categories'],
    queryFn: () => serviceService.getCategories(),
  });

  // 获取服务列表
  const { data: servicesData, isLoading: servicesLoading, refetch } = useQuery({
    queryKey: ['services', filters, currentPage, pageSize],
    queryFn: () => serviceService.getServices({
      ...filters,
      page: currentPage,
      limit: pageSize,
    }),
  });

  // 搜索处理
  const handleSearch = (keyword: string) => {
    setFilters({ ...filters, keyword });
    setCurrentPage(1);
  };

  // 分类选择
  const handleCategorySelect = (category: ServiceCategory) => {
    setSelectedCategory(category);
    setFilters({ ...filters, categoryId: category.id });
    setCurrentPage(1);
  };

  // 排序处理
  const handleSortChange = (value: string) => {
    const [sortBy, sortOrder] = value.split('-');
    setFilters({
      ...filters,
      sortBy: sortBy as any,
      sortOrder: sortOrder as any
    });
    setCurrentPage(1);
  };

  // 查看服务详情
  const handleServiceDetail = (serviceId: string) => {
    navigate(`/service/${serviceId}`);
  };

  // 立即预订
  const handleBookService = (serviceId: string) => {
    navigate(`/service/${serviceId}/book`);
  };

  return (
    <div className={styles.servicesContainer}>
      {/* 面包屑导航 */}
      <Breadcrumb className={styles.breadcrumb}>
        <Breadcrumb.Item>首页</Breadcrumb.Item>
        <Breadcrumb.Item>服务中心</Breadcrumb.Item>
        {selectedCategory && (
          <Breadcrumb.Item>{selectedCategory.name}</Breadcrumb.Item>
        )}
      </Breadcrumb>

      <Row gutter={[24, 24]}>
        {/* 左侧分类导航 */}
        <Col xs={24} md={6}>
          <Card title="服务分类" className={styles.categoryCard}>
            {categoriesLoading ? (
              <Spin />
            ) : (
              <div className={styles.categoryList}>
                <div
                  className={`${styles.categoryItem} ${!selectedCategory ? styles.active : ''}`}
                  onClick={() => {
                    setSelectedCategory(null);
                    setFilters({ ...filters, categoryId: undefined });
                  }}
                >
                  全部服务
                </div>
                {categories?.map((category) => (
                  <div key={category.id} className={styles.categoryGroup}>
                    <div
                      className={`${styles.categoryItem} ${
                        selectedCategory?.id === category.id ? styles.active : ''
                      }`}
                      onClick={() => handleCategorySelect(category)}
                    >
                      <span className={styles.categoryIcon}>{category.icon}</span>
                      {category.name}
                    </div>
                    {category.children && category.children.length > 0 && (
                      <div className={styles.subCategories}>
                        {category.children.map((subCategory) => (
                          <div
                            key={subCategory.id}
                            className={`${styles.subCategoryItem} ${
                              selectedCategory?.id === subCategory.id ? styles.active : ''
                            }`}
                            onClick={() => handleCategorySelect(subCategory)}
                          >
                            {subCategory.name}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </Card>
        </Col>

        {/* 右侧服务列表 */}
        <Col xs={24} md={18}>
          {/* 搜索和筛选 */}
          <Card className={styles.filterCard}>
            <Row gutter={[16, 16]} align="middle">
              <Col xs={24} sm={12} md={8}>
                <Search
                  placeholder="搜索服务"
                  allowClear
                  enterButton={<SearchOutlined />}
                  onSearch={handleSearch}
                />
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Select
                  placeholder="排序方式"
                  style={{ width: '100%' }}
                  onChange={handleSortChange}
                >
                  <Option value="rating-desc">评分从高到低</Option>
                  <Option value="price-asc">价格从低到高</Option>
                  <Option value="price-desc">价格从高到低</Option>
                  <Option value="distance-asc">距离最近</Option>
                </Select>
              </Col>
              <Col xs={24} sm={24} md={10}>
                <Space>
                  <Button icon={<FilterOutlined />}>
                    高级筛选
                  </Button>
                  <span className={styles.resultCount}>
                    共找到 {servicesData?.total || 0} 个服务
                  </span>
                </Space>
              </Col>
            </Row>
          </Card>

          {/* 服务列表 */}
          {servicesLoading ? (
            <div className={styles.loading}>
              <Spin size="large" />
            </div>
          ) : servicesData?.items?.length ? (
            <>
              <Row gutter={[16, 16]}>
                {servicesData.items.map((service) => (
                  <Col xs={24} sm={12} lg={8} key={service.id}>
                    <Card
                      hoverable
                      className={styles.serviceCard}
                      cover={
                        <div className={styles.serviceCover}>
                          <Image
                            src={service.image || '/default-service.jpg'}
                            alt={service.name}
                            preview={false}
                          />
                          <div className={styles.servicePrice}>
                            ¥{service.price}
                            <span className={styles.priceUnit}>/{service.unit}</span>
                          </div>
                        </div>
                      }
                      actions={[
                        <Button
                          type="link"
                          onClick={() => handleServiceDetail(service.id)}
                        >
                          查看详情
                        </Button>,
                        <Button
                          type="primary"
                          onClick={() => handleBookService(service.id)}
                        >
                          立即预订
                        </Button>,
                      ]}
                    >
                      <Card.Meta
                        title={
                          <div className={styles.serviceTitle}>
                            {service.name}
                            <Rate
                              disabled
                              defaultValue={service.rating}
                              style={{ fontSize: 12 }}
                            />
                          </div>
                        }
                        description={
                          <div className={styles.serviceInfo}>
                            <p className={styles.serviceDesc}>
                              {service.description}
                            </p>
                            <div className={styles.serviceMeta}>
                              <Space size="small">
                                <ClockCircleOutlined />
                                <span>{service.duration}分钟</span>
                              </Space>
                              <Space size="small">
                                <UserOutlined />
                                <span>{service.providerCount}位服务商</span>
                              </Space>
                            </div>
                            <div className={styles.serviceTags}>
                              {service.tags?.map((tag) => (
                                <Tag key={tag} size="small">
                                  {tag}
                                </Tag>
                              ))}
                            </div>
                          </div>
                        }
                      />
                    </Card>
                  </Col>
                ))}
              </Row>

              {/* 分页 */}
              <div className={styles.pagination}>
                <Pagination
                  current={currentPage}
                  pageSize={pageSize}
                  total={servicesData.total}
                  showSizeChanger={false}
                  showQuickJumper
                  showTotal={(total, range) =>
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                  }
                  onChange={(page) => setCurrentPage(page)}
                />
              </div>
            </>
          ) : (
            <Card>
              <Empty description="暂无服务" />
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default UserServices;
```

```less
// src/pages/User/Services/index.module.less
.servicesContainer {
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.breadcrumb {
  margin-bottom: 24px;
  padding: 16px 24px;
  background: #fff;
  border-radius: 8px;
}

.categoryCard {
  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
  }
}

.categoryList {
  .categoryItem {
    padding: 12px 16px;
    cursor: pointer;
    border-radius: 6px;
    margin-bottom: 4px;
    transition: all 0.3s;
    display: flex;
    align-items: center;

    &:hover {
      background: #f5f5f5;
    }

    &.active {
      background: #e6f7ff;
      color: #1890ff;
      font-weight: 500;
    }
  }

  .categoryIcon {
    margin-right: 8px;
    font-size: 16px;
  }

  .subCategories {
    margin-left: 24px;

    .subCategoryItem {
      padding: 8px 12px;
      cursor: pointer;
      border-radius: 4px;
      margin-bottom: 2px;
      font-size: 13px;
      color: #666;
      transition: all 0.3s;

      &:hover {
        background: #f5f5f5;
        color: #333;
      }

      &.active {
        background: #e6f7ff;
        color: #1890ff;
      }
    }
  }
}

.filterCard {
  margin-bottom: 16px;

  .resultCount {
    color: #666;
    font-size: 14px;
  }
}

.loading {
  text-align: center;
  padding: 50px 0;
}

.serviceCard {
  height: 100%;

  .ant-card-cover {
    height: 200px;
    overflow: hidden;
  }

  .ant-card-actions {
    background: #fafafa;
  }
}

.serviceCover {
  position: relative;
  height: 200px;

  .ant-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .servicePrice {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;

    .priceUnit {
      font-size: 12px;
      font-weight: normal;
    }
  }
}

.serviceTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.serviceInfo {
  .serviceDesc {
    color: #666;
    font-size: 13px;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .serviceMeta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 12px;
    color: #999;
  }

  .serviceTags {
    .ant-tag {
      margin-bottom: 4px;
    }
  }
}

.pagination {
  text-align: center;
  margin-top: 32px;
}

// 响应式设计
@media (max-width: 768px) {
  .servicesContainer {
    padding: 16px;
  }

  .categoryCard {
    margin-bottom: 16px;
  }

  .filterCard {
    .ant-row {
      .ant-col {
        margin-bottom: 8px;
      }
    }
  }
}
```

---

## 10.3 订单管理界面

### 10.3.1 订单列表与筛选

实现订单管理功能，包括订单状态筛选和操作：

```typescript
// src/pages/User/Orders/index.tsx
import React, { useState } from 'react';
import {
  Card,
  Table,
  Tag,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Modal,
  Rate,
  Form,
  message,
  Descriptions,
  Image,
  Steps,
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  MessageOutlined,
  StarOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { orderService } from '@services/orderService';
import { reviewService } from '@services/reviewService';
import { Order, OrderStatus } from '@types/index';
import { ORDER_STATUS_MAP } from '@constants/index';
import dayjs from 'dayjs';
import styles from './index.module.less';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

interface OrderFilters {
  status?: OrderStatus;
  keyword?: string;
  dateRange?: [string, string];
}

const UserOrders: React.FC = () => {
  const queryClient = useQueryClient();
  const [filters, setFilters] = useState<OrderFilters>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [reviewVisible, setReviewVisible] = useState(false);
  const [reviewForm] = Form.useForm();

  // 获取订单列表
  const { data: ordersData, isLoading } = useQuery({
    queryKey: ['user-orders', filters, currentPage, pageSize],
    queryFn: () => orderService.getUserOrders({
      ...filters,
      page: currentPage,
      limit: pageSize,
    }),
  });

  // 取消订单
  const cancelOrderMutation = useMutation({
    mutationFn: (orderId: string) => orderService.cancelOrder(orderId),
    onSuccess: () => {
      message.success('订单已取消');
      queryClient.invalidateQueries({ queryKey: ['user-orders'] });
    },
    onError: (error: any) => {
      message.error(error.message || '取消失败');
    },
  });

  // 提交评价
  const submitReviewMutation = useMutation({
    mutationFn: (reviewData: any) => reviewService.createReview(reviewData),
    onSuccess: () => {
      message.success('评价提交成功');
      setReviewVisible(false);
      reviewForm.resetFields();
      queryClient.invalidateQueries({ queryKey: ['user-orders'] });
    },
    onError: (error: any) => {
      message.error(error.message || '评价失败');
    },
  });

  // 搜索处理
  const handleSearch = (keyword: string) => {
    setFilters({ ...filters, keyword });
    setCurrentPage(1);
  };

  // 状态筛选
  const handleStatusFilter = (status: OrderStatus | undefined) => {
    setFilters({ ...filters, status });
    setCurrentPage(1);
  };

  // 日期筛选
  const handleDateFilter = (dates: any) => {
    if (dates && dates.length === 2) {
      setFilters({
        ...filters,
        dateRange: [dates[0].format('YYYY-MM-DD'), dates[1].format('YYYY-MM-DD')],
      });
    } else {
      setFilters({ ...filters, dateRange: undefined });
    }
    setCurrentPage(1);
  };

  // 查看订单详情
  const handleViewDetail = (order: Order) => {
    setSelectedOrder(order);
    setDetailVisible(true);
  };

  // 取消订单
  const handleCancelOrder = (order: Order) => {
    Modal.confirm({
      title: '确认取消订单',
      icon: <ExclamationCircleOutlined />,
      content: '取消后无法恢复，确定要取消这个订单吗？',
      onOk: () => cancelOrderMutation.mutate(order.id),
    });
  };

  // 评价订单
  const handleReviewOrder = (order: Order) => {
    setSelectedOrder(order);
    setReviewVisible(true);
  };

  // 提交评价
  const handleSubmitReview = async (values: any) => {
    if (!selectedOrder) return;

    await submitReviewMutation.mutateAsync({
      orderId: selectedOrder.id,
      ...values,
    });
  };

  // 联系服务商
  const handleContactProvider = (order: Order) => {
    // 跳转到聊天页面
    window.open(`/chat?orderId=${order.id}`, '_blank');
  };

  const columns = [
    {
      title: '订单信息',
      key: 'orderInfo',
      render: (record: Order) => (
        <div className={styles.orderInfo}>
          <div className={styles.orderNo}>订单号：{record.orderNo}</div>
          <div className={styles.serviceName}>{record.service?.name}</div>
          <div className={styles.orderTime}>
            下单时间：{dayjs(record.createdAt).format('YYYY-MM-DD HH:mm')}
          </div>
        </div>
      ),
    },
    {
      title: '服务商',
      key: 'provider',
      render: (record: Order) => (
        <div className={styles.providerInfo}>
          <div className={styles.providerName}>{record.serviceProvider?.name}</div>
          <div className={styles.providerPhone}>{record.serviceProvider?.phone}</div>
        </div>
      ),
    },
    {
      title: '服务时间',
      dataIndex: 'scheduledTime',
      render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '订单金额',
      dataIndex: 'totalAmount',
      render: (amount: number) => <span className={styles.amount}>¥{amount}</span>,
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      render: (status: OrderStatus) => {
        const statusConfig = ORDER_STATUS_MAP[status];
        return <Tag color={statusConfig.color}>{statusConfig.text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: Order) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>

          {record.status === OrderStatus.PENDING && (
            <Button
              type="link"
              danger
              onClick={() => handleCancelOrder(record)}
            >
              取消
            </Button>
          )}

          {record.status === OrderStatus.COMPLETED && !record.hasReview && (
            <Button
              type="link"
              icon={<StarOutlined />}
              onClick={() => handleReviewOrder(record)}
            >
              评价
            </Button>
          )}

          <Button
            type="link"
            icon={<MessageOutlined />}
            onClick={() => handleContactProvider(record)}
          >
            联系
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.ordersContainer}>
      <Card>
        <div className={styles.header}>
          <h2>我的订单</h2>

          {/* 筛选条件 */}
          <div className={styles.filters}>
            <Space wrap>
              <Search
                placeholder="搜索订单号或服务名称"
                allowClear
                style={{ width: 250 }}
                onSearch={handleSearch}
              />

              <Select
                placeholder="订单状态"
                allowClear
                style={{ width: 120 }}
                onChange={handleStatusFilter}
              >
                <Option value={OrderStatus.PENDING}>待确认</Option>
                <Option value={OrderStatus.CONFIRMED}>已确认</Option>
                <Option value={OrderStatus.IN_PROGRESS}>进行中</Option>
                <Option value={OrderStatus.COMPLETED}>已完成</Option>
                <Option value={OrderStatus.CANCELLED}>已取消</Option>
              </Select>

              <RangePicker
                placeholder={['开始日期', '结束日期']}
                onChange={handleDateFilter}
              />
            </Space>
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={ordersData?.items}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: currentPage,
            pageSize,
            total: ordersData?.total,
            showSizeChanger: false,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page) => setCurrentPage(page),
          }}
        />
      </Card>

      {/* 订单详情弹窗 */}
      <Modal
        title="订单详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={null}
        width={800}
      >
        {selectedOrder && (
          <div className={styles.orderDetail}>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="订单号" span={2}>
                {selectedOrder.orderNo}
              </Descriptions.Item>
              <Descriptions.Item label="服务名称">
                {selectedOrder.service?.name}
              </Descriptions.Item>
              <Descriptions.Item label="服务商">
                {selectedOrder.serviceProvider?.name}
              </Descriptions.Item>
              <Descriptions.Item label="服务时间">
                {dayjs(selectedOrder.scheduledTime).format('YYYY-MM-DD HH:mm')}
              </Descriptions.Item>
              <Descriptions.Item label="订单金额">
                ¥{selectedOrder.totalAmount}
              </Descriptions.Item>
              <Descriptions.Item label="服务地址" span={2}>
                {selectedOrder.address}
              </Descriptions.Item>
              <Descriptions.Item label="备注说明" span={2}>
                {selectedOrder.description || '无'}
              </Descriptions.Item>
            </Descriptions>

            {/* 订单进度 */}
            <div className={styles.orderProgress}>
              <h4>订单进度</h4>
              <Steps
                current={getOrderStep(selectedOrder.status)}
                items={[
                  { title: '订单提交' },
                  { title: '商家确认' },
                  { title: '服务进行中' },
                  { title: '服务完成' },
                ]}
              />
            </div>
          </div>
        )}
      </Modal>

      {/* 评价弹窗 */}
      <Modal
        title="服务评价"
        open={reviewVisible}
        onCancel={() => setReviewVisible(false)}
        onOk={() => reviewForm.submit()}
        confirmLoading={submitReviewMutation.isPending}
      >
        <Form
          form={reviewForm}
          layout="vertical"
          onFinish={handleSubmitReview}
        >
          <Form.Item
            name="serviceQualityScore"
            label="服务质量"
            rules={[{ required: true, message: '请评分' }]}
          >
            <Rate />
          </Form.Item>

          <Form.Item
            name="serviceAttitudeScore"
            label="服务态度"
            rules={[{ required: true, message: '请评分' }]}
          >
            <Rate />
          </Form.Item>

          <Form.Item
            name="timelinessScore"
            label="时效性"
            rules={[{ required: true, message: '请评分' }]}
          >
            <Rate />
          </Form.Item>

          <Form.Item
            name="valueScore"
            label="性价比"
            rules={[{ required: true, message: '请评分' }]}
          >
            <Rate />
          </Form.Item>

          <Form.Item
            name="comment"
            label="评价内容"
          >
            <TextArea
              rows={4}
              placeholder="请描述您的服务体验"
              maxLength={500}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

// 获取订单步骤
const getOrderStep = (status: OrderStatus): number => {
  switch (status) {
    case OrderStatus.PENDING:
      return 0;
    case OrderStatus.CONFIRMED:
      return 1;
    case OrderStatus.IN_PROGRESS:
      return 2;
    case OrderStatus.COMPLETED:
      return 3;
    default:
      return 0;
  }
};

export default UserOrders;
```

```less
// src/pages/User/Orders/index.module.less
.ordersContainer {
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  h2 {
    margin: 0;
    color: #1f2937;
  }
}

.filters {
  .ant-space {
    flex-wrap: wrap;
  }
}

.orderInfo {
  .orderNo {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
  }

  .serviceName {
    color: #1890ff;
    margin-bottom: 4px;
  }

  .orderTime {
    font-size: 12px;
    color: #999;
  }
}

.providerInfo {
  .providerName {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .providerPhone {
    font-size: 12px;
    color: #666;
  }
}

.amount {
  font-weight: 600;
  color: #f5222d;
  font-size: 16px;
}

.orderDetail {
  .orderProgress {
    margin-top: 24px;

    h4 {
      margin-bottom: 16px;
      color: #1f2937;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ordersContainer {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .filters {
    width: 100%;

    .ant-space {
      width: 100%;

      .ant-space-item {
        width: 100%;

        .ant-input-search,
        .ant-select,
        .ant-picker {
          width: 100% !important;
        }
      }
    }
  }

  // 表格在移动端的优化
  .ant-table {
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
      padding: 8px 4px;
      font-size: 12px;
    }

    .ant-table-tbody > tr > td {
      .orderInfo,
      .providerInfo {
        .orderNo,
        .serviceName,
        .providerName {
          font-size: 12px;
        }

        .orderTime,
        .providerPhone {
          font-size: 10px;
        }
      }

      .amount {
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 576px) {
  // 在小屏幕上隐藏部分列
  .ant-table {
    .ant-table-thead > tr > th:nth-child(3),
    .ant-table-tbody > tr > td:nth-child(3) {
      display: none;
    }
  }
}
```

### 10.3.2 API服务集成

实现与后端API的集成服务：

```typescript
// src/services/authService.ts
import { httpService } from './http';
import type { LoginRequest, LoginResponse, User } from '@types/index';

export const authService = {
  // 密码登录
  login: (data: LoginRequest): Promise<LoginResponse> => {
    return httpService.post('/auth/login', data);
  },

  // 短信登录
  smsLogin: (data: { phone: string; captcha: string }): Promise<LoginResponse> => {
    return httpService.post('/auth/sms-login', data);
  },

  // 用户注册
  register: (data: {
    username: string;
    email: string;
    phone: string;
    password: string;
    captcha: string;
    userType: string;
  }): Promise<void> => {
    return httpService.post('/auth/register', data);
  },

  // 发送短信验证码
  sendSmsCode: (data: { phone: string; type: string }): Promise<void> => {
    return httpService.post('/auth/send-sms', data);
  },

  // 获取用户信息
  getUserInfo: (): Promise<User> => {
    return httpService.get('/auth/profile');
  },

  // 刷新Token
  refreshToken: (refreshToken: string): Promise<LoginResponse> => {
    return httpService.post('/auth/refresh', { refreshToken });
  },

  // 登出
  logout: (): Promise<void> => {
    return httpService.post('/auth/logout');
  },
};
```

```typescript
// src/services/serviceService.ts
import { httpService } from './http';
import type { Service, ServiceCategory, PaginatedResponse } from '@types/index';

export const serviceService = {
  // 获取服务分类
  getCategories: (): Promise<ServiceCategory[]> => {
    return httpService.get('/services/categories');
  },

  // 获取服务列表
  getServices: (params: {
    categoryId?: string;
    keyword?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: string;
  }): Promise<PaginatedResponse<Service>> => {
    return httpService.get('/services', { params });
  },

  // 获取服务详情
  getServiceDetail: (serviceId: string): Promise<Service> => {
    return httpService.get(`/services/${serviceId}`);
  },

  // 搜索服务
  searchServices: (keyword: string): Promise<Service[]> => {
    return httpService.get('/services/search', { params: { keyword } });
  },
};
```

```typescript
// src/services/orderService.ts
import { httpService } from './http';
import type { Order, PaginatedResponse } from '@types/index';

export const orderService = {
  // 获取用户订单列表
  getUserOrders: (params: {
    status?: string;
    keyword?: string;
    dateRange?: [string, string];
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<Order>> => {
    return httpService.get('/orders/user', { params });
  },

  // 获取订单详情
  getOrderDetail: (orderId: string): Promise<Order> => {
    return httpService.get(`/orders/${orderId}`);
  },

  // 创建订单
  createOrder: (data: {
    serviceId: string;
    scheduledTime: string;
    address: string;
    description?: string;
  }): Promise<Order> => {
    return httpService.post('/orders', data);
  },

  // 取消订单
  cancelOrder: (orderId: string): Promise<void> => {
    return httpService.post(`/orders/${orderId}/cancel`);
  },

  // 确认订单
  confirmOrder: (orderId: string): Promise<void> => {
    return httpService.post(`/orders/${orderId}/confirm`);
  },
};
```

```typescript
// src/services/reviewService.ts
import { httpService } from './http';
import type { PaginatedResponse } from '@types/index';

export const reviewService = {
  // 创建评价
  createReview: (data: {
    orderId: string;
    serviceQualityScore: number;
    serviceAttitudeScore: number;
    timelinessScore: number;
    valueScore: number;
    comment?: string;
    images?: string[];
  }): Promise<void> => {
    return httpService.post('/reviews', data);
  },

  // 获取服务商评价列表
  getProviderReviews: (providerId: string, params?: {
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<any>> => {
    return httpService.get(`/reviews/provider/${providerId}`, { params });
  },

  // 获取用户评价列表
  getUserReviews: (params?: {
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<any>> => {
    return httpService.get('/reviews/user', { params });
  },
};
```

## 总结

本篇教程完成了用户端界面的实现，包括：

### 主要功能

1. **认证相关页面**
   - 现代化登录界面设计
   - 多种登录方式支持（密码、短信、微信）
   - 分步式注册流程
   - 表单验证和错误处理

2. **服务浏览与预订**
   - 层级化服务分类展示
   - 服务列表与搜索功能
   - 服务卡片设计
   - 筛选和排序功能

3. **订单管理界面**
   - 订单列表与状态筛选
   - 订单详情展示
   - 订单操作（取消、评价、联系）
   - 评价系统集成

### 技术亮点

- **响应式设计**: 适配移动端和桌面端
- **组件化开发**: 可复用的UI组件
- **状态管理**: React Query + Zustand
- **用户体验**: 加载状态、错误处理、交互反馈
- **API集成**: 完整的服务层封装

### 下一步

在下一篇教程中，我们将实现服务商端界面，包括服务商注册认证、订单接单管理、收入统计等功能。
