好的，我们进入第六篇。本篇我们将开始实现与企业微信的集成，首先是通讯录的同步，这是后续消息推送的基础。

---

### **《从零构建家政维修平台：打造稳健高效的混合架构后台应用》**

#### **第六篇：企业微信集成——通讯录同步与用户关联**

**摘要：** 要向企业微信成员（如维修工人、客服、调度员）推送工单通知，我们首先需要知道他们在企业微信中的唯一标识 `UserID`。本篇将详细讲解如何通过NestJS后端从企业微信拉取通讯录信息，并与我们自建后台的用户（`User`实体）进行关联，为后续的精准消息推送奠定基础。

---

**一、企业微信通讯录API概述与准备工作**

企业微信提供了强大的通讯录管理API，允许应用获取企业成员、部门等信息。

1.  **了解通讯录API：**
    * 企业微信的通讯录API需要独立的 `access_token`，这个 `access_token` 通常通过 `corpid` 和**通讯录管理secret**（或具备通讯录管理权限的应用secret）获取。
    * 主要API包括：获取部门列表、获取部门成员详情等。
    * **文档参考：** 务必查阅最新的企业微信开发文档“通讯录管理”部分，了解API接口、参数和返回格式。`https://developer.work.weixin.qq.com/document/path/90196`

2.  **企业微信管理后台配置：**
    * **获取企业ID (CorpID)：** 登录企业微信管理后台（`work.weixin.qq.com`），在“我的企业”页面可以找到你的CorpID。
    * **创建/配置应用（获取Secret）：**
        * 如果你之前有自建应用，确保它拥有**“通讯录管理”**的权限。进入“应用管理” -> “自建应用”，点击你的应用，在“API权限”部分，勾选“通讯录管理”相关权限。
        * 如果没有，可以专门创建一个“通讯录同步”的自建应用，并赋予它通讯录管理权限。记录下这个应用的 `AgentId` 和 `Secret`。
    * **IP白名单：** 尽管本篇主要讲从企业微信拉取数据（我们主动发起请求），但为了未来的消息回调等功能，**请提前将你本地开发环境的公网IP地址**（如果你的网络有固定公网IP）添加到企业微信管理后台的IP白名单中。部署到服务器后，也需要将服务器的公网IP添加到白名单。

**二、在NestJS后端配置企业微信SDK与常量**

我们将封装一个专门的服务来处理企业微信API的调用。

1.  **安装HTTP客户端库：** 我们需要一个HTTP客户端来调用企业微信API。NestJS推荐使用 `@nestjs/axios`（基于`axios`）。
    ```bash
    npm install @nestjs/axios axios
    # 或者 yarn add @nestjs/axios axios
    ```
2.  **创建企业微信配置和常量：**
    * 在 `src` 目录下新建一个 `wecom` 文件夹，并在其中创建 `wecom.constants.ts` 和 `wecom.config.ts` 文件。
    * 在 `wecom.constants.ts` 中定义企业微信API的基础URL和一些常量。
    * 在 `wecom.config.ts` 中通过环境变量读取 `CorpID` 和 `Secret`。

    ```typescript
    // src/wecom/wecom.constants.ts
    export const WECOM_API_BASE_URL = 'https://qyapi.weixin.qq.com/cgi-bin';
    export const WECOM_ACCESS_TOKEN_URL = `${WECOM_API_BASE_URL}/gettoken`;
    export const WECOM_USER_LIST_URL = `${WECOM_API_BASE_URL}/user/list`;
    export const WECOM_DEPARTMENT_LIST_URL = `${WECOM_API_BASE_URL}/department/list`;
    export const WECOM_MESSAGE_SEND_URL = `${WECOM_API_BASE_URL}/message/send`;
    ```

    ```typescript
    // src/wecom/wecom.config.ts
    import { registerAs } from '@nestjs/config';

    export default registerAs('wecom', () => ({
      corpId: process.env.WECOM_CORP_ID,
      contactSyncSecret: process.env.WECOM_CONTACT_SYNC_SECRET, // 用于通讯录同步的Secret
      agentId: process.env.WECOM_AGENT_ID, // 你的应用AgentId
      agentSecret: process.env.WECOM_AGENT_SECRET, // 你的应用Secret，用于发送消息等
    }));
    ```
    * **配置环境变量：** 在本地 `.env` 文件中添加这些配置。
        ```
        WECOM_CORP_ID=wwxxxxxxxxxxxxxxx
        WECOM_CONTACT_SYNC_SECRET=your_contact_sync_secret
        WECOM_AGENT_ID=100000x # 你的应用ID
        WECOM_AGENT_SECRET=your_app_secret # 你的应用Secret
        ```

**三、创建企业微信服务（WeCom Service）**

这个服务将包含所有与企业微信API交互的逻辑。

1.  **创建服务：** 在命令行中运行 `nest g service wecom`。
2.  **实现获取AccessToken：** 这是所有企业微信API调用的前置条件。
    * `AccessToken` 有效期通常为2小时，需要进行缓存和自动刷新。我们将简单缓存到内存，生产环境应考虑使用Redis等外部缓存。
3.  **实现通讯录同步逻辑：**
    * 获取所有部门。
    * 遍历部门，获取每个部门下的成员详情。
    * 将获取到的成员信息与我们的 `User` 实体进行关联。

    ```typescript
    // src/wecom/wecom.service.ts (部分代码)

    import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
    import { HttpService } from '@nestjs/axios';
    import { ConfigService } from '@nestjs/config';
    import { firstValueFrom } from 'rxjs';
    import {
      WECOM_ACCESS_TOKEN_URL,
      WECOM_DEPARTMENT_LIST_URL,
      WECOM_USER_LIST_URL,
      WECOM_MESSAGE_SEND_URL,
    } from './wecom.constants';
    import { InjectRepository } from '@nestjs/typeorm';
    import { Repository } from 'typeorm';
    import { User, UserRole } from '../user/user.entity'; // 导入User实体和角色

    @Injectable()
    export class WecomService {
      private readonly logger = new Logger(WecomService.name);
      private contactSyncAccessToken: string; // 通讯录同步的AccessToken
      private appAccessToken: string; // 应用消息推送的AccessToken
      private corpId: string;
      private contactSyncSecret: string;
      private agentId: string;
      private agentSecret: string;

      constructor(
        private readonly httpService: HttpService,
        private readonly configService: ConfigService,
        @InjectRepository(User)
        private usersRepository: Repository<User>, // 注入UserRepository用于关联
      ) {
        // 从ConfigService获取配置，推荐方式
        this.corpId = this.configService.get<string>('wecom.corpId');
        this.contactSyncSecret = this.configService.get<string>('wecom.contactSyncSecret');
        this.agentId = this.configService.get<string>('wecom.agentId');
        this.agentSecret = this.configService.get<string>('wecom.agentSecret');

        // 初始化时获取AccessToken
        this.getContactSyncAccessToken();
        this.getAppAccessToken();
        // 可以在这里设置定时任务，每隔一段时间刷新AccessToken
      }

      // 获取通讯录同步的AccessToken
      private async getContactSyncAccessToken(): Promise<string> {
        try {
          const url = `${WECOM_ACCESS_TOKEN_URL}?corpid=${this.corpId}&corpsecret=${this.contactSyncSecret}`;
          const response = await firstValueFrom(this.httpService.get(url));
          if (response.data.errcode === 0) {
            this.contactSyncAccessToken = response.data.access_token;
            this.logger.log('Successfully fetched ContactSyncAccessToken');
            return this.contactSyncAccessToken;
          } else {
            this.logger.error(`Failed to get ContactSyncAccessToken: ${response.data.errmsg}`);
            throw new InternalServerErrorException('Failed to get ContactSyncAccessToken.');
          }
        } catch (error) {
          this.logger.error('Error fetching ContactSyncAccessToken:', error.message);
          throw new InternalServerErrorException('Error fetching ContactSyncAccessToken.');
        }
      }

      // 获取应用推送消息的AccessToken
      private async getAppAccessToken(): Promise<string> {
        try {
          const url = `${WECOM_ACCESS_TOKEN_URL}?corpid=${this.corpId}&corpsecret=${this.agentSecret}`;
          const response = await firstValueFrom(this.httpService.get(url));
          if (response.data.errcode === 0) {
            this.appAccessToken = response.data.access_token;
            this.logger.log('Successfully fetched AppAccessToken');
            return this.appAccessToken;
          } else {
            this.logger.error(`Failed to get AppAccessToken: ${response.data.errmsg}`);
            throw new InternalServerErrorException('Failed to get AppAccessToken.');
          }
        } catch (error) {
          this.logger.error('Error fetching AppAccessToken:', error.message);
          throw new InternalServerErrorException('Error fetching AppAccessToken.');
        }
      }

      /**
       * 同步企业微信通讯录成员到本地数据库
       * 这是一个核心方法，通常在后台管理界面触发，或作为定时任务
       */
      async syncWeComUsers(): Promise<void> {
        this.logger.log('Starting WeCom user synchronization...');
        if (!this.contactSyncAccessToken) {
          await this.getContactSyncAccessToken(); // 确保AccessToken已获取
        }

        try {
          // 1. 获取部门列表
          const deptUrl = `${WECOM_DEPARTMENT_LIST_URL}?access_token=${this.contactSyncAccessToken}`;
          const deptRes = await firstValueFrom(this.httpService.get(deptUrl));
          const departments = deptRes.data.department;

          if (!departments || departments.length === 0) {
            this.logger.warn('No departments found in WeCom.');
            return;
          }

          const wecomUserIds: string[] = []; // 用于跟踪已同步的企微User ID

          for (const dept of departments) {
            // 2. 获取每个部门下的成员详情 (这里获取所有子部门成员)
            const userListUrl = `${WECOM_USER_LIST_URL}?access_token=${this.contactSyncAccessToken}&department_id=${dept.id}&fetch_child=1`;
            const userListRes = await firstValueFrom(this.httpService.get(userListUrl));
            const wecomUsers = userListRes.data.userlist;

            if (wecomUsers && wecomUsers.length > 0) {
              for (const wecomUser of wecomUsers) {
                wecomUserIds.push(wecomUser.userid);
                // 3. 将企微成员信息与本地用户进行关联或更新
                // 查找本地是否存在该企微User ID的用户
                let localUser = await this.usersRepository.findOne({ where: { wxWorkUserId: wecomUser.userid } });

                if (!localUser) {
                  // 如果本地没有，则尝试通过手机号/邮箱等信息匹配
                  // 如果依然没有，则创建新用户，并设置其企微ID
                  localUser = new User();
                  localUser.username = wecomUser.userid; // 默认使用企微ID作为用户名
                  localUser.password = 'random_generated_password'; // 需要生成一个随机密码或引导用户设置
                  localUser.wxWorkUserId = wecomUser.userid;
                  localUser.phone = wecomUser.mobile;
                  localUser.email = wecomUser.email;
                  localUser.status = wecomUser.status === 1 ? UserStatus.Active : UserStatus.Inactive; // 企微状态1为已启用

                  // 根据企微部门/职位信息，尝试赋予合适的本地角色
                  // 例如：如果企微部门包含“维修部”，则角色设置为Worker
                  if (wecomUser.department.includes(YOUR_REPAIR_DEPT_ID)) { // 替换为你的维修部门ID
                      localUser.role = UserRole.Worker;
                  } else if (wecomUser.department.includes(YOUR_CS_DEPT_ID)) { // 替换为你的客服部门ID
                      localUser.role = UserRole.CustomerService;
                  } else {
                      localUser.role = UserRole.Worker; // 默认给工人角色
                  }

                } else {
                  // 如果本地已存在，则更新其信息，特别是手机、邮箱和状态
                  localUser.phone = wecomUser.mobile;
                  localUser.email = wecomUser.email;
                  localUser.status = wecomUser.status === 1 ? UserStatus.Active : UserStatus.Inactive;
                }
                await this.usersRepository.save(localUser);
                this.logger.log(`Synced user: ${wecomUser.name} (UserID: ${wecomUser.userid})`);
              }
            }
          }

          // 可选：处理本地用户，如果其wxWorkUserId不在最新的wecomUserIds中，则将其状态设置为 inactive
          // 这是一个复杂但重要的同步步骤，确保本地数据与企微保持一致性

          this.logger.log('WeCom user synchronization completed.');

        } catch (error) {
          this.logger.error('Error syncing WeCom users:', error.message);
          throw new InternalServerErrorException('Failed to sync WeCom users.');
        }
      }

      // TODO: 添加发送消息的sendTextMessage方法，将在下一篇讲解
    }
    ```
4.  **更新 `User` 实体：** 在 `src/user/user.entity.ts` 中添加 `wxWorkUserId` 字段，用于存储企业微信的UserID。

    ```typescript
    // src/user/user.entity.ts (部分代码)

    @Entity('users')
    export class User {
      // ... 其他字段 ...

      @Column({ unique: true, nullable: true, name: 'wx_work_userid' })
      wxWorkUserId: string; // 企业微信User ID
    }
    ```
5.  **创建WeCom模块（WeCom Module）：**
    * 在 `src/wecom/` 目录下创建 `wecom.module.ts`。
    * 将 `WecomService` 和 `HttpModule` 注册到这个模块。
    * 由于 `WecomService` 需要访问 `User` 实体，需要在 `WecomModule` 中导入 `TypeOrmModule.forFeature([User])`。

    ```typescript
    // src/wecom/wecom.module.ts

    import { Module } from '@nestjs/common';
    import { HttpModule } from '@nestjs/axios';
    import { ConfigModule } from '@nestjs/config'; // 导入ConfigModule
    import { TypeOrmModule } from '@nestjs/typeorm';
    import { User } from '../user/user.entity'; // 导入User实体
    import { WecomService } from './wecom.service';
    import wecomConfig from './wecom.config'; // 导入配置

    @Module({
      imports: [
        HttpModule,
        ConfigModule.forFeature(wecomConfig), // 注册企业微信配置
        TypeOrmModule.forFeature([User]), // 导入User实体仓库
      ],
      providers: [WecomService],
      exports: [WecomService], // 导出WecomService，以便其他模块可以使用
    })
    export class WecomModule {}
    ```
6.  **在 `app.module.ts` 中导入 `WecomModule` 和 `ConfigModule`：**

    ```typescript
    // src/app.module.ts (部分代码)

    import { ConfigModule } from '@nestjs/config'; // 导入ConfigModule
    import { WecomModule } from './wecom/wecom.module'; // 导入企业微信模块

    @Module({
      imports: [
        // ... 其他模块 ...
        ConfigModule.forRoot({ isGlobal: true, load: [wecomConfig] }), // 全局加载配置
        WecomModule, // 导入企业微信模块
      ],
      // ...
    })
    export class AppModule {}
    ```

**四、创建通讯录同步API接口**

我们需要一个API接口来手动触发通讯录同步，方便测试和后台管理。

1.  **创建WeCom控制器：**
    * 在命令行中运行 `nest g controller wecom`。
    * 在 `src/wecom/wecom.controller.ts` 中添加一个同步API。

    ```typescript
    // src/wecom/wecom.controller.ts

    import { Controller, Post, UseGuards } from '@nestjs/common';
    import { WecomService } from './wecom.service';
    import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
    import { RolesGuard } from '../auth/guards/roles.guard';
    import { Roles } from '../auth/decorators/roles.decorator';
    import { UserRole } from '../user/user.entity';

    @Controller('wecom') // 接口前缀 /wecom
    @UseGuards(JwtAuthGuard, RolesGuard) // 保护接口，需要认证和权限
    export class WecomController {
      constructor(private readonly wecomService: WecomService) {}

      @Post('sync-users') // POST /wecom/sync-users
      @Roles(UserRole.Admin) // 只有管理员可以触发通讯录同步
      async syncUsers() {
        await this.wecomService.syncWeComUsers();
        return { message: 'WeCom users synchronization initiated.' };
      }
    }
    ```
2.  **在 `wecom.module.ts` 中注册控制器：**

    ```typescript
    // src/wecom/wecom.module.ts (部分代码)

    import { WecomController } from './wecom.controller'; // 导入控制器

    @Module({
      imports: [/* ... */],
      providers: [WecomService],
      controllers: [WecomController], // 添加控制器
      exports: [WecomService],
    })
    export class WecomModule {}
    ```

**五、测试通讯录同步**

1.  **启动后端应用：** 运行 `npm run start:dev`。
2.  **配置环境变量：** 确保 `.env` 文件中的 `WECOM_CORP_ID` 和 `WECOM_CONTACT_SYNC_SECRET` 正确填写。
3.  **使用API测试工具：**
    * **注册一个管理员用户并登录，获取其JWT Token。**
    * **发送同步请求：**
        * URL: `http://localhost:3000/wecom/sync-users`
        * Method: `POST`
        * Headers: `Authorization: Bearer YOUR_ADMIN_TOKEN`
        * Body: 空（或JSON `{}`）
    * 观察后端日志，查看是否成功获取企业微信通讯录信息，并同步到你的本地MySQL数据库的 `users` 表中。检查 `wx_work_userid` 字段是否已填充。

通过本篇教程，你已经实现了NestJS后端与企业微信通讯录的初步集成，能够拉取成员信息并与本地用户进行关联。这是实现工单消息精准推送的关键一步。

---

**[请告诉我“继续”，我将提供第七篇：企业微信集成——消息通知与工单状态联动。]**