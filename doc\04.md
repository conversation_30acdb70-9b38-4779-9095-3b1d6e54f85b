好的，我们继续第四篇。这篇我们将解决附件存储问题，集成腾讯云对象存储（COS）。

---

### **《从零构建家政维修平台：打造稳健高效的混合架构后台应用》**

#### **第四篇：附件存储与管理——集成腾讯云对象存储（COS）**

**摘要：** 家政维修平台会涉及大量图片（如报修现场照、维修前后对比图）。本篇将指导你如何在NestJS后端集成腾讯云对象存储（COS），实现图片等附件的高效上传、存储和管理。这将确保你的系统具备无限扩展的文件存储能力，并减轻服务器的负担。

---

**一、为什么选择腾讯云COS进行附件存储？**

在现代Web应用中，直接将用户上传的图片、文件等附件存储在服务器的磁盘上，是一个不推荐的做法。尤其对于家政维修平台这种可能产生大量照片的业务，服务器本地存储会带来诸多问题：

* **存储空间限制：** 服务器磁盘容量有限，很快就会耗尽。
* **高并发瓶颈：** 大量用户同时访问图片时，服务器I/O会成为性能瓶颈。
* **数据安全与可用性：** 服务器硬盘故障可能导致数据丢失，备份和容灾复杂。
* **带宽消耗：** 图片访问会占用服务器的带宽，影响主业务接口的性能。

**腾讯云对象存储（COS）**是专门为海量非结构化数据存储而设计的服务，它完美解决了上述问题，并提供了：

* **无限扩展性：** 无需担心存储空间不足。
* **高可用性与持久性：** 数据多副本存储，确保不丢失。
* **高并发访问：** 能够应对海量用户同时访问文件。
* **低成本：** 按量付费，性价比高。
* **易于集成CDN：** 进一步提升文件访问速度。

**二、开通腾讯云COS服务并创建存储桶**

在开始代码集成之前，你需要先在腾讯云控制台开通并配置COS服务。

1.  **登录腾讯云控制台：** 访问 `https://cloud.tencent.com/`，登录你的腾讯云账号。
2.  **开通COS服务：** 在产品列表中搜索并进入“对象存储（COS）”服务。按照指引开通服务。
3.  **创建存储桶（Bucket）：**
    * **存储桶名称：** 命名一个有意义的名称，例如 `home-repair-attachments`。名称全局唯一。
    * **所属地域：** **非常重要！** 选择与你未来部署轻量应用服务器相同的地域，这样可以走内网流量，降低存储桶和服务器之间的数据传输费用，并提升速度。
    * **访问权限：**
        * **公共读写：** 最简单，但安全性最低，不推荐。
        * **公有读私有写：** 对于需要公开访问的图片（如用户头像、报修图片），可以选择此项。上传后文件可直接通过URL访问。
        * **私有读写：** 最安全，文件访问需要签名认证。如果附件包含敏感信息或需要权限控制，选择此项。本教程中，我们假设报修图片是公开可读的，因此可选择**公有读私有写**或在COS控制台配置特定的读权限策略。
    * **确认创建：** 完成设置后，创建存储桶。

4.  **获取API密钥（SecretId和SecretKey）：**
    * 在腾讯云控制台顶部导航栏，点击你的头像，选择“访问管理” -> “API密钥管理”。
    * **创建密钥：** 如果你还没有API密钥，点击“新建密钥”生成一对 `SecretId` 和 `SecretKey`。
    * **妥善保管：** `SecretId` 和 `SecretKey` 是你程序访问COS的凭证，如同账号密码，**请务必妥善保管，切勿泄露或直接硬编码到代码中。** 后续我们将通过环境变量的方式配置。

**三、NestJS后端集成COS SDK**

现在，我们将在NestJS项目中添加文件上传功能，并将文件上传到COS。

1.  **安装腾讯云COS SDK：**
    * 在 `home-repair-backend` 项目根目录的命令行中，运行以下命令安装COS的Node.js SDK：
        ```bash
        npm install cos-nodejs-sdk-v5
        # 或者 yarn add cos-nodejs-sdk-v5
        ```
2.  **安装文件上传中间件：** NestJS通常使用 `multer` 或 `fastify-multer` 来处理文件上传。
    * 安装 `multer`：
        ```bash
        npm install multer @types/multer
        # 或者 yarn add multer @types/multer
        ```
3.  **创建COS服务（COS Service）：**
    * 在 `src` 目录下新建一个 `cos` 文件夹，并在其中创建 `cos.service.ts` 文件。
    * 这个服务将封装与COS交互的逻辑。

    ```typescript
    // src/cos/cos.service.ts

    import { Injectable, InternalServerErrorException } from '@nestjs/common';
    import * as COS from 'cos-nodejs-sdk-v5';
    import * as path from 'path';

    @Injectable()
    export class CosService {
      private cos: COS;
      private bucket: string;
      private region: string;

      constructor() {
        // 从环境变量中获取COS配置，生产环境推荐使用此方式
        this.bucket = process.env.COS_BUCKET;
        this.region = process.env.COS_REGION;
        this.cos = new COS({
          SecretId: process.env.COS_SECRET_ID,
          SecretKey: process.env.COS_SECRET_KEY,
        });

        if (!this.bucket || !this.region || !this.cos.options.SecretId || !this.cos.options.SecretKey) {
          throw new InternalServerErrorException('COS configuration is missing. Please check environment variables.');
        }
      }

      /**
       * 上传文件到COS
       * @param filename 最终存储的文件名，建议使用UUID等唯一值
       * @param fileBuffer 文件内容的Buffer
       * @param mimetype 文件MIME类型
       * @returns COS返回的文件URL
       */
      async uploadFile(filename: string, fileBuffer: Buffer, mimetype: string): Promise<string> {
        return new Promise((resolve, reject) => {
          this.cos.putObject(
            {
              Bucket: this.bucket,
              Region: this.region,
              Key: filename, // 存储在COS上的文件名
              Body: fileBuffer, // 文件内容
              ContentType: mimetype, // 文件MIME类型
              // ACL: 'public-read', // 可选：设置为公共读，如果存储桶权限不是公有读写
            },
            (err, data) => {
              if (err) {
                console.error('COS upload error:', err);
                reject(new InternalServerErrorException('Failed to upload file to COS.'));
              } else {
                // COS返回的URL格式通常是：https://[BucketName]-[APPID].cos.[Region].myqcloud.com/[Key]
                const fileUrl = `https://${this.bucket}.cos.${this.region}.myqcloud.com/${filename}`;
                resolve(fileUrl);
              }
            },
          );
        });
      }

      // TODO: 可添加删除文件等其他COS操作方法
    }
    ```
4.  **创建文件上传控制器：**
    * 在 `src/app.controller.ts` 中或创建一个新的 `upload.controller.ts` 来处理文件上传请求。
    * 使用 `@UseInterceptors(FileInterceptor('file'))` 装饰器处理单个文件上传，其中 `'file'` 是上传文件字段的名称。
    * 注入 `CosService` 来调用上传逻辑。

    ```typescript
    // src/upload/upload.controller.ts (示例)
    import { Controller, Post, UseInterceptors, UploadedFile, HttpException, HttpStatus } from '@nestjs/common';
    import { FileInterceptor } from '@nestjs/platform-express';
    import { CosService } from '../cos/cos.service';
    import { v4 as uuidv4 } from 'uuid'; // 用于生成唯一文件名
    // npm install uuid @types/uuid

    @Controller('upload') // 接口前缀 /upload
    export class UploadController {
      constructor(private readonly cosService: CosService) {}

      @Post('image') // POST /upload/image
      @UseInterceptors(FileInterceptor('file')) // 'file' 是前端上传时对应的字段名
      async uploadImage(@UploadedFile() file: Express.Multer.File) {
        if (!file) {
          throw new HttpException('No file uploaded.', HttpStatus.BAD_REQUEST);
        }

        const filename = `${uuidv4()}${path.extname(file.originalname)}`; // 生成唯一文件名
        const fileUrl = await this.cosService.uploadFile(filename, file.buffer, file.mimetype);

        // 将文件URL返回给前端，前端可将此URL存储到业务数据库（如工单图片URL）
        return {
          message: 'File uploaded successfully',
          url: fileUrl,
          filename: filename,
        };
      }
    }
    ```
5.  **配置 `app.module.ts` 和创建 `cos.module.ts`：**

    ```typescript
    // src/cos/cos.module.ts
    import { Module } from '@nestjs/common';
    import { CosService } from './cos.service';

    @Module({
      providers: [CosService],
      exports: [CosService], // 导出CosService，以便其他模块可以使用
    })
    export class CosModule {}
    ```

    ```typescript
    // src/app.module.ts (部分代码)
    import { CosModule } from './cos/cos.module'; // 导入COS模块
    import { UploadController } from './upload/upload.controller'; // 导入上传控制器

    @Module({
      imports: [
        // ... 其他模块 ...
        CosModule, // 导入COS模块
      ],
      controllers: [AppController, UploadController], // 添加上传控制器
      providers: [AppService],
    })
    export class AppModule {}
    ```
6.  **配置环境变量：** 在项目根目录创建 `.env` 文件（或使用其他配置管理方式），并添加COS的配置信息。**这些信息绝不能直接写死在代码中。**
    ```
    COS_SECRET_ID=your_cos_secret_id
    COS_SECRET_KEY=your_cos_secret_key
    COS_BUCKET=your_cos_bucket_name
    COS_REGION=your_cos_region # 例如：ap-guangzhou
    ```
    * 在NestJS中，你可以使用 `@nestjs/config` 包来方便地读取环境变量。需要额外安装：`npm install @nestjs/config`。

**四、测试文件上传功能**

1.  **启动后端应用：** 在命令行中运行 `npm run start:dev`。
2.  **使用API测试工具：**
    * 打开Postman、Apifox或Insomnia。
    * 创建一个 `POST` 请求，URL设置为 `http://localhost:3000/upload/image`。
    * 在请求Body中，选择 `form-data` 类型。
    * 添加一个Key为 `file`，类型为 `File`，然后选择你要上传的图片文件。
    * 发送请求。如果成功，你将收到一个包含文件URL的响应。你可以在浏览器中访问这个URL，验证图片是否已上传到COS并能正常显示。

通过本篇教程，你已经成功将腾讯云COS集成到你的NestJS后端，实现了附件的高效存储和管理。未来，当客户或工人上传照片时，这些文件将直接安全地存储在COS中，极大地减轻了你的服务器压力。

---

**[请告诉我“继续”，我将提供第五篇：核心业务模块构建（工单管理）。]**