好的，我完全理解并接受这个“无 Docker 本地开发版”的教程大纲。

非常棒，我们现在就来深入讲解**第七篇：财务管理与消息通知**。

---

## 第七篇：财务管理与消息通知

在本篇中，我们将为“家维在线系统”实现两个关键的业务模块：**财务管理（佣金与提现）**和**消息通知**。这两个模块对于维护平台的正常运营、保障技师的收入结算以及提升用户体验至关重要。

### 7.1 佣金与提现模块 (Finance Module)

财务管理模块将处理订单的佣金计算、技师提现申请以及管理员的审核流程。我们将设计必要的实体来记录佣金和提现信息。

#### 7.1.1 定义`CommissionRecord`和`WithdrawRecord`实体

我们将创建两个TypeORM实体来分别表示佣金记录和提现记录。

**1. `CommissionRecord` 实体**

这个实体将记录每次订单完成后的佣金分配详情。

```typescript
// src/finance/entities/commission-record.entity.ts
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Order } from '../../order/entities/order.entity';
import { User } from '../../user/entities/user.entity'; // 假设技师也是User实体的一种

@Entity('commission_records')
export class CommissionRecord {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, comment: '订单总金额' })
  orderAmount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, comment: '平台佣金比例，例如 0.15 代表 15%' })
  platformCommissionRate: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, comment: '平台实际收取佣金金额' })
  platformCommissionAmount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, comment: '技师实际收入金额' })
  technicianIncomeAmount: number;

  @ManyToOne(() => Order, order => order.commissionRecords)
  @JoinColumn({ name: 'orderId' })
  order: Order;

  @Column()
  orderId: number; // 外键

  @ManyToOne(() => User, user => user.commissionRecords) // 假设技师是User实体
  @JoinColumn({ name: 'technicianId' })
  technician: User;

  @Column()
  technicianId: number; // 外键

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 可以在这里添加其他字段，例如：
  // @Column({ type: 'varchar', length: 50, comment: '佣金状态，例如：已计算、已结算' })
  // status: string;
}
```

**2. `WithdrawRecord` 实体**

这个实体将记录技师的提现申请和处理状态。

```typescript
// src/finance/entities/withdraw-record.entity.ts
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from '../../user/entities/user.entity';

export enum WithdrawStatus {
  PENDING = 'pending',    // 待审核
  APPROVED = 'approved',  // 已通过
  REJECTED = 'rejected',  // 已驳回
  PROCESSING = 'processing', // 处理中（银行转账等）
  COMPLETED = 'completed',  // 已完成
}

@Entity('withdraw_records')
export class WithdrawRecord {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => User, user => user.withdrawRecords)
  @JoinColumn({ name: 'technicianId' })
  technician: User;

  @Column()
  technicianId: number; // 外键

  @Column({ type: 'decimal', precision: 10, scale: 2, comment: '提现金额' })
  amount: number;

  @Column({ type: 'varchar', length: 50, default: WithdrawStatus.PENDING, comment: '提现状态' })
  status: WithdrawStatus;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '审核意见或驳回原因' })
  remark: string;

  @CreateDateColumn()
  applyAt: Date; // 申请时间

  @UpdateDateColumn()
  processedAt: Date; // 处理时间（审核通过或驳回时间）

  // 可以在这里添加技师的收款账户信息，例如：
  // @Column({ type: 'varchar', length: 100, nullable: true, comment: '收款银行' })
  // bankName: string;
  // @Column({ type: 'varchar', length: 100, nullable: true, comment: '收款账户名' })
  // accountName: string;
  // @Column({ type: 'varchar', length: 100, nullable: true, comment: '收款银行卡号' })
  // accountNumber: string;
}
```

**不要忘记在`User`实体中添加与这些记录的关联：**

```typescript
// src/user/entities/user.entity.ts (部分代码)
import { OneToMany } from 'typeorm';
import { CommissionRecord } from '../../finance/entities/commission-record.entity';
import { WithdrawRecord } from '../../finance/entities/withdraw-record.entity';

@Entity('users')
export class User {
  // ... 其他字段

  @OneToMany(() => CommissionRecord, commissionRecord => commissionRecord.technician)
  commissionRecords: CommissionRecord[];

  @OneToMany(() => WithdrawRecord, withdrawRecord => withdrawRecord.technician)
  withdrawRecords: WithdrawRecord[];

  // ... 其他字段
}
```

#### 7.1.2 自动计算订单分佣逻辑

佣金的计算通常发生在订单状态变为“已完成”或“已支付”之后。我们可以在订单完成的逻辑中触发佣金记录的创建。

**核心逻辑：**

* 在订单模块（`OrderService`）中，当订单状态更新为**`completed`**（或由技师确认收款）时，调用财务模块的服务来计算并记录佣金。
* 需要一个**平台佣金比例**的配置，这可以存储在数据库中，或者作为环境变量。

```typescript
// src/order/order.service.ts (部分代码，示例)
import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Order, OrderStatus } from './entities/order.entity';
import { FinanceService } from '../finance/finance.service'; // 引入财务服务

@Injectable()
export class OrderService {
  constructor(
    @InjectRepository(Order)
    private orderRepository: Repository<Order>,
    private financeService: FinanceService, // 注入财务服务
  ) {}

  async completeOrder(orderId: number, technicianId: number): Promise<Order> {
    const order = await this.orderRepository.findOne({
      where: { id: orderId, technician: { id: technicianId } },
      relations: ['technician'] // 确保加载技师信息
    });

    if (!order) {
      throw new BadRequestException('订单不存在或无权操作');
    }
    if (order.status !== OrderStatus.SERVICING && order.status !== OrderStatus.PENDING_PAYMENT) {
      throw new BadRequestException('订单状态不允许完成');
    }

    order.status = OrderStatus.COMPLETED;
    order.completedAt = new Date();
    await this.orderRepository.save(order);

    // 调用财务服务计算并记录佣金
    await this.financeService.calculateAndRecordCommission(order);

    return order;
  }

  // ... 其他订单相关方法
}
```

**`FinanceService`中的计算逻辑：**

```typescript
// src/finance/finance.service.ts
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Order } from '../order/entities/order.entity';
import { CommissionRecord } from './entities/commission-record.entity';
import { WithdrawRecord, WithdrawStatus } from './entities/withdraw-record.entity';
import { User } from '../user/entities/user.entity';

@Injectable()
export class FinanceService {
  // 假设平台佣金比例
  private readonly PLATFORM_COMMISSION_RATE = 0.15; // 15%

  constructor(
    @InjectRepository(CommissionRecord)
    private commissionRecordRepository: Repository<CommissionRecord>,
    @InjectRepository(WithdrawRecord)
    private withdrawRecordRepository: Repository<WithdrawRecord>,
    @InjectRepository(User) // 需要访问技师余额
    private userRepository: Repository<User>,
  ) {}

  /**
   * 计算并记录订单佣金
   * @param order 订单实体
   */
  async calculateAndRecordCommission(order: Order): Promise<CommissionRecord> {
    const platformCommissionAmount = order.totalAmount * this.PLATFORM_COMMISSION_RATE;
    const technicianIncomeAmount = order.totalAmount - platformCommissionAmount;

    const commissionRecord = this.commissionRecordRepository.create({
      orderAmount: order.totalAmount,
      platformCommissionRate: this.PLATFORM_COMMISSION_RATE,
      platformCommissionAmount,
      technicianIncomeAmount,
      order: order,
      technician: order.technician, // 确保order关联了technician
    });

    await this.commissionRecordRepository.save(commissionRecord);

    // 更新技师账户余额（假设User实体中有balance字段）
    // 注意：实际项目中，余额更新应该通过事务确保数据一致性
    const technician = await this.userRepository.findOne({ where: { id: order.technician.id } });
    if (technician) {
        // 假设 User 实体有一个 balance 字段
        // 注意：这里只是一个简化的例子，实际业务中应该考虑并发和事务
        technician.balance = (technician.balance || 0) + technicianIncomeAmount;
        await this.userRepository.save(technician);
    }

    return commissionRecord;
  }

  // ... 后续提现相关方法
}
```

#### 7.1.3 技师端：获取收入记录、申请提现、获取提现记录API

**1. 获取收入记录 (GET /api/finance/technician/commissions)**

```typescript
// src/finance/finance.controller.ts (部分代码)
import { Controller, Get, Post, Body, Param, Req, UseGuards, Query } from '@nestjs/common';
import { FinanceService } from './finance.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard'; // 假设有JWT守卫
import { RolesGuard } from '../auth/guards/roles.guard'; // 假设有角色守卫
import { Roles } from '../auth/decorators/roles.decorator'; // 假设有Roles装饰器
import { UserRole } from '../user/entities/user.entity'; // 假设用户角色枚举

@Controller('finance')
@UseGuards(JwtAuthGuard, RolesGuard)
export class FinanceController {
  constructor(private readonly financeService: FinanceService) {}

  @Get('technician/commissions')
  @Roles(UserRole.TECHNICIAN) // 只有技师能访问
  async getTechnicianCommissions(@Req() req, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    const technicianId = req.user.id; // 从JWT Payload中获取技师ID
    const [commissions, total] = await this.financeService.getTechnicianCommissions(technicianId, page, limit);
    return { data: commissions, total, page, limit };
  }

  // ... 其他API
}
```

```typescript
// src/finance/finance.service.ts (添加方法)
// ...
export class FinanceService {
  // ...
  async getTechnicianCommissions(technicianId: number, page: number, limit: number): Promise<[CommissionRecord[], number]> {
    const skip = (page - 1) * limit;
    return this.commissionRecordRepository.findAndCount({
      where: { technician: { id: technicianId } },
      relations: ['order'], // 关联订单信息
      order: { createdAt: 'DESC' },
      skip,
      take: limit,
    });
  }

  // ...
}
```

**2. 申请提现 (POST /api/finance/technician/withdraw)**

需要一个DTO来接收提现金额。

```typescript
// src/finance/dto/create-withdraw.dto.ts
import { IsDecimal, IsNumber, Min } from 'class-validator';

export class CreateWithdrawDto {
  @IsNumber()
  @IsDecimal({ decimal_digits: '0,2' }) // 最多两位小数
  @Min(0.01) // 最小提现金额
  amount: number;
}
```

```typescript
// src/finance/finance.controller.ts (添加方法)
// ...
@Controller('finance')
@UseGuards(JwtAuthGuard, RolesGuard)
export class FinanceController {
  // ...
  @Post('technician/withdraw')
  @Roles(UserRole.TECHNICIAN)
  async requestWithdraw(@Req() req, @Body() createWithdrawDto: CreateWithdrawDto) {
    const technicianId = req.user.id;
    const withdrawRecord = await this.financeService.requestWithdraw(technicianId, createWithdrawDto.amount);
    return { message: '提现申请已提交，等待审核。', data: withdrawRecord };
  }

  // ...
}
```

```typescript
// src/finance/finance.service.ts (添加方法)
// ...
export class FinanceService {
  // ...
  async requestWithdraw(technicianId: number, amount: number): Promise<WithdrawRecord> {
    // 1. 检查技师余额是否足够
    const technician = await this.userRepository.findOne({ where: { id: technicianId } });
    if (!technician || technician.balance < amount) {
      throw new BadRequestException('余额不足或用户不存在');
    }

    // 2. 检查是否有待处理的提现申请（可选，避免重复申请）
    const pendingWithdrawals = await this.withdrawRecordRepository.count({
      where: { technician: { id: technicianId }, status: WithdrawStatus.PENDING },
    });
    if (pendingWithdrawals > 0) {
      throw new BadRequestException('您有待处理的提现申请，请勿重复提交。');
    }

    // 3. 扣除技师余额（预扣）
    // 注意：实际业务中，这部分余额可能需要冻结，而不是直接扣除
    // 这里我们简单实现为直接扣除
    technician.balance -= amount;
    await this.userRepository.save(technician);

    // 4. 创建提现记录
    const withdrawRecord = this.withdrawRecordRepository.create({
      technician: technician,
      amount: amount,
      status: WithdrawStatus.PENDING,
    });
    return this.withdrawRecordRepository.save(withdrawRecord);
  }

  // ...
}
```

**3. 获取提现记录 (GET /api/finance/technician/withdrawals)**

```typescript
// src/finance/finance.controller.ts (添加方法)
// ...
@Controller('finance')
@UseGuards(JwtAuthGuard, RolesGuard)
export class FinanceController {
  // ...
  @Get('technician/withdrawals')
  @Roles(UserRole.TECHNICIAN)
  async getTechnicianWithdrawals(@Req() req, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    const technicianId = req.user.id;
    const [withdrawals, total] = await this.financeService.getTechnicianWithdrawals(technicianId, page, limit);
    return { data: withdrawals, total, page, limit };
  }
  // ...
}
```

```typescript
// src/finance/finance.service.ts (添加方法)
// ...
export class FinanceService {
  // ...
  async getTechnicianWithdrawals(technicianId: number, page: number, limit: number): Promise<[WithdrawRecord[], number]> {
    const skip = (page - 1) * limit;
    return this.withdrawRecordRepository.findAndCount({
      where: { technician: { id: technicianId } },
      order: { applyAt: 'DESC' },
      skip,
      take: limit,
    });
  }
  // ...
}
```

#### 7.1.4 管理员端：佣金记录查询、提现申请审核、财务报表API

**1. 佣金记录查询 (GET /api/finance/admin/commissions)**

```typescript
// src/finance/finance.controller.ts (添加方法)
// ...
@Controller('finance')
@UseGuards(JwtAuthGuard, RolesGuard)
export class FinanceController {
  // ...
  @Get('admin/commissions')
  @Roles(UserRole.ADMIN) // 只有管理员能访问
  async getAllCommissions(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('technicianId') technicianId?: number,
    @Query('orderId') orderId?: number
  ) {
    const [commissions, total] = await this.financeService.getAllCommissions(page, limit, technicianId, orderId);
    return { data: commissions, total, page, limit };
  }

  // ...
}
```

```typescript
// src/finance/finance.service.ts (添加方法)
// ...
import { FindManyOptions } from 'typeorm';
// ...
export class FinanceService {
  // ...
  async getAllCommissions(page: number, limit: number, technicianId?: number, orderId?: number): Promise<[CommissionRecord[], number]> {
    const skip = (page - 1) * limit;
    const where: FindManyOptions<CommissionRecord>['where'] = {};

    if (technicianId) {
      where['technician'] = { id: technicianId };
    }
    if (orderId) {
      where['order'] = { id: orderId };
    }

    return this.commissionRecordRepository.findAndCount({
      where,
      relations: ['technician', 'order'], // 关联技师和订单信息
      order: { createdAt: 'DESC' },
      skip,
      take: limit,
    });
  }

  // ...
}
```

**2. 提现申请审核 (PATCH /api/finance/admin/withdrawals/:id/approve, PATCH /api/finance/admin/withdrawals/:id/reject)**

需要一个DTO来接收审核意见。

```typescript
// src/finance/dto/update-withdraw-status.dto.ts
import { IsOptional, IsString, MaxLength } from 'class-validator';

export class UpdateWithdrawStatusDto {
  @IsOptional()
  @IsString()
  @MaxLength(255)
  remark?: string;
}
```

```typescript
// src/finance/finance.controller.ts (添加方法)
// ...
@Controller('finance')
@UseGuards(JwtAuthGuard, RolesGuard)
export class FinanceController {
  // ...
  @Get('admin/withdrawals') // 获取所有提现申请列表
  @Roles(UserRole.ADMIN)
  async getAllWithdrawals(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('status') status?: WithdrawStatus,
    @Query('technicianId') technicianId?: number
  ) {
    const [withdrawals, total] = await this.financeService.getAllWithdrawals(page, limit, status, technicianId);
    return { data: withdrawals, total, page, limit };
  }

  @Patch('admin/withdrawals/:id/approve')
  @Roles(UserRole.ADMIN)
  async approveWithdrawal(@Param('id') id: number, @Body() updateDto: UpdateWithdrawStatusDto) {
    const withdrawRecord = await this.financeService.updateWithdrawalStatus(id, WithdrawStatus.APPROVED, updateDto.remark);
    return { message: '提现申请已批准。', data: withdrawRecord };
  }

  @Patch('admin/withdrawals/:id/reject')
  @Roles(UserRole.ADMIN)
  async rejectWithdrawal(@Param('id') id: number, @Body() updateDto: UpdateWithdrawStatusDto) {
    const withdrawRecord = await this.financeService.updateWithdrawalStatus(id, WithdrawStatus.REJECTED, updateDto.remark);
    return { message: '提现申请已驳回。', data: withdrawRecord };
  }

  // ...
}
```

```typescript
// src/finance/finance.service.ts (添加方法)
// ...
export class FinanceService {
  // ...
  async getAllWithdrawals(page: number, limit: number, status?: WithdrawStatus, technicianId?: number): Promise<[WithdrawRecord[], number]> {
    const skip = (page - 1) * limit;
    const where: FindManyOptions<WithdrawRecord>['where'] = {};

    if (status) {
      where['status'] = status;
    }
    if (technicianId) {
      where['technician'] = { id: technicianId };
    }

    return this.withdrawRecordRepository.findAndCount({
      where,
      relations: ['technician'],
      order: { applyAt: 'DESC' },
      skip,
      take: limit,
    });
  }

  async updateWithdrawalStatus(id: number, newStatus: WithdrawStatus, remark?: string): Promise<WithdrawRecord> {
    const withdrawRecord = await this.withdrawRecordRepository.findOne({ where: { id } });
    if (!withdrawRecord) {
      throw new BadRequestException('提现记录不存在');
    }

    // 只有待审核状态的提现才能进行审核操作
    if (withdrawRecord.status !== WithdrawStatus.PENDING) {
      throw new BadRequestException('该提现申请已处理，无法再次操作。');
    }

    withdrawRecord.status = newStatus;
    withdrawRecord.remark = remark;
    withdrawRecord.processedAt = new Date();

    // 如果驳回，需要将预扣的金额返还给技师
    if (newStatus === WithdrawStatus.REJECTED) {
        const technician = await this.userRepository.findOne({ where: { id: withdrawRecord.technicianId } });
        if (technician) {
            technician.balance = (technician.balance || 0) + withdrawRecord.amount;
            await this.userRepository.save(technician);
        }
    }
    // 如果批准，状态变为 APPROVED，实际转账由人工或外部系统完成，待完成后再更新为 COMPLETED

    return this.withdrawRecordRepository.save(withdrawRecord);
  }

  // ...
}
```

**3. 财务报表API (GET /api/finance/admin/report)**

财务报表通常涉及复杂的数据聚合和统计，这里只提供一个简单的示例，例如统计某段时间内的总收入、总支出（提现）。

```typescript
// src/finance/finance.controller.ts (添加方法)
// ...
@Controller('finance')
@UseGuards(JwtAuthGuard, RolesGuard)
export class FinanceController {
  // ...
  @Get('admin/report')
  @Roles(UserRole.ADMIN)
  async getFinanceReport(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    const report = await this.financeService.getFinanceReport(startDate, endDate);
    return report;
  }
}
```

```typescript
// src/finance/finance.service.ts (添加方法)
// ...
import { Between } from 'typeorm';
// ...
export class FinanceService {
  // ...
  async getFinanceReport(startDateStr?: string, endDateStr?: string) {
    let startDate: Date;
    let endDate: Date;

    if (startDateStr) {
      startDate = new Date(startDateStr);
      if (isNaN(startDate.getTime())) throw new BadRequestException('无效的开始日期');
    }
    if (endDateStr) {
      endDate = new Date(endDateStr);
      if (isNaN(endDate.getTime())) throw new BadRequestException('无效的结束日期');
    }

    const commissionWhere: any = {};
    if (startDate && endDate) {
      commissionWhere.createdAt = Between(startDate, endDate);
    } else if (startDate) {
      commissionWhere.createdAt = MoreThanOrEqual(startDate);
    } else if (endDate) {
      commissionWhere.createdAt = LessThanOrEqual(endDate);
    }

    const withdrawWhere: any = { status: WithdrawStatus.COMPLETED }; // 通常只统计已完成的提现
    if (startDate && endDate) {
      withdrawWhere.processedAt = Between(startDate, endDate);
    } else if (startDate) {
      withdrawWhere.processedAt = MoreThanOrEqual(startDate);
    } else if (endDate) {
      withdrawWhere.processedAt = LessThanOrEqual(endDate);
    }

    const totalPlatformCommission = await this.commissionRecordRepository.sum('platformCommissionAmount', commissionWhere);
    const totalTechnicianIncome = await this.commissionRecordRepository.sum('technicianIncomeAmount', commissionWhere);
    const totalWithdrawals = await this.withdrawRecordRepository.sum('amount', withdrawWhere);

    return {
      totalPlatformCommission: totalPlatformCommission || 0, // 平台总收入
      totalTechnicianIncome: totalTechnicianIncome || 0,   // 技师总收入（流水）
      totalWithdrawals: totalWithdrawals || 0,             // 技师总提现
      netProfit: (totalPlatformCommission || 0) - (totalWithdrawals || 0), // 平台净利润（简化计算，实际可能更复杂）
    };
  }
  // ...
}
```

---

### 7.2 消息通知模块 (Notification Module)

消息通知模块是提升用户体验、及时传递信息的重要组成部分。我们将实现站内信功能，并讨论如何初步集成第三方短信/邮件通知服务。

#### 7.2.1 设计消息类型和`Notification`实体

我们将定义一个`Notification`实体来存储站内信。消息类型可以通过枚举或配置来管理。

```typescript
// src/notification/entities/notification.entity.ts
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from '../../user/entities/user.entity';

export enum NotificationType {
  ORDER_STATUS_UPDATE = '订单状态更新',
  NEW_ORDER = '新订单通知',
  WITHDRAW_STATUS = '提现状态更新',
  SYSTEM_ANNOUNCEMENT = '系统公告',
  SERVICE_REMINDER = '服务提醒',
  // ... 更多消息类型
}

@Entity('notifications')
export class Notification {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 100, comment: '消息标题' })
  title: string;

  @Column({ type: 'text', comment: '消息内容' })
  content: string;

  @Column({ type: 'varchar', length: 50, default: NotificationType.SYSTEM_ANNOUNCEMENT, comment: '消息类型' })
  type: NotificationType;

  @ManyToOne(() => User, user => user.sentNotifications, { nullable: true }) // 发送者，系统消息可为空
  @JoinColumn({ name: 'senderId' })
  sender: User;

  @Column({ nullable: true })
  senderId: number;

  @ManyToOne(() => User, user => user.receivedNotifications) // 接收者
  @JoinColumn({ name: 'receiverId' })
  receiver: User;

  @Column()
  receiverId: number;

  @Column({ default: false, comment: '是否已读' })
  isRead: boolean;

  @Column({ type: 'json', nullable: true, comment: '附加数据（例如订单ID、提现ID等）' })
  payload: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

**在`User`实体中添加与通知的关联：**

```typescript
// src/user/entities/user.entity.ts (部分代码)
import { OneToMany } from 'typeorm';
import { Notification } from '../../notification/entities/notification.entity';

@Entity('users')
export class User {
  // ... 其他字段

  @OneToMany(() => Notification, notification => notification.sender)
  sentNotifications: Notification[];

  @OneToMany(() => Notification, notification => notification.receiver)
  receivedNotifications: Notification[];

  // ... 其他字段
}
```

#### 7.2.2 实现站内信功能

**1. 创建通知API (POST /api/notifications)** - 通常由后端服务内部调用，或管理员发送系统公告

```typescript
// src/notification/dto/create-notification.dto.ts
import { IsString, IsNotEmpty, IsEnum, IsOptional, IsNumber } from 'class-validator';
import { NotificationType } from '../entities/notification.entity';

export class CreateNotificationDto {
  @IsString()
  @IsNotEmpty()
  title: string;

  @IsString()
  @IsNotEmpty()
  content: string;

  @IsEnum(NotificationType)
  @IsOptional()
  type?: NotificationType;

  @IsNumber()
  @IsNotEmpty()
  receiverId: number; // 接收者ID

  @IsNumber()
  @IsOptional()
  senderId?: number; // 发送者ID，系统发送时可为空

  @IsOptional()
  payload?: any; // 附加数据
}
```

```typescript
// src/notification/notification.service.ts
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Notification, NotificationType } from './entities/notification.entity';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { User } from '../user/entities/user.entity';

@Injectable()
export class NotificationService {
  constructor(
    @InjectRepository(Notification)
    private notificationRepository: Repository<Notification>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  /**
   * 创建并发送一条站内信通知
   * @param createNotificationDto 通知内容
   */
  async createNotification(createNotificationDto: CreateNotificationDto): Promise<Notification> {
    const receiver = await this.userRepository.findOne({ where: { id: createNotificationDto.receiverId } });
    if (!receiver) {
      throw new Error('接收者不存在'); // 实际中抛出 BadRequestException
    }

    let sender = null;
    if (createNotificationDto.senderId) {
      sender = await this.userRepository.findOne({ where: { id: createNotificationDto.senderId } });
      if (!sender) {
        throw new Error('发送者不存在'); // 实际中抛出 BadRequestException
      }
    }

    const notification = this.notificationRepository.create({
      ...createNotificationDto,
      receiver,
      sender,
    });
    return this.notificationRepository.save(notification);
  }

  // ... 其他方法
}
```

**2. 获取用户通知列表 (GET /api/notifications)**

```typescript
// src/notification/notification.controller.ts
import { Controller, Get, Post, Body, Patch, Param, Req, UseGuards, Query } from '@nestjs/common';
import { NotificationService } from './notification.service';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard'; // 假设JWT守卫

@Controller('notifications')
@UseGuards(JwtAuthGuard)
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  // 后端内部调用或管理员发送
  @Post()
  // @Roles(UserRole.ADMIN) // 只有管理员或内部服务能调用
  // @UseGuards(RolesGuard)
  async create(@Body() createNotificationDto: CreateNotificationDto) {
    return this.notificationService.createNotification(createNotificationDto);
  }

  @Get()
  async getMyNotifications(@Req() req, @Query('page') page: number = 1, @Query('limit') limit: number = 10, @Query('isRead') isRead?: boolean) {
    const userId = req.user.id;
    const [notifications, total] = await this.notificationService.getNotificationsForUser(userId, page, limit, isRead);
    return { data: notifications, total, page, limit };
  }

  // ... 其他方法
}
```

```typescript
// src/notification/notification.service.ts (添加方法)
// ...
import { IsBoolean } from 'class-validator';
import { FindManyOptions } from 'typeorm';

export class NotificationService {
  // ...
  async getNotificationsForUser(userId: number, page: number, limit: number, isRead?: boolean): Promise<[Notification[], number]> {
    const skip = (page - 1) * limit;
    const where: FindManyOptions<Notification>['where'] = { receiver: { id: userId } };

    if (isRead !== undefined) {
      where['isRead'] = isRead;
    }

    return this.notificationRepository.findAndCount({
      where,
      order: { createdAt: 'DESC' },
      skip,
      take: limit,
      relations: ['sender'] // 如果需要显示发送者信息
    });
  }

  // ...
}
```

**3. 标记通知为已读 (PATCH /api/notifications/:id/read)**

```typescript
// src/notification/notification.controller.ts (添加方法)
// ...
@Controller('notifications')
@UseGuards(JwtAuthGuard)
export class NotificationController {
  // ...
  @Patch(':id/read')
  async markAsRead(@Param('id') id: number, @Req() req) {
    const userId = req.user.id;
    const notification = await this.notificationService.markNotificationAsRead(id, userId);
    return { message: '通知已标记为已读', data: notification };
  }
  // ...
}
```

```typescript
// src/notification/notification.service.ts (添加方法)
// ...
export class NotificationService {
  // ...
  async markNotificationAsRead(notificationId: number, userId: number): Promise<Notification> {
    const notification = await this.notificationRepository.findOne({
      where: { id: notificationId, receiver: { id: userId } },
    });

    if (!notification) {
      throw new BadRequestException('通知不存在或无权操作');
    }

    if (notification.isRead) {
      return notification; // 已经已读，直接返回
    }

    notification.isRead = true;
    return this.notificationRepository.save(notification);
  }

  async markAllAsRead(userId: number): Promise<void> {
    await this.notificationRepository.update({ receiver: { id: userId }, isRead: false }, { isRead: true });
  }

  // 获取未读通知数量
  async getUnreadCount(userId: number): Promise<number> {
    return this.notificationRepository.count({ where: { receiver: { id: userId }, isRead: false } });
  }
}
```

#### 7.2.3 （可选）初步集成第三方短信/邮件通知服务

虽然站内信是基础，但对于关键事件（如新订单、订单状态变更、提现成功等），短信或邮件通知能提供更及时的触达。

**实现思路：**

1.  **选择第三方服务：**
    * **短信服务：** 国内通常选择阿里云短信、腾讯云短信、或行业短信服务商。
    * **邮件服务：** SendGrid, Mailgun, Postmark, AWS SES 或国内的腾讯企业邮箱、网易企业邮箱等。
2.  **安装SDK：** 根据选择的服务，安装其Node.js SDK。
3.  **配置凭证：** 将API Key、Secret等敏感信息配置在**环境变量**或NestJS的**ConfigModule**中。
4.  **创建Service：** 创建一个独立的通知服务（例如`ThirdPartyNotificationService`），封装短信和邮件发送逻辑。

**示例（以一个抽象的`SmsService`为例）：**

```typescript
// src/common/services/sms.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config'; // 假设使用了ConfigModule

@Injectable()
export class SmsService {
  private readonly logger = new Logger(SmsService.name);
  // 实际中这里会是第三方短信SDK的实例
  private smsClient: any;

  constructor(private configService: ConfigService) {
    // 初始化短信SDK，例如阿里云SMS的Client
    // this.smsClient = new ALY.SMS({
    //   accessKeyId: this.configService.get('SMS_ACCESS_KEY_ID'),
    //   secretAccessKey: this.configService.get('SMS_SECRET_ACCESS_KEY'),
    //   endpoint: '...',
    //   apiVersion: '...',
    // });
    this.logger.log('SMS Service initialized.');
  }

  /**
   * 发送短信
   * @param phoneNumber 接收手机号
   * @param templateCode 短信模板ID
   * @param templateParams 模板参数对象
   */
  async sendSms(phoneNumber: string, templateCode: string, templateParams: Record<string, string>): Promise<void> {
    try {
      // 实际调用第三方SDK的发送方法
      // const response = await this.smsClient.send({
      //   PhoneNumbers: phoneNumber,
      //   SignName: this.configService.get('SMS_SIGN_NAME'),
      //   TemplateCode: templateCode,
      //   TemplateParam: JSON.stringify(templateParams),
      // });
      this.logger.log(`短信发送成功：To ${phoneNumber}, Template: ${templateCode}, Params: ${JSON.stringify(templateParams)}`);
      // console.log('SMS response:', response); // 打印第三方服务返回
    } catch (error) {
      this.logger.error(`短信发送失败：To ${phoneNumber}, Error: ${error.message}`, error.stack);
      // 实际项目中可能需要更细致的错误处理，例如重试机制、告警
    }
  }
}
```

**在事件发生时触发短信/邮件：**

例如，在订单状态更新为`COMPLETED`时，除了记录佣金，还可以发送短信通知用户订单已完成。

```typescript
// src/order/order.service.ts (部分代码)
import { SmsService } from '../common/services/sms.service'; // 引入短信服务

@Injectable()
export class OrderService {
  constructor(
    // ...
    private financeService: FinanceService,
    private smsService: SmsService, // 注入短信服务
    private notificationService: NotificationService, // 注入站内信服务
  ) {}

  async completeOrder(orderId: number, technicianId: number): Promise<Order> {
    // ... 订单完成逻辑

    await this.orderRepository.save(order);

    // 1. 调用财务服务计算并记录佣金
    await this.financeService.calculateAndRecordCommission(order);

    // 2. 发送站内信通知用户订单已完成
    await this.notificationService.createNotification({
      title: '订单已完成',
      content: `您的订单 #${order.id} 已由技师 ${order.technician.name} 完成，请您进行评价。`,
      type: NotificationType.ORDER_STATUS_UPDATE,
      receiverId: order.user.id, // 订单用户ID
      senderId: null, // 系统发送
      payload: { orderId: order.id }
    });

    // 3. （可选）发送短信通知用户订单已完成
    if (order.user.phoneNumber) {
        await this.smsService.sendSms(
            order.user.phoneNumber,
            'SMS_TEMPLATE_ORDER_COMPLETED', // 具体的短信模板ID
            { orderId: order.id.toString(), technicianName: order.technician.name }
        );
    }

    return order;
  }
}
```

---

**总结：**

本篇我们深入构建了**佣金与提现模块**，包括：
* **实体设计：** `CommissionRecord` 用于记录平台佣金和技师收入，`WithdrawRecord` 用于管理提现申请。
* **核心逻辑：** 订单完成后自动计算佣金，并更新技师余额。
* **API实现：** 为技师提供了查询收入、申请提现、查询提现记录的接口；为管理员提供了查询佣金记录、审核提现申请和查看财务报表的接口。

同时，我们还搭建了**消息通知模块**：
* **实体设计：** `Notification` 用于存储站内信。
* **核心功能：** 实现站内信的创建、用户消息列表获取和标记为已读功能。
* **扩展讨论：** 探讨了如何集成第三方短信/邮件通知服务，以及在业务流程中触发这些通知的场景。

至此，我们的“家维在线系统”在财务结算和用户沟通方面又迈进了一大步。

---

您对本篇的内容有什么疑问吗？或者我们现在可以开始**第八篇：高级特性与未来规划**了？