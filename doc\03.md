好的，我们继续第三篇。我们将聚焦于后台应用最基础也是最重要的功能之一：用户管理。

---

### **《从零构建家政维修平台：打造稳健高效的混合架构后台应用》**

#### **第三篇：核心业务模块构建——用户管理（认证、角色与基本信息）**

**摘要：** 任何管理后台的核心都是用户管理。本篇教程将指导你如何在NestJS后端构建用户模块，实现用户注册、登录认证、以及基本的角色管理功能。这将是管理客服、调度员和维修工人的基础，为后续的权限控制和业务流程奠定基础。

---

**一、设计用户数据模型（Entity）**

在数据库中存储用户数据，首先需要定义用户的数据结构。

1.  **创建用户实体（User Entity）：**
    * 在 `src` 目录下新建一个 `user` 文件夹，并在其中创建 `user.entity.ts` 文件。
    * 用户实体将包含用户的基本信息和认证相关字段。我们还需要考虑不同类型的用户（如管理员、客服、调度、维修工人）以及他们的状态。
    * **主要字段：**
        * `id`: 唯一标识符。
        * `username`: 用户名（用于登录）。
        * `password`: 密码（需要加密存储）。
        * `email` / `phone`: 联系方式。
        * `role`: 用户角色（例如 `admin`, `customer_service`, `dispatcher`, `worker`）。
        * `status`: 用户状态（例如 `active`, `inactive`, `pending`）。
        * `createdAt`, `updatedAt`: 创建和更新时间戳。
    * **TypeORM映射：** 使用 `@Entity()`、`@PrimaryGeneratedColumn()`、`@Column()` 等TypeORM装饰器将类属性映射到数据库表字段。

    ```typescript
    // src/user/user.entity.ts

    import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

    export enum UserRole {
      Admin = 'admin',
      CustomerService = 'customer_service',
      Dispatcher = 'dispatcher',
      Worker = 'worker',
      Customer = 'customer', // 客户可能也在后台管理，或者通过小程序注册
    }

    export enum UserStatus {
      Active = 'active',
      Inactive = 'inactive',
      Pending = 'pending',
    }

    @Entity('users') // 数据库表名为 'users'
    export class User {
      @PrimaryGeneratedColumn()
      id: number;

      @Column({ unique: true })
      username: string; // 登录用户名

      @Column()
      password: string; // 加密后的密码

      @Column({ unique: true, nullable: true })
      email: string;

      @Column({ unique: true, nullable: true })
      phone: string;

      @Column({
        type: 'enum',
        enum: UserRole,
        default: UserRole.Worker, // 默认角色
      })
      role: UserRole;

      @Column({
        type: 'enum',
        enum: UserStatus,
        default: UserStatus.Active,
      })
      status: UserStatus;

      @CreateDateColumn({ name: 'created_at' })
      createdAt: Date;

      @UpdateDateColumn({ name: 'updated_at' })
      updatedAt: Date;

      // TODO: 如果需要与企业微信关联，可以在这里添加一个企微User ID字段
      // @Column({ unique: true, nullable: true, name: 'wx_work_userid' })
      // wxWorkUserId: string;
    }
    ```
2.  **更新 `app.module.ts`：** 将新创建的实体添加到 `TypeOrmModule.forRoot()` 的 `entities` 数组中，以便TypeORM识别并创建/更新数据库表。

    ```typescript
    // src/app.module.ts (部分代码)

    import { User } from './user/user.entity'; // 导入用户实体

    @Module({
      imports: [
        TypeOrmModule.forRoot({
          // ... 其他配置 ...
          entities: [User], // 添加 User 实体
          synchronize: true, // 确保开发环境开启，会自动创建users表
        }),
      ],
      // ...
    })
    export class AppModule {}
    ```

**二、构建用户模块（User Module）**

NestJS推荐使用模块来组织代码。

1.  **创建用户模块：** 在命令行中运行：
    ```bash
    nest g module user
    ```
    这会在 `src/user/` 目录下创建 `user.module.ts`。

2.  **创建用户服务（User Service）：**
    * 服务是处理业务逻辑的核心。它将负责用户数据的增删改查、密码加密等。
    * 在命令行中运行：
        ```bash
        nest g service user
        ```
    * 在 `src/user/user.service.ts` 中，使用 `@InjectRepository()` 注入 `UserRepository` 来操作数据库。

    ```typescript
    // src/user/user.service.ts (部分代码)

    import { Injectable } from '@nestjs/common';
    import { InjectRepository } from '@nestjs/typeorm';
    import { Repository } from 'typeorm';
    import { User } from './user.entity';
    import * as bcrypt from 'bcrypt'; // 用于密码加密

    @Injectable()
    export class UserService {
      constructor(
        @InjectRepository(User)
        private usersRepository: Repository<User>,
      ) {}

      async create(userData: Partial<User>): Promise<User> {
        // 密码加密
        const hashedPassword = await bcrypt.hash(userData.password, 10); // 10是盐值轮数
        const newUser = this.usersRepository.create({ ...userData, password: hashedPassword });
        return this.usersRepository.save(newUser);
      }

      async findByUsername(username: string): Promise<User | undefined> {
        return this.usersRepository.findOne({ where: { username } });
      }

      async findById(id: number): Promise<User | undefined> {
        return this.usersRepository.findOne({ where: { id } });
      }

      // TODO: 添加更多方法如更新用户、删除用户、列出用户等
    }
    ```
3.  **创建用户控制器（User Controller）：**
    * 控制器负责接收HTTP请求并调用服务层的逻辑。
    * 在命令行中运行：
        ```bash
        nest g controller user
        ```
    * 在 `src/user/user.controller.ts` 中，添加API路由，例如用于用户注册和获取用户列表。

    ```typescript
    // src/user/user.controller.ts (部分代码)

    import { Controller, Post, Body, Get, Param } from '@nestjs/common';
    import { UserService } from './user.service';
    import { User } from './user.entity';

    @Controller('users') // 接口前缀是 /users
    export class UserController {
      constructor(private readonly userService: UserService) {}

      @Post('register') // POST /users/register
      async register(@Body() userData: Partial<User>): Promise<User> {
        return this.userService.create(userData);
      }

      @Get(':username') // GET /users/:username
      async getUserByUsername(@Param('username') username: string): Promise<User> {
        return this.userService.findByUsername(username);
      }

      // TODO: 添加更多接口，如获取所有用户、更新用户、删除用户等
    }
    ```
4.  **在 `user.module.ts` 中注册：** 确保模块、控制器和服务都正确注册到用户模块中。

    ```typescript
    // src/user/user.module.ts

    import { Module } from '@nestjs/common';
    import { UserService } from './user.service';
    import { UserController } from './user.controller';
    import { TypeOrmModule } from '@nestjs/typeorm';
    import { User } from './user.entity';

    @Module({
      imports: [TypeOrmModule.forFeature([User])], // 注册用户实体到当前模块
      providers: [UserService],
      controllers: [UserController],
      exports: [UserService], // 如果其他模块需要使用UserService，需要导出
    })
    export class UserModule {}
    ```
5.  **在 `app.module.ts` 中导入用户模块：**

    ```typescript
    // src/app.module.ts (部分代码)

    import { UserModule } from './user/user.module'; // 导入用户模块

    @Module({
      imports: [
        TypeOrmModule.forRoot({
          // ...
        }),
        UserModule, // 导入用户模块
      ],
      // ...
    })
    export class AppModule {}
    ```
6.  **安装密码加密库：** 我们使用了 `bcrypt` 进行密码加密，需要安装它。
    ```bash
    npm install bcryptjs @types/bcryptjs # NestJS推荐使用 bcryptjs 因为它完全由JavaScript实现，兼容性更好
    # 或者 yarn add bcryptjs @types/bcryptjs
    ```

**三、实现用户认证（登录）**

用户登录是管理后台的核心功能。我们将使用JWT（JSON Web Tokens）进行认证。

1.  **安装认证相关依赖：**
    ```bash
    npm install @nestjs/passport passport passport-jwt @nestjs/jwt jsonwebtoken
    npm install --save-dev @types/passport-jwt @types/jsonwebtoken
    # 或者 yarn add ...
    ```
2.  **创建Auth模块：**
    * 运行 `nest g module auth`。
    * **Auth Service：** 负责验证用户身份、生成JWT令牌。
    * **Auth Controller：** 提供登录接口（如 `POST /auth/login`）。
    * **JWT Strategy：** 用于解析和验证传入请求中的JWT令牌，提取用户信息。
    * **Guard：** 保护需要认证的接口。

    ```typescript
    // src/auth/auth.service.ts (部分代码)
    import { Injectable, UnauthorizedException } from '@nestjs/common';
    import { UserService } from '../user/user.service';
    import * as bcrypt from 'bcrypt';
    import { JwtService } from '@nestjs/jwt';

    @Injectable()
    export class AuthService {
      constructor(
        private userService: UserService,
        private jwtService: JwtService,
      ) {}

      async validateUser(username: string, pass: string): Promise<any> {
        const user = await this.userService.findByUsername(username);
        if (user && (await bcrypt.compare(pass, user.password))) {
          // 移除密码字段，不暴露给外部
          const { password, ...result } = user;
          return result;
        }
        return null;
      }

      async login(user: any) {
        const payload = { username: user.username, sub: user.id, role: user.role };
        return {
          access_token: this.jwtService.sign(payload),
        };
      }
    }
    ```

    ```typescript
    // src/auth/auth.controller.ts (部分代码)
    import { Controller, Post, Body, UseGuards, Request } from '@nestjs/common';
    import { AuthService } from './auth.service';
    import { LocalAuthGuard } from './guards/local-auth.guard'; // 稍后创建
    import { JwtAuthGuard } from './guards/jwt-auth.guard'; // 稍后创建

    @Controller('auth')
    export class AuthController {
      constructor(private authService: AuthService) {}

      @UseGuards(LocalAuthGuard) // 使用本地策略守卫
      @Post('login')
      async login(@Request() req) {
        return this.authService.login(req.user);
      }

      @UseGuards(JwtAuthGuard) // 使用JWT守卫保护接口
      @Get('profile')
      getProfile(@Request() req) {
        return req.user; // 返回已认证的用户信息
      }
    }
    ```
3.  **创建Local Strategy和JWT Strategy：** 负责Passport的认证逻辑。
4.  **创建Guard：** 用于保护路由。
5.  **配置JwtModule：** 在AuthModule中配置JwtModule，包括 secret 和 token 过期时间。

**四、测试用户管理功能**

1.  **启动应用：** 在项目根目录运行 `npm run start:dev`。
2.  **使用API测试工具：**
    * **注册用户：** 向 `http://localhost:3000/users/register` 发送 `POST` 请求，Body中包含 `username`, `password`, `role` 等信息。
    * **登录用户：** 向 `http://localhost:3000/auth/login` 发送 `POST` 请求，Body中包含 `username`, `password`。成功后会返回 `access_token`。
    * **访问受保护接口：** 携带登录后获得的 `access_token`（在请求头 `Authorization: Bearer YOUR_TOKEN` 中）访问受保护的接口，如 `http://localhost:3000/auth/profile`。

通过本篇教程，你已经成功构建了NestJS后端的用户管理模块，包括用户注册、数据库存储和基本的认证功能。这是你整个管理后台的基础骨架，为后续的权限控制和业务流程奠定了坚实基础。

---

**[请告诉我“继续”，我将提供第四篇：附件存储与管理（COS集成）。]**