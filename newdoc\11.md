好的，我们继续进行前端部分的**第三篇：服务浏览与订单创建流程**。

在本篇中，我们将聚焦于用户浏览服务、查看服务详情以及创建订单的核心业务流程。这将涉及到如何从后端获取服务数据，并使用 Ant Design 组件进行展示和交互。

---

## 第三篇：服务浏览与订单创建流程

### 3.1 服务列表与分类

我们将创建“首页”或“服务列表页”，展示所有可用的服务，并支持服务分类的筛选和搜索功能。

#### 3.1.1 服务列表页 (`src/pages/ServiceListPage.tsx`)

这个页面将作为用户进入平台后首先看到的服务展示界面。

```tsx
// src/pages/ServiceListPage.tsx
import React, { useState, useEffect } from 'react';
import { Card, List, Typography, Spin, Space, Input, Select, Button, Tag } from 'antd';
import { Link } from 'react-router-dom';
import api from '../services/api';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

// 定义服务类型，与后端 Service 实体字段对应
interface Service {
  id: number;
  name: string;
  description: string;
  price: number;
  imageUrl?: string;
  category?: {
    id: number;
    name: string;
  };
  // ... 其他服务字段
}

// 定义服务分类类型
interface ServiceCategory {
  id: number;
  name: string;
}

const ServiceListPage: React.FC = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<number | undefined>(undefined);

  useEffect(() => {
    fetchServicesAndCategories();
  }, []);

  const fetchServicesAndCategories = async () => {
    setLoading(true);
    try {
      const [servicesResponse, categoriesResponse] = await Promise.all([
        api.get('/services'), // 假设后端获取服务列表 API
        api.get('/service-categories'), // 假设后端获取服务分类 API
      ]);
      setServices(servicesResponse.data);
      setCategories(categoriesResponse.data);
    } catch (error) {
      console.error('获取服务或分类失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 过滤服务列表
  const filteredServices = services.filter(service => {
    const matchesSearch = searchTerm ? service.name.toLowerCase().includes(searchTerm.toLowerCase()) : true;
    const matchesCategory = selectedCategory ? (service.category?.id === selectedCategory) : true;
    return matchesSearch && matchesCategory;
  });

  return (
    <div style={{ padding: '20px' }}>
      <Title level={2} style={{ textAlign: 'center', marginBottom: '30px' }}>我们的服务</Title>

      <Space direction="vertical" style={{ width: '100%', marginBottom: '20px' }}>
        <Search
          placeholder="搜索服务名称..."
          onSearch={value => setSearchTerm(value)}
          onChange={e => setSearchTerm(e.target.value)}
          enterButton
          style={{ width: '100%' }}
        />
        <Select
          placeholder="选择服务分类"
          style={{ width: '100%' }}
          allowClear
          onChange={value => setSelectedCategory(value)}
          value={selectedCategory}
        >
          {categories.map(category => (
            <Option key={category.id} value={category.id}>{category.name}</Option>
          ))}
        </Select>
      </Space>

      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}><Spin size="large" tip="加载服务列表..." /></div>
      ) : (
        <List
          grid={{
            gutter: 16,
            xs: 1,
            sm: 2,
            md: 3,
            lg: 4,
            xl: 4,
            xxl: 5,
          }}
          dataSource={filteredServices}
          locale={{ emptyText: '暂无符合条件的服务' }}
          renderItem={service => (
            <List.Item>
              <Link to={`/service/${service.id}`}>
                <Card
                  hoverable
                  cover={service.imageUrl && <img alt={service.name} src={service.imageUrl} style={{ height: 180, objectFit: 'cover' }} />}
                >
                  <Card.Meta
                    title={service.name}
                    description={
                      <>
                        <Text type="secondary" ellipsis={{ rows: 2 }}>{service.description}</Text>
                        <div style={{ marginTop: 8 }}>
                          <Text strong style={{ color: '#f5222d', fontSize: '1.2em' }}>¥{service.price.toFixed(2)}</Text>
                          {service.category && <Tag color="blue" style={{ marginLeft: 8 }}>{service.category.name}</Tag>}
                        </div>
                      </>
                    }
                  />
                </Card>
              </Link>
          )}
        />
      )}
    </div>
  );
};

export default ServiceListPage;
```

**在 `App.tsx` 中添加路由：**

```tsx
// src/App.tsx (部分)
import ServiceListPage from './pages/ServiceListPage';
// ... 其他 import

function App() {
  return (
    <Router>
      <Layout className="layout">
        <Header>
          {/* ... 你的 Header 内容，可能包含导航链接到 /services */}
        </Header>
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              <Route path="/" element={<ServiceListPage />} /> {/* 将首页设置为服务列表 */}
              <Route path="/services" element={<ServiceListPage />} />
              {/* ... 其他路由 */}
            </Routes>
          </div>
        </Content>
        {/* ... Footer */}
      </Layout>
    </Router>
  );
}

export default App;
```

### 3.2 服务详情页

当用户点击服务列表中的某个服务项时，将跳转到其详情页，展示更详细的服务信息。

#### 3.2.1 服务详情页 (`src/pages/ServiceDetailPage.tsx`)

```tsx
// src/pages/ServiceDetailPage.tsx
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Typography, Spin, Descriptions, Button, Divider, Space, message } from 'antd';
import api from '../services/api';
import { useUser } from '../contexts/UserContext'; // 可能需要判断用户是否登录才能下单

const { Title, Text, Paragraph } = Typography;

interface Service {
  id: number;
  name: string;
  description: string;
  price: number;
  imageUrl?: string;
  category?: {
    id: number;
    name: string;
  };
  // ... 其他详细服务字段，如服务时长、包含项目等
}

const ServiceDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>(); // 获取路由参数中的服务ID
  const navigate = useNavigate();
  const { isAuthenticated } = useUser(); // 获取认证状态

  const [service, setService] = useState<Service | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      fetchServiceDetail(parseInt(id));
    }
  }, [id]);

  const fetchServiceDetail = async (serviceId: number) => {
    setLoading(true);
    try {
      const response = await api.get(`/services/${serviceId}`); // 假设后端获取服务详情 API
      setService(response.data);
    } catch (error) {
      console.error('获取服务详情失败:', error);
      message.error('服务详情加载失败。');
      navigate('/services'); // 获取失败后可以跳转回服务列表
    } finally {
      setLoading(false);
    }
  };

  const handleOrderService = () => {
    if (!isAuthenticated) {
      message.warning('请先登录才能下单！');
      navigate('/login');
      return;
    }
    // 跳转到订单创建页面，并带上服务ID
    navigate(`/order/create?serviceId=${service?.id}`);
  };

  if (loading || !service) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}><Spin size="large" tip="加载服务详情..." /></div>
    );
  }

  return (
    <Card style={{ maxWidth: 1000, margin: '20px auto' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div style={{ textAlign: 'center' }}>
          {service.imageUrl && (
            <img
              src={service.imageUrl}
              alt={service.name}
              style={{ maxWidth: '100%', maxHeight: 400, objectFit: 'contain', borderRadius: '8px' }}
            />
          )}
          <Title level={2} style={{ marginTop: '20px' }}>{service.name}</Title>
          {service.category && <Tag color="blue">{service.category.name}</Tag>}
        </div>

        <Divider />

        <Descriptions bordered column={1} title="服务详情">
          <Descriptions.Item label="价格">
            <Text strong style={{ color: '#f5222d', fontSize: '1.5em' }}>¥{service.price.toFixed(2)}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="服务描述">
            <Paragraph>{service.description}</Paragraph>
          </Descriptions.Item>
          {/* 可以在这里添加更多服务详情，如服务时长、包含内容、注意事项等 */}
          <Descriptions.Item label="服务时长">
            约 1 小时 (示例)
          </Descriptions.Item>
        </Descriptions>

        <div style={{ textAlign: 'center', marginTop: '30px' }}>
          <Button type="primary" size="large" onClick={handleOrderService}>
            立即下单
          </Button>
        </div>
      </Space>
    </Card>
  );
};

export default ServiceDetailPage;
```

**在 `App.tsx` 中添加路由：**

```tsx
// src/App.tsx (部分)
import ServiceDetailPage from './pages/ServiceDetailPage';
// ... 其他 import

function App() {
  return (
    <Router>
      <Layout className="layout">
        {/* ... Header, Content, Footer */}
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              <Route path="/" element={<ServiceListPage />} />
              <Route path="/services" element={<ServiceListPage />} />
              <Route path="/service/:id" element={<ServiceDetailPage />} /> {/* 新增的服务详情路由 */}
              {/* ... 其他路由 */}
            </Routes>
          </div>
        </Content>
        {/* ... Footer */}
      </Layout>
    </Router>
  );
}

export default App;
```

### 3.3 创建订单流程

用户在服务详情页点击“立即下单”后，将跳转到订单创建页面，填写服务相关信息并提交订单。

#### 3.3.1 订单创建页面 (`src/pages/CreateOrderPage.tsx`)

这个页面将展示用户选择的服务信息，并允许用户选择服务地址、预约时间、填写备注等。为了简化，我们这里假设用户地址已经存在，并且从 `UserAddressesPage` 获取。

```tsx
// src/pages/CreateOrderPage.tsx
import React, { useState, useEffect } from 'react';
import { Form, Select, DatePicker, Input, Button, Card, Typography, message, Spin, Space } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import moment from 'moment';
import api from '../services/api';
import { useUser } from '../contexts/UserContext';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 确保与后端 Address 实体字段对应
interface Address {
  id: number;
  receiverName: string;
  phoneNumber: string;
  province: string;
  city: string;
  district: string;
  detailAddress: string;
  isDefault: boolean;
}

interface Service {
  id: number;
  name: string;
  price: number;
  // ... 其他服务字段
}

const CreateOrderPage: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation(); // 用于获取 URL 参数
  const { user } = useUser(); // 获取当前登录用户，以便获取其地址
  
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [userAddresses, setUserAddresses] = useState<Address[]>([]);
  const [totalAmount, setTotalAmount] = useState<number>(0); // 预估总金额

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const serviceId = queryParams.get('serviceId');

    const fetchData = async () => {
      setLoading(true);
      try {
        // 1. 获取服务详情
        if (serviceId) {
          const serviceRes = await api.get(`/services/${serviceId}`);
          setSelectedService(serviceRes.data);
          form.setFieldsValue({ serviceId: serviceRes.data.id }); // 设置表单服务ID
          setTotalAmount(serviceRes.data.price); // 初始化总金额为服务价格
        } else {
          message.error('未指定服务ID，无法创建订单。');
          navigate('/services');
          return;
        }

        // 2. 获取用户地址列表
        const addressRes = await api.get('/user/addresses');
        setUserAddresses(addressRes.data);
        
        // 如果有默认地址，设置到表单中
        const defaultAddress = addressRes.data.find((addr: Address) => addr.isDefault);
        if (defaultAddress) {
          form.setFieldsValue({ addressId: defaultAddress.id });
        }

      } catch (error) {
        console.error('加载订单创建数据失败:', error);
        message.error('加载订单创建数据失败，请稍后再试。');
        navigate('/services');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [location.search, navigate, form]); // 依赖 location.search 确保从服务详情页过来时能正确加载

  const onFinish = async (values: any) => {
    setSubmitting(true);
    try {
      const payload = {
        serviceId: values.serviceId,
        addressId: values.addressId,
        scheduledTime: values.scheduledTime.toISOString(), // 将 Moment 对象转换为 ISO 字符串
        remarks: values.remarks,
        // 其他可能的字段，如：paymentMethod, quantity 等
      };
      
      const response = await api.post('/orders/create', payload); // 假设后端创建订单 API
      message.success('订单创建成功！');
      navigate(`/order/${response.data.id}`); // 跳转到订单详情页
    } catch (error) {
      console.error('订单创建失败:', error);
      // 错误已由拦截器处理
    } finally {
      setSubmitting(false);
    }
  };

  // 禁用过去的日期和时间
  const disabledDate = (current: any) => {
    return current && current < moment().startOf('day'); // 禁用今天之前的日期
  };

  // TODO: 可以进一步限制具体的时间段，例如只允许预约明天开始的9点到18点

  if (loading || !selectedService) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}><Spin size="large" tip="加载订单信息..." /></div>
    );
  }

  return (
    <Card title="创建新订单" style={{ maxWidth: 800, margin: '20px auto' }}>
      <Form
        form={form}
        name="create_order"
        onFinish={onFinish}
        layout="vertical"
        initialValues={{
          serviceId: selectedService.id,
          scheduledTime: moment().add(1, 'hour').startOf('hour'), // 默认预约一小时后
          remarks: '',
        }}
      >
        <Form.Item label="服务名称">
          <Input value={selectedService.name} disabled />
          <Form.Item name="serviceId" noStyle><Input type="hidden" /></Form.Item> {/* 隐藏字段，用于提交 */}
        </Form.Item>

        <Form.Item label="服务价格">
          <Input value={`¥${selectedService.price.toFixed(2)}`} disabled />
        </Form.Item>

        <Form.Item
          name="addressId"
          label="选择服务地址"
          rules={[{ required: true, message: '请选择服务地址！' }]}
        >
          <Select placeholder="请选择您的服务地址">
            {userAddresses.map(address => (
              <Option key={address.id} value={address.id}>
                {`${address.receiverName} - ${address.phoneNumber} - ${address.province}${address.city}${address.district}${address.detailAddress}`}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="scheduledTime"
          label="预约服务时间"
          rules={[{ required: true, message: '请选择预约服务时间！' }]}
        >
          <DatePicker
            showTime={{ format: 'HH:mm' }}
            format="YYYY-MM-DD HH:mm"
            disabledDate={disabledDate}
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          name="remarks"
          label="备注"
          // rules={[{ max: 200, message: '备注不能超过200字！' }]}
        >
          <TextArea rows={4} placeholder="请填写您的特殊需求或注意事项" />
        </Form.Item>

        <Form.Item label="总金额">
          <Title level={4} style={{ color: '#f5222d', display: 'inline-block', margin: 0 }}>
            ¥{totalAmount.toFixed(2)}
          </Title>
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={submitting} block size="large">
            提交订单
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default CreateOrderPage;
```

**在 `App.tsx` 中添加路由：**

```tsx
// src/App.tsx (部分)
import CreateOrderPage from './pages/CreateOrderPage';
// ... 其他 import

function App() {
  return (
    <Router>
      <Layout className="layout">
        {/* ... Header, Content, Footer */}
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              {/* ... 其他公共路由 */}

              <Route element={<PrivateRoute />}>
                {/* ... 其他受保护的用户路由 */}
                <Route path="/order/create" element={<CreateOrderPage />} /> {/* 新增的订单创建路由 */}
              </Route>

              {/* ... 404 路由 */}
            </Routes>
          </div>
        </Content>
        {/* ... Footer */}
      </Layout>
    </Router>
  );
}

export default App;
```

---

**本篇总结：**

在这一篇中，我们完成了：

* **服务列表与分类：** 构建了展示所有服务并通过搜索和分类筛选的页面，使用了 Ant Design 的 `Card`, `List`, `Search`, `Select` 等组件。
* **服务详情页：** 实现了查看单个服务详细信息的页面，并提供了“立即下单”的入口，使用了 `Descriptions` 和 `Button`。
* **创建订单流程：** 设计了订单创建表单，允许用户选择服务地址、预约时间，并与后端 API 联调提交订单。我们还引入了 Moment.js 来处理日期时间选择。

通过这些功能的实现，用户现在可以顺畅地浏览平台提供的服务，了解详情，并最终提交订单。接下来，我们将进入订单管理和状态流转的实现。

---

您对本篇内容有什么疑问吗？如果没有，我们就可以进入**第四篇：订单管理与状态流转**了。