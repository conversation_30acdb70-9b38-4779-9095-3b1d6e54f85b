好的，我们继续进行前端部分的**第二篇：认证与用户中心界面实现**。

在本篇中，我们将专注于用户认证相关的界面开发，包括登录、注册、用户信息展示与更新，以及地址管理和密码修改。这些功能将紧密结合我们之前后端实现的认证与用户管理 API。

---

## 第二篇：认证与用户中心界面实现

### 2.1 登录与注册页面

我们将使用 Ant Design 的表单组件来构建美观且带有验证功能的登录和注册界面。

#### 2.1.1 登录页面 (`src/pages/LoginPage.tsx`)

我们将实现一个简单的登录表单，包括用户名/手机号和密码。

```tsx
// src/pages/LoginPage.tsx
import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message, Space } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import api from '../services/api'; // 引入我们配置好的 axios 实例

const { Title } = Typography;

const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate(); // 用于路由跳转

  const onFinish = async (values: any) => {
    setLoading(true);
    try {
      // 调用后端登录 API
      const response = await api.post('/auth/login', {
        username: values.username, // 后端可能支持用户名或手机号
        password: values.password,
      });

      // 登录成功，将 JWT Token 存储到 localStorage
      const { access_token } = response.data;
      localStorage.setItem('access_token', access_token);
      message.success('登录成功！');

      // 根据业务需求，可以获取用户角色并跳转到不同页面
      // 例如，如果后端在登录响应中返回了用户信息和角色
      // if (response.data.user && response.data.user.role === 'admin') {
      //   navigate('/admin/dashboard');
      // } else if (response.data.user && response.data.user.role === 'technician') {
      //   navigate('/technician/dashboard');
      // } else {
      //   navigate('/dashboard'); // 默认跳转到用户中心
      // }
      navigate('/dashboard'); // 暂时统一跳转到用户中心

    } catch (error: any) {
      // 错误已由 api 拦截器统一处理，这里可以不再重复 message.error
      // 如果需要特定的错误反馈，可以在这里处理
      console.error('登录失败:', error);
      // message.error(error.response?.data?.message || '登录失败，请稍后再试。'); // 如果拦截器没有处理
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 'calc(100vh - 134px)' }}>
      <Card style={{ width: 400, padding: '20px' }}>
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={3}>用户登录</Title>
        </div>
        <Form
          name="login"
          initialValues={{ remember: true }}
          onFinish={onFinish}
          autoComplete="off"
        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: '请输入用户名或手机号！' }]}
          >
            <Input prefix={<UserOutlined />} placeholder="用户名/手机号" />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入密码！' }]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="密码" />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading} block>
              登录
            </Button>
          </Form.Item>

          <Form.Item>
            <Space size="middle">
              <Link to="/register">立即注册</Link>
              <Link to="/forgot-password">忘记密码？</Link> {/* 预留忘记密码链接 */}
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default LoginPage;
```

#### 2.1.2 注册页面 (`src/pages/RegisterPage.tsx`)

注册页面将收集用户名、手机号、密码和确认密码。

```tsx
// src/pages/RegisterPage.tsx
import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message, Space } from 'antd';
import { UserOutlined, LockOutlined, MobileOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import api from '../services/api';

const { Title } = Typography;

const RegisterPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm(); // 使用 Form 实例，方便后续操作
  const navigate = useNavigate();

  const onFinish = async (values: any) => {
    setLoading(true);
    try {
      // 调用后端注册 API
      await api.post('/user/register', {
        username: values.username,
        password: values.password,
        phoneNumber: values.phoneNumber,
        // 注册时默认给普通用户角色，如果后端有其他角色，可以根据需求调整
        role: 'user',
      });
      message.success('注册成功！请登录。');
      navigate('/login'); // 注册成功后跳转到登录页
    } catch (error: any) {
      console.error('注册失败:', error);
      // message.error(error.response?.data?.message || '注册失败，请稍后再试。');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 'calc(100vh - 134px)' }}>
      <Card style={{ width: 400, padding: '20px' }}>
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={3}>用户注册</Title>
        </div>
        <Form
          form={form}
          name="register"
          onFinish={onFinish}
          autoComplete="off"
          scrollToFirstError // 提交失败时自动滚动到第一个错误
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名！' },
              { min: 4, message: '用户名至少4位！' },
              { max: 20, message: '用户名最多20位！' },
            ]}
          >
            <Input prefix={<UserOutlined />} placeholder="用户名" />
          </Form.Item>

          <Form.Item
            name="phoneNumber"
            rules={[
              { required: true, message: '请输入手机号！' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号！' },
            ]}
          >
            <Input prefix={<MobileOutlined />} placeholder="手机号" />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码！' },
              { min: 6, message: '密码至少6位！' },
            ]}
            hasFeedback // 密码输入时显示反馈图标
          >
            <Input.Password prefix={<LockOutlined />} placeholder="密码" />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            dependencies={['password']} // 依赖 password 字段
            hasFeedback
            rules={[
              { required: true, message: '请再次确认密码！' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致！'));
                },
              }),
            ]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="确认密码" />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading} block>
              注册
            </Button>
          </Form.Item>

          <Form.Item>
            已有账号？<Link to="/login">立即登录</Link>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default RegisterPage;
```

### 2.2 用户信息展示与更新

创建用户中心页面，用于展示和更新用户个人信息。

#### 2.2.1 `UserContext` 认证上下文（可选但推荐）

在多页面应用中，每次都需要从 `localStorage` 读取 Token 并检查认证状态比较繁琐。使用 React 的 **Context API** 可以方便地在组件树中共享认证状态和用户信息。

创建一个 `src/contexts/UserContext.tsx` 文件：

```tsx
// src/contexts/UserContext.tsx
import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
import api from '../services/api';
import { Spin } from 'antd'; // 用于加载状态

// 定义用户类型，与后端 User 实体字段对应
interface User {
  id: number;
  username: string;
  phoneNumber: string;
  email?: string;
  role: string; // 例如 'user', 'technician', 'admin'
  balance?: number; // 技师可能有的余额
  // ... 其他用户字段
}

interface UserContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (token: string) => void;
  logout: () => void;
  fetchUserProfile: () => Promise<void>; // 刷新用户资料
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 获取用户资料
  const fetchUserProfile = async () => {
    setIsLoading(true);
    const token = localStorage.getItem('access_token');
    if (token) {
      try {
        const response = await api.get('/user/info'); // 假设后端有 /user/info 接口
        setUser(response.data);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('获取用户资料失败:', error);
        localStorage.removeItem('access_token'); // 获取失败，清除 Token
        setUser(null);
        setIsAuthenticated(false);
      }
    } else {
      setUser(null);
      setIsAuthenticated(false);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchUserProfile();
  }, []); // 组件加载时获取一次

  const login = (token: string) => {
    localStorage.setItem('access_token', token);
    fetchUserProfile(); // 登录后立即获取用户资料
  };

  const logout = () => {
    localStorage.removeItem('access_token');
    setUser(null);
    setIsAuthenticated(false);
  };

  return (
    <UserContext.Provider value={{ user, isAuthenticated, isLoading, login, logout, fetchUserProfile }}>
      {isLoading ? (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
          <Spin size="large" tip="加载中..." />
        </div>
      ) : (
        children
      )}
    </UserContext.Provider>
  );
};

// 自定义 Hook 方便在组件中使用
export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
```

**将 `UserProvider` 包装在 `src/main.tsx` 中：**

```tsx
// src/main.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import { UserProvider } from './contexts/UserContext.tsx'; // 引入 UserProvider

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <UserProvider> {/* 将整个 App 包裹在 UserProvider 中 */}
      <App />
    </UserProvider>
  </React.StrictMode>,
);
```

现在，`PrivateRoute` 可以使用 `useUser` 了：

```tsx
// src/components/PrivateRoute.tsx (修改后)
import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useUser } from '../contexts/UserContext'; // 引入 useUser

interface PrivateRouteProps {
  allowedRoles?: string[]; // 允许访问的角色列表
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({ allowedRoles }) => {
  const { isAuthenticated, user, isLoading } = useUser(); // 使用 useUser Hook

  if (isLoading) {
    // 在这里渲染一个全屏加载动画，因为 UserProvider 内部已经处理了 Spin
    // 如果 UserProvider 内部没有处理，这里需要显示一个加载状态
    return null; // 或者返回一个简单的加载占位符，取决于 UserProvider 的实现
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // 如果有角色限制，并且当前用户角色不在允许列表中
  if (allowedRoles && user && !allowedRoles.includes(user.role)) {
    // 可以重定向到无权限页面或首页
    return <Navigate to="/" replace />; // 假设无权限直接回首页
  }

  return <Outlet />;
};

export default PrivateRoute;
```

#### 2.2.2 用户信息展示与更新页面 (`src/pages/UserProfilePage.tsx`)

创建一个 `src/pages/UserProfilePage.tsx` 文件。

```tsx
// src/pages/UserProfilePage.tsx
import React, { useEffect, useState } from 'react';
import { Card, Descriptions, Button, Form, Input, message, Spin, Space } from 'antd';
import { useUser } from '../contexts/UserContext';
import api from '../services/api';

const UserProfilePage: React.FC = () => {
  const { user, isLoading, fetchUserProfile } = useUser();
  const [editing, setEditing] = useState(false);
  const [form] = Form.useForm();
  const [updateLoading, setUpdateLoading] = useState(false);

  useEffect(() => {
    // 当 user 数据加载或更新时，设置表单初始值
    if (user) {
      form.setFieldsValue({
        username: user.username,
        phoneNumber: user.phoneNumber,
        email: user.email,
        // ... 其他用户字段
      });
    }
  }, [user, form]);

  const handleEdit = () => {
    setEditing(true);
  };

  const handleCancel = () => {
    setEditing(false);
    // 重置表单到初始值
    form.setFieldsValue({
      username: user?.username,
      phoneNumber: user?.phoneNumber,
      email: user?.email,
    });
  };

  const onFinish = async (values: any) => {
    setUpdateLoading(true);
    try {
      // 调用后端更新用户信息 API
      await api.put('/user/info', values); // 假设后端 /user/info 支持 PUT 方法更新
      message.success('用户信息更新成功！');
      setEditing(false);
      fetchUserProfile(); // 刷新用户资料
    } catch (error: any) {
      console.error('更新用户信息失败:', error);
      // 错误已由拦截器处理
    } finally {
      setUpdateLoading(false);
    }
  };

  if (isLoading || !user) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" tip="加载用户资料..." />
      </div>
    );
  }

  return (
    <Card title="我的个人资料" style={{ maxWidth: 800, margin: '20px auto' }}>
      {editing ? (
        <Form
          form={form}
          name="user_profile_edit"
          onFinish={onFinish}
          layout="vertical"
          initialValues={user}
        >
          <Form.Item
            label="用户名"
            name="username"
            rules={[{ required: true, message: '请输入用户名！' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="手机号"
            name="phoneNumber"
            rules={[{ required: true, message: '请输入手机号！' }, { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号！' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="邮箱"
            name="email"
            rules={[{ type: 'email', message: '请输入有效的邮箱地址！' }]}
          >
            <Input />
          </Form.Item>
          {/* 添加其他需要编辑的字段 */}
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={updateLoading}>
                保存
              </Button>
              <Button onClick={handleCancel}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      ) : (
        <>
          <Descriptions bordered column={1}>
            <Descriptions.Item label="用户ID">{user.id}</Descriptions.Item>
            <Descriptions.Item label="用户名">{user.username}</Descriptions.Item>
            <Descriptions.Item label="手机号">{user.phoneNumber}</Descriptions.Item>
            <Descriptions.Item label="邮箱">{user.email || '未设置'}</Descriptions.Item>
            <Descriptions.Item label="角色">{user.role}</Descriptions.Item>
            {user.role === 'technician' && ( // 技师角色显示余额
              <Descriptions.Item label="账户余额">¥{user.balance?.toFixed(2) || '0.00'}</Descriptions.Item>
            )}
            {/* 添加其他需要展示的字段 */}
          </Descriptions>
          <Button type="primary" style={{ marginTop: 20 }} onClick={handleEdit}>
            编辑资料
          </Button>
        </>
      )}
    </Card>
  );
};

export default UserProfilePage;
```

**在 `App.tsx` 中添加路由：**

```tsx
// src/App.tsx (部分)
import UserProfilePage from './pages/UserProfilePage';

function App() {
  return (
    <Router>
      <Layout className="layout">
        {/* ... Header, Content, Footer */}
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/login" element={<LoginPage />} />
              <Route path="/register" element={<RegisterPage />} />

              <Route element={<PrivateRoute />}>
                <Route path="/dashboard" element={<UserDashboard />} /> {/* 你的用户中心首页 */}
                <Route path="/profile" element={<UserProfilePage />} /> {/* 新增的个人资料页面 */}
                {/* ... 其他受保护的用户路由 */}
              </Route>

              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </div>
        </Content>
        {/* ... Footer */}
      </Layout>
    </Router>
  );
}

export default App;
```

你可能还需要在 `UserDashboard`（即 `src/pages/UserDashboard.tsx`）中添加导航到 `UserProfilePage` 的链接。例如：

```tsx
// src/pages/UserDashboard.tsx (修改后)
import React from 'react';
import { Typography, Card, Space, Button } from 'antd';
import { Link } from 'react-router-dom';
import { useUser } from '../contexts/UserContext'; // 引入 useUser Hook

const { Title, Text } = Typography;

const UserDashboard: React.FC = () => {
  const { user, logout } = useUser(); // 获取用户信息和注销方法

  const handleLogout = () => {
    logout();
    // 退出后可以跳转到登录页或首页
  };

  return (
    <div style={{ padding: '20px' }}>
      <Title level={2}>欢迎您，{user?.username}！</Title>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card title="个人信息概览">
          <p><Text strong>手机号:</Text> {user?.phoneNumber}</p>
          <p><Text strong>角色:</Text> {user?.role}</p>
          {user?.role === 'technician' && (
            <p><Text strong>账户余额:</Text> ¥{user.balance?.toFixed(2) || '0.00'}</p>
          )}
          <Button type="link" onClick={() => { /* 可以导航到个人资料页 */ }}>
            <Link to="/profile">查看/编辑资料</Link>
          </Button>
        </Card>

        <Card title="我的订单">
          <p>查看您的订单历史和状态。</p>
          <Button type="link" onClick={() => { /* 导航到订单列表页 */ }}>
            <Link to="/orders">查看我的订单</Link>
          </Button>
        </Card>

        <Card title="我的地址">
          <p>管理您的收货地址。</p>
          <Button type="link" onClick={() => { /* 导航到地址管理页 */ }}>
            <Link to="/addresses">管理我的地址</Link>
          </Button>
        </Card>

        {user?.role === 'technician' && (
          <Card title="技师中心">
            <p>管理您的任务和收入。</p>
            <Button type="link" onClick={() => { /* 导航到技师任务页 */ }}>
              <Link to="/technician/tasks">我的任务</Link>
            </Button>
            <Button type="link" onClick={() => { /* 导航到收入管理页 */ }}>
              <Link to="/technician/finance">我的收入</Link>
            </Button>
          </Card>
        )}

        <Button type="primary" danger onClick={handleLogout} style={{ marginTop: '20px' }}>
          退出登录
        </Button>
      </Space>
    </div>
  );
};

export default UserDashboard;
```

### 2.3 地址管理

我们将创建一个页面来展示和管理用户的收货地址。

#### 2.3.1 地址列表与 CRUD 操作 (`src/pages/UserAddressesPage.tsx`)

创建一个 `src/pages/UserAddressesPage.tsx` 文件。

```tsx
// src/pages/UserAddressesPage.tsx
import React, { useState, useEffect } from 'react';
import { Card, List, Button, Modal, Form, Input, message, Spin, Space } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import api from '../services/api';

// 定义地址类型，与后端 Address 实体字段对应
interface Address {
  id: number;
  receiverName: string;
  phoneNumber: string;
  province: string;
  city: string;
  district: string;
  detailAddress: string;
  isDefault: boolean;
  // ... 其他地址字段
}

const UserAddressesPage: React.FC = () => {
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAddress, setEditingAddress] = useState<Address | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchAddresses();
  }, []);

  const fetchAddresses = async () => {
    setLoading(true);
    try {
      const response = await api.get('/user/addresses'); // 假设后端获取地址列表 API
      setAddresses(response.data);
    } catch (error) {
      console.error('获取地址失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingAddress(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (address: Address) => {
    setEditingAddress(address);
    form.setFieldsValue(address); // 设置表单初始值
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除此地址吗？',
      okText: '删除',
      cancelText: '取消',
      onOk: async () => {
        try {
          await api.delete(`/user/addresses/${id}`); // 假设后端删除地址 API
          message.success('地址删除成功！');
          fetchAddresses(); // 刷新列表
        } catch (error) {
          console.error('删除地址失败:', error);
        }
      },
    });
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      if (editingAddress) {
        // 编辑地址
        await api.put(`/user/addresses/${editingAddress.id}`, values); // 假设后端更新地址 API
        message.success('地址更新成功！');
      } else {
        // 添加地址
        await api.post('/user/addresses', values); // 假设后端添加地址 API
        message.success('地址添加成功！');
      }
      setModalVisible(false);
      fetchAddresses(); // 刷新列表
    } catch (error: any) {
      console.error('保存地址失败:', error);
      // 错误已由拦截器处理
    }
  };

  return (
    <Card title="我的地址" style={{ maxWidth: 800, margin: '20px auto' }} extra={
      <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
        新增地址
      </Button>
    }>
      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}><Spin size="large" /></div>
      ) : (
        <List
          itemLayout="horizontal"
          dataSource={addresses}
          renderItem={(item) => (
            <List.Item
              actions={[
                <Button type="link" icon={<EditOutlined />} onClick={() => handleEdit(item)}>编辑</Button>,
                <Button type="link" danger icon={<DeleteOutlined />} onClick={() => handleDelete(item.id)}>删除</Button>,
              ]}
            >
              <List.Item.Meta
                title={<span>{item.receiverName} ({item.phoneNumber}) {item.isDefault && <span style={{ color: 'red' }}>[默认]</span>}</span>}
                description={`${item.province}${item.city}${item.district}${item.detailAddress}`}
              />
            </List.Item>
          )}
        />
      )}

      <Modal
        title={editingAddress ? '编辑地址' : '新增地址'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        confirmLoading={form.isFieldsTouched() && form.getFieldsError().some(f => f.errors.length > 0) === false} // 仅在表单有修改且无错误时显示加载状态
      >
        <Form form={form} layout="vertical" name="address_form">
          <Form.Item
            name="receiverName"
            label="收件人姓名"
            rules={[{ required: true, message: '请输入收件人姓名！' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="phoneNumber"
            label="手机号码"
            rules={[{ required: true, message: '请输入手机号码！' }, { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号！' }]}
          >
            <Input />
          </Form.Item>
          {/* 这里可以集成 Ant Design 的级联选择器 `Cascader` 来选择省市区 */}
          <Space.Compact style={{ width: '100%' }}>
            <Form.Item name="province" label="省份" noStyle rules={[{ required: true, message: '请输入省份！' }]}>
              <Input placeholder="省份" />
            </Form.Item>
            <Form.Item name="city" label="城市" noStyle rules={[{ required: true, message: '请输入城市！' }]}>
              <Input placeholder="城市" />
            </Form.Item>
            <Form.Item name="district" label="区/县" noStyle rules={[{ required: true, message: '请输入区/县！' }]}>
              <Input placeholder="区/县" />
            </Form.Item>
          </Space.Compact>
          <Form.Item
            name="detailAddress"
            label="详细地址"
            rules={[{ required: true, message: '请输入详细地址！' }]}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item
            name="isDefault"
            valuePropName="checked" // 用于 Checkbox
          >
            <Input type="checkbox" /> 设为默认地址
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default UserAddressesPage;
```

**在 `App.tsx` 中添加路由：**

```tsx
// src/App.tsx (部分)
import UserAddressesPage from './pages/UserAddressesPage';

function App() {
  return (
    <Router>
      <Layout className="layout">
        {/* ... Header, Content, Footer */}
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              {/* ... 其他公共路由 */}

              <Route element={<PrivateRoute />}>
                {/* ... 其他受保护的用户路由 */}
                <Route path="/addresses" element={<UserAddressesPage />} /> {/* 新增的地址管理页面 */}
              </Route>

              {/* ... 404 路由 */}
            </Routes>
          </div>
        </Content>
        {/* ... Footer */}
      </Layout>
    </Router>
  );
}

export default App;
```

### 2.4 密码修改

实现一个独立的页面或模态框来让用户修改密码。

#### 2.4.1 密码修改页面 (`src/pages/ChangePasswordPage.tsx`)

创建一个 `src/pages/ChangePasswordPage.tsx` 文件。

```tsx
// src/pages/ChangePasswordPage.tsx
import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message } from 'antd';
import { LockOutlined } from '@ant-design/icons';
import api from '../services/api';

const { Title } = Typography;

const ChangePasswordPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const onFinish = async (values: any) => {
    setLoading(true);
    try {
      // 调用后端修改密码 API
      await api.put('/user/change-password', {
        oldPassword: values.oldPassword,
        newPassword: values.newPassword,
      });
      message.success('密码修改成功！请重新登录。');
      // 密码修改成功后，强制用户重新登录以使新的 Token 生效
      localStorage.removeItem('access_token');
      window.location.href = '/login'; // 重定向到登录页
    } catch (error: any) {
      console.error('密码修改失败:', error);
      // 错误已由拦截器处理
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 'calc(100vh - 134px)' }}>
      <Card style={{ width: 400, padding: '20px' }}>
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={3}>修改密码</Title>
        </div>
        <Form
          form={form}
          name="change_password"
          onFinish={onFinish}
          autoComplete="off"
        >
          <Form.Item
            name="oldPassword"
            rules={[{ required: true, message: '请输入当前密码！' }]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="当前密码" />
          </Form.Item>

          <Form.Item
            name="newPassword"
            rules={[
              { required: true, message: '请输入新密码！' },
              { min: 6, message: '新密码至少6位！' },
            ]}
            hasFeedback
          >
            <Input.Password prefix={<LockOutlined />} placeholder="新密码" />
          </Form.Item>

          <Form.Item
            name="confirmNewPassword"
            dependencies={['newPassword']}
            hasFeedback
            rules={[
              { required: true, message: '请再次确认新密码！' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的新密码不一致！'));
                },
              }),
            ]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="确认新密码" />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading} block>
              修改密码
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default ChangePasswordPage;
```

**在 `App.tsx` 中添加路由：**

```tsx
// src/App.tsx (部分)
import ChangePasswordPage from './pages/ChangePasswordPage';

function App() {
  return (
    <Router>
      <Layout className="layout">
        {/* ... Header, Content, Footer */}
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              {/* ... 其他公共路由 */}

              <Route element={<PrivateRoute />}>
                {/* ... 其他受保护的用户路由 */}
                <Route path="/change-password" element={<ChangePasswordPage />} /> {/* 新增的密码修改页面 */}
              </Route>

              {/* ... 404 路由 */}
            </Routes>
          </div>
        </Content>
        {/* ... Footer */}
      </Layout>
    </Router>
  );
}

export default App;
```

你还可以在 `UserDashboard` 或 `UserProfilePage` 中添加导航到密码修改页面的链接。

---

**本篇总结：**

在这一篇中，我们完成了：
* **登录和注册页面：** 使用 Ant Design `Form` 组件构建，并实现了与后端 API 的数据交互，包括 JWT Token 的存储和跳转逻辑。
* **`UserContext` 认证上下文：** 创建了一个全局的 Context 来管理用户的认证状态、用户信息和加载状态，简化了组件间的数据共享。
* **用户信息展示与更新页面：** 允许用户查看和编辑自己的个人资料，并与后端 API 联调。
* **地址管理页面：** 实现了地址的增删改查功能，通过模态框进行表单操作，并与后端地址管理 API 联调。
* **密码修改页面：** 提供了修改密码的功能，并强制用户在修改后重新登录，以更新认证状态。

通过这些页面的开发，我们对 React、Ant Design 和 Axios 的结合使用有了更深入的理解，并为后续的业务功能开发打下了坚实的用户认证基础。

---

您对本篇内容有什么疑问吗？如果没有，我们就可以进入**第三篇：服务浏览与订单创建流程**了。