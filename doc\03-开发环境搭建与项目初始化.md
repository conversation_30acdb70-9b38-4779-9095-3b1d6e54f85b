### **《新一代在线家政维修服务平台：从零构建企业级O2O服务生态》**

#### **第三篇：开发环境搭建与NestJS项目初始化——构建现代化开发基础**

**摘要：** 工欲善其事，必先利其器。本篇将详细指导你搭建一个完整的现代化开发环境，包括Node.js、数据库、开发工具的安装配置，以及使用NestJS CLI创建项目骨架。我们将采用Docker容器化方案，确保开发环境的一致性和可移植性。

---

**一、开发环境准备**

### 1. 基础工具安装

**Node.js 环境：**
```bash
# 推荐使用 Node.js 18+ LTS 版本
# 方式一：官网下载安装
# 访问 https://nodejs.org/ 下载LTS版本

# 方式二：使用 nvm 管理多版本Node.js (推荐)
# Windows用户使用 nvm-windows
# macOS/Linux用户使用 nvm

# 安装最新LTS版本
nvm install --lts
nvm use --lts

# 验证安装
node --version  # 应显示 v18.x.x 或更高
npm --version   # 应显示 9.x.x 或更高
```

**包管理器选择：**
```bash
# 推荐使用 pnpm，性能更好，节省磁盘空间
npm install -g pnpm

# 或者使用 yarn
npm install -g yarn

# 验证安装
pnpm --version
```

**开发工具推荐：**
- **IDE**: Visual Studio Code (必备插件：TypeScript、Prettier、ESLint)
- **API测试**: Postman 或 Apifox
- **数据库管理**: DBeaver 或 Navicat
- **版本控制**: Git

### 2. Docker环境搭建

使用Docker可以确保开发环境的一致性，避免"在我机器上能跑"的问题：

```bash
# 安装 Docker Desktop
# Windows/macOS: 访问 https://www.docker.com/products/docker-desktop/
# Linux: 使用包管理器安装

# 验证安装
docker --version
docker-compose --version
```

**二、数据库环境搭建**

### 1. 使用Docker Compose搭建数据库

创建项目根目录并配置数据库环境：

```yaml
# docker-compose.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: home-service-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: home_service_platform
      MYSQL_USER: developer
      MYSQL_PASSWORD: dev123456
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:7-alpine
    container_name: home-service-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  adminer:
    image: adminer
    container_name: home-service-adminer
    restart: always
    ports:
      - "8080:8080"
    depends_on:
      - mysql

volumes:
  mysql_data:
  redis_data:
```

### 2. 启动数据库服务

```bash
# 创建项目目录
mkdir home-service-platform
cd home-service-platform

# 创建数据库初始化目录
mkdir -p database/init

# 启动数据库服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs mysql
```

### 3. 数据库初始化

将之前设计的数据库脚本放入初始化目录：

```sql
-- database/init/01-create-tables.sql
-- 这里放入第二篇中设计的所有建表语句

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 用户基础表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    phone VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    role ENUM('customer', 'provider', 'admin') NOT NULL COMMENT '用户角色',
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active' COMMENT '账户状态',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    real_name VARCHAR(50) COMMENT '真实姓名',
    id_card VARCHAR(18) COMMENT '身份证号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_phone (phone),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
) COMMENT '用户基础信息表';

-- 其他表结构...
```

**三、NestJS项目初始化**

### 1. 安装NestJS CLI

```bash
# 全局安装NestJS CLI
npm install -g @nestjs/cli

# 验证安装
nest --version
```

### 2. 创建项目

```bash
# 创建新项目
nest new home-service-backend

# 选择包管理器 (推荐pnpm)
cd home-service-backend

# 安装核心依赖
pnpm add @nestjs/typeorm typeorm mysql2
pnpm add @nestjs/config @nestjs/jwt @nestjs/passport
pnpm add passport passport-jwt passport-local
pnpm add bcryptjs class-validator class-transformer
pnpm add @nestjs/swagger swagger-ui-express

# 安装开发依赖
pnpm add -D @types/bcryptjs @types/passport-jwt @types/passport-local
```

### 3. 项目结构规划

```
src/
├── common/                 # 公共模块
│   ├── decorators/         # 装饰器
│   ├── filters/           # 异常过滤器
│   ├── guards/            # 守卫
│   ├── interceptors/      # 拦截器
│   └── pipes/             # 管道
├── config/                # 配置模块
├── database/              # 数据库相关
│   ├── entities/          # 实体定义
│   └── migrations/        # 数据库迁移
├── modules/               # 业务模块
│   ├── auth/              # 认证模块
│   ├── users/             # 用户管理
│   ├── providers/         # 服务商管理
│   ├── services/          # 服务管理
│   ├── orders/            # 订单管理
│   └── payments/          # 支付模块
├── app.module.ts
└── main.ts
```

### 4. 环境配置

```typescript
// .env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=developer
DB_PASSWORD=dev123456
DB_DATABASE=home_service_platform

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# 应用配置
APP_PORT=3000
APP_ENV=development

# 腾讯云配置
TENCENT_SECRET_ID=your-secret-id
TENCENT_SECRET_KEY=your-secret-key
COS_BUCKET=your-bucket-name
COS_REGION=ap-guangzhou
```

### 5. 数据库连接配置

```typescript
// src/config/database.config.ts
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';

export const getDatabaseConfig = (configService: ConfigService): TypeOrmModuleOptions => ({
  type: 'mysql',
  host: configService.get('DB_HOST'),
  port: configService.get('DB_PORT'),
  username: configService.get('DB_USERNAME'),
  password: configService.get('DB_PASSWORD'),
  database: configService.get('DB_DATABASE'),
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  synchronize: configService.get('APP_ENV') === 'development',
  logging: configService.get('APP_ENV') === 'development',
  timezone: '+00:00',
  charset: 'utf8mb4',
});
```

### 6. 应用模块配置

```typescript
// src/app.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { getDatabaseConfig } from './config/database.config';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    
    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: getDatabaseConfig,
      inject: [ConfigService],
    }),
    
    // 业务模块将在后续添加
  ],
})
export class AppModule {}
```

### 7. 应用启动配置

```typescript
// src/main.ts
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // 全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));
  
  // CORS配置
  app.enableCors({
    origin: ['http://localhost:3000', 'http://localhost:5173'],
    credentials: true,
  });
  
  // Swagger文档配置
  const config = new DocumentBuilder()
    .setTitle('家政维修服务平台 API')
    .setDescription('新一代在线家政维修服务平台后端API文档')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api-docs', app, document);
  
  const port = process.env.APP_PORT || 3000;
  await app.listen(port);
  
  console.log(`🚀 应用启动成功！`);
  console.log(`📖 API文档地址: http://localhost:${port}/api-docs`);
  console.log(`🔗 应用地址: http://localhost:${port}`);
}

bootstrap();
```

**四、开发工具配置**

### 1. TypeScript配置优化

```json
// tsconfig.json
{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "es2020",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false,
    "paths": {
      "@/*": ["src/*"],
      "@/common/*": ["src/common/*"],
      "@/config/*": ["src/config/*"],
      "@/modules/*": ["src/modules/*"]
    }
  }
}
```

### 2. 代码格式化配置

```json
// .prettierrc
{
  "singleQuote": true,
  "trailingComma": "all",
  "tabWidth": 2,
  "semi": true,
  "printWidth": 100,
  "endOfLine": "lf"
}
```

### 3. 启动项目测试

```bash
# 启动开发服务器
pnpm run start:dev

# 访问应用
# http://localhost:3000 - 应用首页
# http://localhost:3000/api-docs - API文档
# http://localhost:8080 - 数据库管理界面
```

通过本篇教程，我们已经搭建了一个完整的现代化开发环境，包括容器化的数据库服务和配置完善的NestJS项目。这为后续的功能开发提供了坚实的基础。

---

**[请告诉我"继续"，我将提供第四篇：用户管理与认证系统构建。]**
