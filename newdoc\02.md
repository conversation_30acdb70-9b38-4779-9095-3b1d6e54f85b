好的，我们继续进行定制化系列教程的第二篇。这一篇将是核心实战，我们将把设计文档中的数据模型转化为代码，并构建用户管理模块的基础API。

-----

### **《新一代在线家政维修服务平台：从需求到实现的高效开发实战》**

#### **定制化系列教程 第二篇：核心基础设施搭建——数据库设计与API基础**

**摘要：** 在本篇教程中，我们将基于您提供的“家维在线系统模块设计文档”中的数据模型和接口设计，开始搭建系统的核心基础设施。我们将重点关注数据库的实体关系设计、表的创建，并着手构建用户管理模块的基础API，确保数据的持久化和基本的用户操作。

-----

### **2.1 数据模型到数据库表结构转化**

您在设计文档的“4. 数据模型”章节中提供了非常详细的表格，这大大简化了我们的工作。我们将根据这些表格，使用TypeORM来定义我们的实体（Entities），并最终映射到MySQL数据库表。

**核心实体关系 (ERD) 概览：**

虽然设计文档中没有直接的ER图，但我们可以根据表格中的字段和“外键”描述来推导。以下是一个简化的核心实体关系概览。在实际项目中，我们可能会使用专门的ERD工具（如 draw.io, dbdiagram.io）来创建更详细的图表。

```mermaid
erDiagram
    USERS {
        String userId PK
        String username
        String phone
        String password
        String email
        JSON addresses
        Decimal balance
        DateTime registerTime
        DateTime lastLoginTime
        Int status
        String avatar
    }

    TECHNICIANS {
        String technicianId PK
        String name
        String phone
        String idCard
        JSON skills
        Int experience
        Decimal rating
        Int orderCount
        Decimal balance
        Decimal frozen_balance
        Decimal commission_rate
        Int status
        JSON location
        DateTime registerTime
        JSON certifications
    }

    SERVICE_CATEGORIES {
        String categoryId PK
        String categoryName
        DateTime createTime
    }

    SERVICES {
        String serviceId PK
        String serviceName
        String categoryId FK "ServiceCategory.categoryId"
        String description
        Decimal basePrice
        String imageUrl
        Int status
        DateTime createTime
        DateTime updateTime
        JSON tags
    }

    ORDERS {
        String orderNumber PK
        String userId FK "User.userId"
        String serviceId FK "Service.serviceId"
        DateTime appointmentTime
        String address
        String contact
        String phone
        String status
        Decimal amount
        Int payStatus
        String technicianId FK "Technician.technicianId"
        Boolean technicianAccepted
        Boolean technicianRejected
        String rejectReason
        DateTime createTime
        DateTime payTime
        DateTime completeTime
        String remarks
        String repairStatus
        JSON photos
        JSON review
    }

    PART_CATEGORIES {
        String categoryId PK
        String categoryName
        DateTime createTime
    }

    PARTS {
        String partId PK
        String partName
        String model
        String brand
        Decimal price
        Int stock
        String categoryId FK "PartCategory.categoryId"
        String description
        String imageUrl
        DateTime createTime
        DateTime updateTime
    }

    BRANDS {
        String brandId PK
        String brandName
        String logoUrl
        DateTime createTime
    }

    PRODUCT_CATEGORIES {
        String categoryId PK
        String categoryName
        DateTime createTime
    }

    PRODUCT_MODELS {
        String modelId PK
        String modelName
        String brandId FK "Brand.brandId"
        String categoryId FK "ProductCategory.categoryId"
        Int releaseYear
        String description
        String imageUrl
        DateTime createTime
    }

    PART_COMPATIBILITY {
        Int id PK
        String partId FK "Part.partId"
        String modelId FK "ProductModel.modelId"
        Boolean isOriginal
        Decimal priceAdjustment
        String notes
    }

    REVIEWS {
        String reviewId PK
        String orderNumber FK "Order.orderNumber"
        String userId FK "User.userId"
        String technicianId FK "Technician.technicianId"
        Decimal rating
        String content
        DateTime reviewTime
        JSON photos
        String replyContent
        DateTime replyTime
        Boolean isAnonymous
    }

    COMMISSION_RECORDS {
        String commission_id PK
        String order_number FK "Order.orderNumber"
        String technician_id FK "Technician.technicianId"
        Decimal order_amount
        Decimal commission_rate
        Decimal commission_amount
        Int status
        DateTime create_time
        DateTime settle_time
        String operator_id
        String remarks
    }

    WITHDRAW_RECORDS {
        String withdraw_id PK
        String technician_id FK "Technician.technicianId"
        Decimal amount
        String bank_account
        String bank_name
        String account_name
        Int status
        DateTime apply_time
        DateTime process_time
        String operator_id
        String remarks
    }

    USERS ||--o{ ORDERS : places
    TECHNICIANS ||--o{ ORDERS : handles
    SERVICE_CATEGORIES ||--o{ SERVICES : contains
    SERVICES ||--o{ ORDERS : provides
    PART_CATEGORIES ||--o{ PARTS : contains
    PARTS ||--o{ PART_COMPATIBILITY : has
    BRANDS ||--o{ PRODUCT_MODELS : owns
    PRODUCT_CATEGORIES ||--o{ PRODUCT_MODELS : belongs_to
    PRODUCT_MODELS ||--o{ PART_COMPATIBILITY : compatible_with
    ORDERS ||--o{ REVIEWS : reviewed_by
    TECHNICIANS ||--o{ REVIEWS : receives
    ORDERS ||--o{ COMMISSION_RECORDS : generates
    TECHNICIANS ||--o{ COMMISSION_RECORDS : earns
    TECHNICIANS ||--o{ WITHDRAW_RECORDS : requests
```

**重要提示：**

  * `JSON` 类型字段（如 `addresses`, `skills`, `location`, `photos`, `review`, `tags`, `certifications`）在MySQL中通常使用 `JSON` 数据类型存储，或者如果你使用的是旧版本MySQL，可以使用 `TEXT` 类型存储JSON字符串。TypeORM支持JSON类型。
  * `Array` 类型字段同样在MySQL中会映射为 `JSON` 或 `TEXT`。
  * `DateTime` 在MySQL中通常为 `DATETIME` 类型。
  * `Decimal` (精确小数) 在MySQL中通常为 `DECIMAL(precision, scale)` 类型，例如 `DECIMAL(10, 2)` 表示总共10位数字，其中2位是小数。
  * `Boolean` 在MySQL中通常为 `TINYINT(1)`。
  * `String` 在MySQL中通常为 `VARCHAR(length)` 或 `TEXT`。
  * `Integer` 在MySQL中通常为 `INT`。
  * 对于 `userId` 这样的ID，考虑到您设计文档中给出了 `U10001` 这样的示例值，我们可能需要自定义ID生成策略（例如，在Service层生成UUID或基于序列号的ID），而不是依赖数据库自增。这里先用 `PrimaryColumn` 并指定 `unique: true`。

**TypeORM实体定义：**

我们将在 `src` 目录下为每个主要模块创建子目录，并在其中放置实体文件。

**1. 准备实体文件目录结构：**

在 `src` 目录下创建以下文件夹结构：

```
src/
├── user/
│   └── entities/
│       └── user.entity.ts
├── technician/
│   └── entities/
│       └── technician.entity.ts
├── service/
│   └── entities/
│       ├── service.entity.ts
│       └── service-category.entity.ts
├── order/
│   └── entities/
│       └── order.entity.ts
├── part/
│   └── entities/
│       ├── part.entity.ts
│       ├── part-category.entity.ts
│       └── part-compatibility.entity.ts
├── product/
│   └── entities/
│       ├── brand.entity.ts
│       ├── product-category.entity.ts
│       └── product-model.entity.ts
├── review/
│   └── entities/
│       └── review.entity.ts
├── finance/
│   └── entities/
│       ├── commission-record.entity.ts
│       └── withdraw-record.entity.ts
└── app.module.ts
└── main.ts
... (其他 NestJS 生成的文件)
```

**2. 定义实体 (以 User 实体为例):**

在 `src/user/entities/user.entity.ts` 文件中创建 `User` 实体。

```typescript
// src/user/entities/user.entity.ts
import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('users') // 对应数据库表名
export class User {
  @PrimaryColumn({ type: 'varchar', length: 50, unique: true })
  userId: string; // 用户唯一标识，例如 "U10001"

  @Column({ type: 'varchar', length: 100 })
  username: string; // 用户名

  @Column({ type: 'varchar', length: 20, unique: true })
  phone: string; // 手机号码

  @Column({ type: 'varchar', length: 255, nullable: true, unique: true })
  email: string; // 电子邮箱

  @Column({ type: 'varchar', length: 255 }) // 密码将存储加密后的哈希值
  password: string;

  @Column({ type: 'json', nullable: true }) // 地址列表，存储JSON数组
  // TypeORM 会自动处理 JSON 类型字段的序列化和反序列化
  addresses: any[];

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0.00 })
  balance: number; // 账户余额

  @CreateDateColumn({ type: 'timestamp' }) // 自动设置创建时间
  registerTime: Date; // 注册时间

  @Column({ type: 'timestamp', nullable: true })
  lastLoginTime: Date; // 最后登录时间

  @Column({ type: 'int', default: 1 }) // 账户状态，例如 1:正常, 0:禁用
  status: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  avatar: string; // 头像URL
}
```

**3. 定义所有核心实体文件：**

请按照设计文档“表4-2”到“表4-11”中的字段和数据类型定义，在对应的 `entities` 文件夹中创建以下实体文件。这里只给出部分示例，请您参照 `User` 实体进行补充。

  * **`src/technician/entities/technician.entity.ts`**

    ```typescript
    import { Entity, PrimaryColumn, Column, CreateDateColumn } from 'typeorm';

    @Entity('technicians')
    export class Technician {
      @PrimaryColumn({ type: 'varchar', length: 50, unique: true })
      technicianId: string;

      @Column({ type: 'varchar', length: 100 })
      name: string;

      @Column({ type: 'varchar', length: 20, unique: true })
      phone: string;

      @Column({ type: 'varchar', length: 18, unique: true })
      idCard: string;

      @Column({ type: 'json' }) // 存储技能数组
      skills: string[];

      @Column({ type: 'int' })
      experience: number;

      @Column({ type: 'decimal', precision: 3, scale: 1, default: 5.0 })
      rating: number;

      @Column({ type: 'int', default: 0 })
      orderCount: number;

      @Column({ type: 'decimal', precision: 10, scale: 2, default: 0.00 })
      balance: number;

      @Column({ type: 'decimal', precision: 10, scale: 2, default: 0.00 })
      frozen_balance: number;

      @Column({ type: 'decimal', precision: 3, scale: 2, default: 0.70 })
      commission_rate: number;

      @Column({ type: 'int', default: 1 }) // 1: 空闲, 0: 忙碌, 2: 禁用
      status: number;

      @Column({ type: 'json', nullable: true }) // 存储经纬度 {lat: ..., lng: ...}
      location: { lat: number; lng: number };

      @CreateDateColumn({ type: 'timestamp' })
      registerTime: Date;

      @Column({ type: 'json', nullable: true }) // 存储认证信息数组
      certifications: any[];
    }
    ```

  * **`src/service/entities/service-category.entity.ts`**

    ```typescript
    import { Entity, PrimaryColumn, Column, CreateDateColumn, OneToMany } from 'typeorm';
    import { Service } from './service.entity'; // 导入 Service 实体

    @Entity('service_categories')
    export class ServiceCategory {
        @PrimaryColumn({ type: 'varchar', length: 50, unique: true })
        categoryId: string;

        @Column({ type: 'varchar', length: 100, unique: true })
        categoryName: string;

        @CreateDateColumn({ type: 'timestamp' })
        createTime: Date;

        // 定义一对多关系：一个分类可以有多个服务
        @OneToMany(() => Service, service => service.category)
        services: Service[];
    }
    ```

  * **`src/service/entities/service.entity.ts`**

    ```typescript
    import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
    import { ServiceCategory } from './service-category.entity'; // 导入 ServiceCategory 实体
    import { Order } from '../../order/entities/order.entity'; // 导入 Order 实体（稍后创建）

    @Entity('services')
    export class Service {
      @PrimaryColumn({ type: 'varchar', length: 50, unique: true })
      serviceId: string;

      @Column({ type: 'varchar', length: 100 })
      serviceName: string;

      @Column({ type: 'varchar', length: 50 })
      categoryId: string; // 外键字段

      @ManyToOne(() => ServiceCategory, category => category.services)
      @JoinColumn({ name: 'categoryId' }) // 指定外键列名
      category: ServiceCategory; // 关联的 ServiceCategory 实体

      @Column({ type: 'text' })
      description: string;

      @Column({ type: 'decimal', precision: 10, scale: 2 })
      basePrice: number;

      @Column({ type: 'varchar', length: 255, nullable: true })
      imageUrl: string;

      @Column({ type: 'int', default: 1 }) // 1: 上架, 0: 下架
      status: number;

      @CreateDateColumn({ type: 'timestamp' })
      createTime: Date;

      @UpdateDateColumn({ type: 'timestamp' })
      updateTime: Date;

      @Column({ type: 'json', nullable: true }) // 存储标签数组
      tags: string[];

      // 定义一对多关系：一个服务可以被多个订单使用
      // @OneToMany(() => Order, order => order.service) // 稍后在 Order 实体中完善
      // orders: Order[];
    }
    ```

  * **`src/order/entities/order.entity.ts`**

    ```typescript
    import { Entity, PrimaryColumn, Column, CreateDateColumn, ManyToOne, JoinColumn, OneToOne, OneToMany } from 'typeorm';
    import { User } from '../../user/entities/user.entity'; // 导入 User 实体
    import { Technician } from '../../technician/entities/technician.entity'; // 导入 Technician 实体
    import { Service } from '../../service/entities/service.entity'; // 导入 Service 实体
    import { Review } from '../../review/entities/review.entity'; // 导入 Review 实体（稍后创建）
    import { CommissionRecord } from '../../finance/entities/commission-record.entity'; // 导入 CommissionRecord 实体（稍后创建）

    @Entity('orders')
    export class Order {
      @PrimaryColumn({ type: 'varchar', length: 50, unique: true })
      orderNumber: string;

      @Column({ type: 'varchar', length: 50 })
      userId: string; // 外键字段

      @ManyToOne(() => User)
      @JoinColumn({ name: 'userId' })
      user: User;

      @Column({ type: 'varchar', length: 50 })
      serviceId: string; // 外键字段

      @ManyToOne(() => Service)
      @JoinColumn({ name: 'serviceId' })
      service: Service; // 关联的 Service 实体

      @Column({ type: 'varchar', length: 100 })
      serviceName: string; //冗余字段，方便快速查询

      @Column({ type: 'timestamp' })
      appointmentTime: Date;

      @Column({ type: 'varchar', length: 255 })
      address: string;

      @Column({ type: 'varchar', length: 100 })
      contact: string;

      @Column({ type: 'varchar', length: 20 })
      phone: string;

      @Column({ type: 'varchar', length: 50 }) // 订单状态：待支付、待安排、待服务、服务中、已完成、售后中、退款中
      status: string;

      @Column({ type: 'decimal', precision: 10, scale: 2 })
      amount: number;

      @Column({ type: 'int', default: 0 }) // 支付状态：0:未支付, 1:已支付, 2:支付失败
      payStatus: number;

      @Column({ type: 'varchar', length: 50, nullable: true })
      technicianId: string; // 外键字段，可为空

      @ManyToOne(() => Technician, { nullable: true })
      @JoinColumn({ name: 'technicianId' })
      technician: Technician;

      @Column({ type: 'boolean', default: false })
      technicianAccepted: boolean;

      @Column({ type: 'boolean', default: false })
      technicianRejected: boolean;

      @Column({ type: 'text', nullable: true })
      rejectReason: string;

      @CreateDateColumn({ type: 'timestamp' })
      createTime: Date;

      @Column({ type: 'timestamp', nullable: true })
      payTime: Date;

      @Column({ type: 'timestamp', nullable: true })
      completeTime: Date;

      @Column({ type: 'text', nullable: true })
      remarks: string;

      @Column({ type: 'varchar', length: 50, nullable: true }) // 维修状态，如：现场评估中，等待配件，维修中，待收款
      repairStatus: string;

      @Column({ type: 'json', nullable: true }) // 存储维修过程中的照片URLs
      photos: string[];

      // 定义一对一关系：一个订单可能有一个评价
      @OneToOne(() => Review, review => review.order)
      review: Review;

      // 定义一对多关系：一个订单可能生成多条佣金记录（例如，拆装费和维修费分开结算）
      @OneToMany(() => CommissionRecord, commissionRecord => commissionRecord.order)
      commissionRecords: CommissionRecord[];
    }
    ```

  * **其他实体文件：** 请您根据“4. 数据模型”中的详细表格，为 `PartCategory`, `Part`, `Brand`, `ProductCategory`, `ProductModel`, `PartCompatibility`, `Review`, `CommissionRecord`, `WithdrawRecord` 创建相应的实体文件。确保它们使用了正确的装饰器 (`@Entity`, `@PrimaryColumn`, `@Column`, `@CreateDateColumn`, `@UpdateDateColumn`, `@ManyToOne`, `@OneToOne`, `@OneToMany`, `@JoinColumn`) 和数据类型。

**4. 更新 `AppModule` 以包含所有实体：**

在 `src/app.module.ts` 中，将所有新创建的实体类添加到 `TypeOrmModule.forRoot` 的 `entities` 数组中。

```typescript
// src/app.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';

// 导入所有你的实体类
import { User } from './user/entities/user.entity';
import { Technician } from './technician/entities/technician.entity';
import { ServiceCategory } from './service/entities/service-category.entity';
import { Service } from './service/entities/service.entity';
import { Order } from './order/entities/order.entity';
import { PartCategory } from './part/entities/part-category.entity';
import { Part } from './part/entities/part.entity';
import { Brand } from './product/entities/brand.entity';
import { ProductCategory } from './product/entities/product-category.entity';
import { ProductModel } from './product/entities/product-model.entity';
import { PartCompatibility } from './part/entities/part-compatibility.entity';
import { Review } from './review/entities/review.entity';
import { CommissionRecord } from './finance/entities/commission-record.entity';
import { WithdrawRecord } from './finance/entities/withdraw-record.entity';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: 'localhost',
      port: 3306,
      username: 'root',
      password: 'YOUR_MYSQL_ROOT_PASSWORD', // 替换为你的密码
      database: 'home_repair_db',
      entities: [
        User,
        Technician,
        ServiceCategory,
        Service,
        Order,
        PartCategory,
        Part,
        Brand,
        ProductCategory,
        ProductModel,
        PartCompatibility,
        Review,
        CommissionRecord,
        WithdrawRecord,
      ],
      synchronize: true, // 仍然在开发环境使用，它会在启动时自动创建或更新表
      logging: ['query', 'error'],
    }),
    // 稍后会在这里添加 UserModule
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
```

**重要提示：** `synchronize: true` 在开发阶段非常方便，它会在每次应用启动时检查实体定义并自动更新数据库模式。但在生产环境中，**切勿使用此选项**，因为它可能会导致数据丢失或意外的模式更改。生产环境应使用 TypeORM 提供的 [Migrations (数据库迁移)](https://www.google.com/search?q=https://typeorm.io/migrations) 功能来安全地管理数据库模式。

### **2.2 构建用户管理模块 (User Module) 基础API**

我们将首先实现设计文档中“3.1.1 用户管理”中的核心功能以及“5.1 用户接口”中的部分内容。

**1. 创建用户模块：**

如果您还没有创建，请在终端运行以下命令：

```bash
nest generate module user
nest generate service user
nest generate controller user
```

这会在 `src/user` 目录下创建 `user.module.ts`, `user.service.ts`, `user.controller.ts` 文件。

**2. 配置 UserModule (src/user/user.module.ts):**

打开 `src/user/user.module.ts` 文件。我们需要在这里导入 `TypeOrmModule.forFeature` 来注册 `User` 实体，以便 `UserService` 可以使用 `UserRepository`。

```typescript
// src/user/user.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm'; // 导入 TypeOrmModule
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { User } from './entities/user.entity'; // 导入 User 实体

@Module({
  imports: [TypeOrmModule.forFeature([User])], // 注册 User 实体到当前模块
  controllers: [UserController],
  providers: [UserService],
  exports: [UserService], // 如果其他模块需要使用 UserService，则需要导出
})
export class UserModule {}
```

**3. 在 `AppModule` 中导入 `UserModule`：**

现在 `UserModule` 已经准备好了，我们需要在主模块 (`AppModule`) 中导入它，以便 NestJS 能够识别并加载 `UserModule` 中定义的控制器和服务。

```typescript
// src/app.module.ts (部分内容，仅展示 imports 数组)
import { UserModule } from './user/user.module'; // 导入 UserModule

@Module({
  imports: [
    TypeOrmModule.forRoot({
      // ... 数据库配置保持不变
      entities: [
        // ... 所有实体列表
      ],
      synchronize: true,
      logging: ['query', 'error'],
    }),
    UserModule, // 在这里导入 UserModule
  ],
  // ... 其他部分保持不变
})
export class AppModule {}
```

**4. 安装 `bcrypt` 用于密码加密：**

为了安全地存储用户密码，我们将使用 `bcrypt` 库对其进行哈希处理。

```bash
npm install bcrypt
npm install -D @types/bcrypt
```

**5. 实现 UserService (src/user/user.service.ts):**

这个服务层将包含处理用户业务逻辑的方法。

```typescript
// src/user/user.service.ts
import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import * as bcrypt from 'bcrypt'; // 导入 bcrypt

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User) // 注入 User 实体对应的 TypeORM Repository
    private usersRepository: Repository<User>,
  ) {}

  /**
   * 用户注册
   * @param registerDto 注册数据
   * @returns 新创建的用户对象
   */
  async register(registerDto: any): Promise<User> {
    const { phone, password, username, email } = registerDto;

    // FR-UM-001: 系统应支持用户通过手机号+验证码、微信授权方式注册。
    // 这里先实现手机号+密码注册。验证码和微信授权会在后续集成。
    // 检查手机号是否已被注册
    const existingUserByPhone = await this.usersRepository.findOne({ where: { phone } });
    if (existingUserByPhone) {
      throw new BadRequestException('手机号已被注册');
    }

    // 如果提供了邮箱，检查邮箱是否已被注册
    if (email) {
      const existingUserByEmail = await this.usersRepository.findOne({ where: { email } });
      if (existingUserByEmail) {
        throw new BadRequestException('邮箱已被注册');
      }
    }

    // FR-UM-003: 系统应提供安全的登录机制（密码、验证码、微信授权），并支持密码找回/重置功能。
    // 表10-1: 密码加密存储
    // 生成密码哈希值，盐度为10
    const hashedPassword = await bcrypt.hash(password, 10);

    // 创建新的用户实体实例
    const newUser = this.usersRepository.create({
      // 简单的ID生成策略：U + 时间戳 + 随机数。实际项目可能使用 UUID 或其他更健壮的策略。
      userId: `U${Date.now()}${Math.floor(Math.random() * 1000)}`,
      username,
      phone,
      email,
      password: hashedPassword, // 存储哈希后的密码
      registerTime: new Date(), // 设置注册时间
      status: 1, // 默认正常状态
    });

    // 保存新用户到数据库
    return this.usersRepository.save(newUser);
  }

  /**
   * 验证用户登录凭证
   * @param phone 手机号
   * @param pass 原始密码
   * @returns 验证通过的用户对象 (不含密码) 或 null
   */
  async validateUser(phone: string, pass: string): Promise<Omit<User, 'password'>> {
    // 这是一个简化版的登录验证，实际的认证逻辑会更复杂，集成JWT将在第三篇。
    const user = await this.usersRepository.findOne({ where: { phone } });
    if (user && (await bcrypt.compare(pass, user.password))) {
      const { password, ...result } = user; // 移除密码返回
      return result;
    }
    return null;
  }

  /**
   * 根据用户ID查找用户
   * @param userId 用户ID
   * @returns 用户对象
   */
  async findOne(userId: string): Promise<User> {
    const user = await this.usersRepository.findOne({ where: { userId } });
    if (!user) {
      throw new NotFoundException('用户未找到');
    }
    return user;
  }

  /**
   * 更新用户信息
   * @param userId 用户ID
   * @param updateDto 更新数据
   * @returns 更新后的用户对象
   */
  async updateUserInfo(userId: string, updateDto: any): Promise<User> {
    const user = await this.usersRepository.findOne({ where: { userId } });
    if (!user) {
      throw new NotFoundException('用户未找到');
    }
    // 使用 Object.assign 更新用户字段
    Object.assign(user, updateDto);
    await this.usersRepository.save(user);
    return user;
  }

  /**
   * 添加用户地址
   * @param userId 用户ID
   * @param addressInfo 地址信息
   * @returns 更新后的用户对象
   */
  async addAddress(userId: string, addressInfo: any): Promise<User> {
    const user = await this.usersRepository.findOne({ where: { userId } });
    if (!user) {
      throw new NotFoundException('用户未找到');
    }
    if (!user.addresses) {
      user.addresses = []; // 如果地址列表为空，则初始化
    }
    // 为新地址生成一个简单的ID
    user.addresses.push({ id: `ADDR-${Date.now()}-${Math.floor(Math.random() * 1000)}`, ...addressInfo });
    return this.usersRepository.save(user);
  }

  /**
   * 获取用户地址列表
   * @param userId 用户ID
   * @returns 地址数组
   */
  async getAddresses(userId: string): Promise<any[]> {
    const user = await this.usersRepository.findOne({ where: { userId } });
    if (!user) {
      throw new NotFoundException('用户未找到');
    }
    return user.addresses || [];
  }

  /**
   * 更新用户密码
   * @param userId 用户ID
   * @param oldPassword 旧密码
   * @param newPassword 新密码
   */
  async updatePassword(userId: string, oldPassword: string, newPassword: string): Promise<void> {
    const user = await this.usersRepository.findOne({ where: { userId } });
    if (!user) {
      throw new NotFoundException('用户未找到');
    }
    // 验证旧密码是否正确
    const isPasswordValid = await bcrypt.compare(oldPassword, user.password);
    if (!isPasswordValid) {
      throw new BadRequestException('旧密码不正确');
    }
    // 哈希新密码并保存
    user.password = await bcrypt.hash(newPassword, 10);
    await this.usersRepository.save(user);
  }
}
```

**6. 实现 UserController (src/user/user.controller.ts):**

控制器负责处理HTTP请求，并调用服务层的方法。我们还将引入 DTO (Data Transfer Objects) 进行请求体验证。

```typescript
// src/user/user.controller.ts
import { Controller, Post, Body, Get, Param, Put, UseGuards, Request, HttpStatus } from '@nestjs/common';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto'; // 导入 DTO
import { UpdateUserDto } from './dto/update-user.dto'; // 导入 DTO
import { LoginUserDto } from './dto/login-user.dto'; // 导入 DTO
import { AddAddressDto } from './dto/add-address.dto'; // 导入 DTO
import { UpdatePasswordDto } from './dto/update-password.dto'; // 导入 DTO
// import { AuthGuard } from '@nestjs/passport'; // 稍后认证模块会用到，暂时注释

@Controller('api/user') // 定义基础路由 /api/user
export class UserController {
  constructor(private readonly userService: UserService) {}

  /**
   * 用户注册接口 (POST /api/user/register)
   * 对应设计文档 5.1 用户接口 - 用户注册
   */
  @Post('register')
  async register(@Body() createUserDto: CreateUserDto) {
    const user = await this.userService.register(createUserDto);
    // 实际项目中，注册成功后通常直接登录并返回token
    // 这里为了演示，我们先移除密码再返回
    const { password, ...result } = user;
    return {
      statusCode: HttpStatus.CREATED, // 201 Created
      message: '注册成功',
      data: result,
      // token: 'generate_jwt_token_here' // 稍后集成JWT
    };
  }

  /**
   * 用户登录接口 (POST /api/user/login)
   * 对应设计文档 5.1 用户接口 - 用户登录
   */
  @Post('login')
  // @UseGuards(AuthGuard('local')) // 使用本地策略进行初步认证，需要 AuthModule 支持，稍后集成
  async login(@Body() loginUserDto: LoginUserDto) {
    const user = await this.userService.validateUser(loginUserDto.phone, loginUserDto.password);
    if (!user) {
      // 登录失败，返回 401 Unauthorized 或 400 Bad Request
      return {
        statusCode: HttpStatus.BAD_REQUEST, // 也可以是 HttpStatus.UNAUTHORIZED
        message: '手机号或密码错误',
        success: false
      };
    }
    return {
      statusCode: HttpStatus.OK, // 200 OK
      message: '登录成功',
      success: true,
      data: user,
      // token: 'generate_jwt_token_here' // 稍后集成JWT
    };
  }

  /**
   * 获取用户信息接口 (GET /api/user/info)
   * 对应设计文档 5.1 用户接口 - 用户信息获取
   * @Request() req 用于访问请求对象，获取用户信息（如从 JWT token 中解析出的用户ID）
   */
  // @UseGuards(AuthGuard('jwt')) // 假设有 JWT 守卫保护，稍后集成
  @Get('info')
  async getUserInfo(@Request() req) {
    // 暂时硬编码一个用户ID，实际应从 req.user.userId (JWT token 解析后) 中获取
    // 确保这个 userId 存在于你的数据库中，或者先通过注册接口创建一个
    const userId = 'U1716616000000_example'; // 请替换为你实际创建的或测试用的 userId
    const user = await this.userService.findOne(userId);
    const { password, ...result } = user; // 移除密码
    return {
      statusCode: HttpStatus.OK,
      data: result
    };
  }

  /**
   * 更新用户信息接口 (PUT /api/user/info)
   * 对应设计文档 5.1 用户接口 - 用户信息更新
   */
  // @UseGuards(AuthGuard('jwt'))
  @Put('info')
  async updateUserInfo(@Request() req, @Body() updateDto: UpdateUserDto) {
    const userId = 'U1716616000000_example'; // 替换为从认证信息中获取的ID
    const updatedUser = await this.userService.updateUserInfo(userId, updateDto);
    const { password, ...result } = updatedUser; // 移除密码
    return {
      statusCode: HttpStatus.OK,
      message: '更新成功',
      data: result
    };
  }

  /**
   * 添加地址接口 (POST /api/user/address)
   * 对应设计文档 5.1 用户接口 - 地址管理
   */
  // @UseGuards(AuthGuard('jwt'))
  @Post('address')
  async addAddress(@Request() req, @Body() addAddressDto: AddAddressDto) {
    const userId = 'U1716616000000_example'; // 替换为从认证信息中获取的ID
    const user = await this.userService.addAddress(userId, addAddressDto);
    const { password, ...result } = user; // 移除密码
    return {
      statusCode: HttpStatus.CREATED,
      message: '地址添加成功',
      data: result.addresses // 返回更新后的地址列表
    };
  }

  /**
   * 获取地址列表接口 (GET /api/user/addresses)
   * 对应设计文档 5.1 用户接口 - 地址管理
   */
  // @UseGuards(AuthGuard('jwt'))
  @Get('addresses')
  async getAddresses(@Request() req) {
    const userId = 'U1716616000000_example'; // 替换为从认证信息中获取的ID
    const addresses = await this.userService.getAddresses(userId);
    return {
      statusCode: HttpStatus.OK,
      data: addresses
    };
  }

  /**
   * 修改密码接口 (PUT /api/user/password)
   * 对应设计文档 5.1 用户接口 - 密码修改
   */
  // @UseGuards(AuthGuard('jwt'))
  @Put('password')
  async updatePassword(@Request() req, @Body() updatePasswordDto: UpdatePasswordDto) {
    const userId = 'U1716616000000_example'; // 替换为从认证信息中获取的ID
    await this.userService.updatePassword(userId, updatePasswordDto.oldPassword, updatePasswordDto.newPassword);
    return {
      statusCode: HttpStatus.OK,
      message: '密码修改成功'
    };
  }

  // 退出登录 (POST /api/user/logout)
  // 实际实现：客户端删除 JWT token 即可，后端通常不需要特别的 /logout 接口
  // @Post('logout')
  // async logout() {
  //   return { message: '退出登录成功' };
  // }
}
```

**7. 定义 DTO (Data Transfer Objects):**

在 `src/user/dto` 目录下创建以下文件，用于验证请求体的结构和数据类型。

```typescript
// src/user/dto/create-user.dto.ts
import { IsNotEmpty, IsString, MinLength, IsOptional, IsEmail, IsMobilePhone } from 'class-validator';

export class CreateUserDto {
  @IsNotEmpty({ message: '用户名不能为空' })
  @IsString({ message: '用户名必须是字符串' })
  username: string;

  @IsNotEmpty({ message: '手机号不能为空' })
  // @IsMobilePhone('zh-CN', { message: '手机号格式不正确' }) // 针对中国大陆手机号
  // 更通用的手机号验证，或者根据具体需求调整
  @IsString({ message: '手机号必须是字符串' }) // 简单示例，后续可加强正则
  phone: string;

  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码必须是字符串' })
  @MinLength(6, { message: '密码至少包含6个字符' })
  password: string;

  @IsOptional()
  @IsEmail({}, { message: '邮箱格式不正确' })
  email?: string;
}
```

```typescript
// src/user/dto/login-user.dto.ts
import { IsNotEmpty, IsString, IsMobilePhone } from 'class-validator';

export class LoginUserDto {
  @IsNotEmpty({ message: '手机号不能为空' })
  // @IsMobilePhone('zh-CN', { message: '手机号格式不正确' }) // 针对中国大陆手机号
  @IsString({ message: '手机号必须是字符串' }) // 简单示例，后续可加强正则
  phone: string;

  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码必须是字符串' })
  password: string;
}
```

```typescript
// src/user/dto/update-user.dto.ts
import { IsString, IsOptional, IsEmail, IsUrl } from 'class-validator';

export class UpdateUserDto {
  @IsOptional()
  @IsString({ message: '用户名必须是字符串' })
  username?: string;

  @IsOptional()
  @IsEmail({}, { message: '邮箱格式不正确' })
  email?: string;

  @IsOptional()
  @IsUrl({}, { message: '头像URL格式不正确' }) // 验证是否为有效的URL
  avatar?: string;

  // ... 其他用户可更新的字段可以添加在这里
}
```

```typescript
// src/user/dto/add-address.dto.ts
import { IsNotEmpty, IsString, IsOptional, IsBoolean, IsMobilePhone } from 'class-validator';

export class AddAddressDto {
  @IsNotEmpty({ message: '收货人姓名不能为空' })
  @IsString()
  consignee: string;

  @IsNotEmpty({ message: '手机号不能为空' })
  // @IsMobilePhone('zh-CN', { message: '手机号格式不正确' })
  @IsString({ message: '手机号必须是字符串' })
  phone: string;

  @IsNotEmpty({ message: '详细地址不能为空' })
  @IsString()
  detailAddress: string;

  @IsOptional()
  @IsString()
  province?: string;

  @IsOptional()
  @IsString()
  city?: string;

  @IsOptional()
  @IsString()
  district?: string;

  @IsOptional()
  @IsString()
  tag?: string; // 例如：家，公司

  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;
}
```

```typescript
// src/user/dto/update-password.dto.ts
import { IsNotEmpty, IsString, MinLength } from 'class-validator';

export class UpdatePasswordDto {
  @IsNotEmpty({ message: '旧密码不能为空' })
  @IsString({ message: '旧密码必须是字符串' })
  oldPassword: string;

  @IsNotEmpty({ message: '新密码不能为空' })
  @IsString({ message: '新密码必须是字符串' })
  @MinLength(6, { message: '新密码至少包含6个字符' })
  newPassword: string;
}
```

**8. 启用全局管道进行DTO验证：**

在 `src/main.ts` 中，添加验证管道，以便DTO中的验证规则生效。

```typescript
// src/main.ts
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common'; // 导入ValidationPipe

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  // 启用全局验证管道，自动根据DTO中的装饰器进行请求体验证
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true, // 移除DTO中未定义的属性
    forbidNonWhitelisted: true, // 如果存在未定义属性，则抛出错误
    transform: true, // 自动转换请求体为DTO实例
  }));
  await app.listen(3000);
}
bootstrap();
```

安装必要的验证库：
`npm install class-validator class-transformer`

### **2.3 测试API**

在完成上述所有代码编写后，保存所有文件。

**1. 启动后端服务：**

在项目根目录（`home-repair-backend`）下，运行：

```bash
npm run start:dev
```

这将启动NestJS应用，并且由于 `synchronize: true`，TypeORM 会自动在您的本地 MySQL 数据库中创建所有定义的表。您可以使用 MySQL Workbench 或 DBeaver 连接到 `home_repair_db` 数据库，查看是否成功生成了 `users`, `technicians` 等表。

**2. 使用 Postman、Insomnia 或类似的工具测试您的API：**

  * **确保您的 MySQL 服务器正在运行。**
  * **确保 NestJS 应用已成功启动。**

**测试用例：**

1.  **用户注册 (POST `http://localhost:3000/api/user/register`)**

      * **Header:** `Content-Type: application/json`
      * **Body (JSON):**
        ```json
        {
            "username": "测试用户001",
            "phone": "13812345678",
            "password": "password123",
            "email": "<EMAIL>"
        }
        ```
      * **预期响应:** `statusCode: 201`, `message: "注册成功"`, `data` 包含用户信息 (不含密码)。
      * **注意：** 记住 `data` 中返回的 `userId`，后续测试 `GET /info` 等接口时需要用到。

2.  **用户登录 (POST `http://localhost:3000/api/user/login`)**

      * **Header:** `Content-Type: application/json`
      * **Body (JSON):**
        ```json
        {
            "phone": "13812345678",
            "password": "password123"
        }
        ```
      * **预期响应:** `statusCode: 200`, `message: "登录成功"`, `success: true`, `data` 包含用户信息 (不含密码)。

3.  **获取用户信息 (GET `http://localhost:3000/api/user/info`)**

      * **Header:** `Content-Type: application/json`
      * **Body:** (空)
      * **重要:** 在 `src/user/user.controller.ts` 中，我们暂时硬编码了 `userId = 'U1716616000000_example'`。请将此值替换为您在注册成功后实际获得的 `userId`，或者一个已存在于您数据库中的用户ID，否则会报 `404 Not Found`。
      * **预期响应:** `statusCode: 200`, `data` 包含用户的详细信息 (不含密码)。

4.  **更新用户信息 (PUT `http://localhost:3000/api/user/info`)**

      * **Header:** `Content-Type: application/json`
      * **Body (JSON):**
        ```json
        {
            "username": "测试用户新名称",
            "avatar": "https://example.com/new_avatar.jpg"
        }
        ```
      * **重要:** 同样需要将 `user.controller.ts` 中的硬编码 `userId` 替换为您要更新的实际用户ID。
      * **预期响应:** `statusCode: 200`, `message: "更新成功"`, `data` 包含更新后的用户信息。

5.  **添加地址 (POST `http://localhost:3000/api/user/address`)**

      * **Header:** `Content-Type: application/json`
      * **Body (JSON):**
        ```json
        {
            "consignee": "张三",
            "phone": "13812345678",
            "detailAddress": "北京市海淀区中关村软件园",
            "province": "北京市",
            "city": "北京市",
            "district": "海淀区",
            "tag": "公司",
            "isDefault": true
        }
        ```
      * **重要:** 同样需要将 `user.controller.ts` 中的硬编码 `userId` 替换为您要添加地址的实际用户ID。
      * **预期响应:** `statusCode: 201`, `message: "地址添加成功"`, `data` 包含用户更新后的地址列表。

6.  **获取地址列表 (GET `http://localhost:3000/api/user/addresses`)**

      * **Header:** `Content-Type: application/json`
      * **Body:** (空)
      * **重要:** 同样需要将 `user.controller.ts` 中的硬编码 `userId` 替换为您要获取地址列表的实际用户ID。
      * **预期响应:** `statusCode: 200`, `data` 包含用户的地址数组。

7.  **修改密码 (PUT `http://localhost:3000/api/user/password`)**

      * **Header:** `Content-Type: application/json`
      * **Body (JSON):**
        ```json
        {
            "oldPassword": "password123",  // 替换为该用户的当前密码
            "newPassword": "new_password_456"
        }
        ```
      * **重要:** 同样需要将 `user.controller.ts` 中的硬编码 `userId` 替换为您要修改密码的实际用户ID。
      * **预期响应:** `statusCode: 200`, `message: "密码修改成功"`, 无 `data`。

**第二篇总结：**

您已成功完成：

  * 将设计文档中的数据模型转换为 TypeORM 实体，并为所有核心实体创建了对应的 TypeScript 类。
  * 配置了 `AppModule`，使 TypeORM 能够在应用启动时自动创建这些数据库表。
  * 构建了用户管理模块 (`UserModule`) 的基础架构。
  * 实现了用户注册、登录（初步）、信息获取与更新、地址管理和密码修改等核心API。
  * 引入了 DTO 进行请求体验证，并启用了全局验证管道。

这些构成了我们家维在线系统后端服务的核心骨架。

-----

**[请告诉我“继续”，我们就可以进入第三篇：安全与认证——JWT与RBAC实现，来为我们的API加上认证和授权的翅膀了。]**