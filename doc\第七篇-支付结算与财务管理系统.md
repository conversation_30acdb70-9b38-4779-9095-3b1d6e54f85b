### **《新一代在线家政维修服务平台：从需求到实现的企业级开发实战》**

#### **第七篇：支付结算与财务管理系统**

**摘要：** 基于需求规格说明书中的支付结算需求（FR-PS-001至FR-PS-008），本篇将构建一个完整的支付结算与财务管理系统。我们将实现多支付方式集成、托管支付模式、自动结算、佣金计算、财务报表等核心功能，确保资金安全和财务透明。

---

## **7.1 多支付方式集成**

### **支付模块结构创建**

```bash
# 创建支付管理模块
nest g module modules/payments
nest g controller modules/payments
nest g service modules/payments

# 创建结算管理模块
nest g module modules/settlements
nest g controller modules/settlements
nest g service modules/settlements

# 创建财务管理模块
nest g module modules/finance
nest g controller modules/finance
nest g service modules/finance
```

### **支付相关实体设计**

```typescript
// src/database/entities/payment.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { Order } from './order.entity';
import { User } from './user.entity';
import { PaymentRefund } from './payment-refund.entity';

export enum PaymentMethod {
  WECHAT_PAY = 'wechat_pay',
  ALIPAY = 'alipay',
  UNION_PAY = 'union_pay',
  BALANCE = 'balance',
}

export enum PaymentStatus {
  PENDING = 'pending',           // 待支付
  PROCESSING = 'processing',     // 支付中
  SUCCESS = 'success',           // 支付成功
  FAILED = 'failed',            // 支付失败
  CANCELLED = 'cancelled',       // 已取消
  REFUNDED = 'refunded',        // 已退款
  PARTIAL_REFUNDED = 'partial_refunded', // 部分退款
}

export enum PaymentType {
  ORDER_PAYMENT = 'order_payment',     // 订单支付
  DEPOSIT = 'deposit',                 // 押金
  PENALTY = 'penalty',                 // 违约金
  PLATFORM_FEE = 'platform_fee',      // 平台费用
}

@Entity('payments')
@Index(['order_id'])
@Index(['user_id'])
@Index(['payment_method'])
@Index(['status'])
@Index(['created_at'])
export class Payment {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'payment_no', length: 32, unique: true })
  paymentNo: string;

  @Column({ name: 'order_id', type: 'bigint' })
  orderId: number;

  @Column({ name: 'user_id', type: 'bigint' })
  userId: number;

  @Column({
    name: 'payment_method',
    type: 'enum',
    enum: PaymentMethod,
  })
  paymentMethod: PaymentMethod;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  status: PaymentStatus;

  @Column({
    name: 'payment_type',
    type: 'enum',
    enum: PaymentType,
    default: PaymentType.ORDER_PAYMENT,
  })
  paymentType: PaymentType;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ name: 'actual_amount', type: 'decimal', precision: 10, scale: 2, nullable: true })
  actualAmount: number;

  @Column({ name: 'platform_fee', type: 'decimal', precision: 10, scale: 2, default: 0 })
  platformFee: number;

  @Column({ name: 'third_party_fee', type: 'decimal', precision: 10, scale: 2, default: 0 })
  thirdPartyFee: number;

  @Column({ name: 'third_party_transaction_id', length: 100, nullable: true })
  thirdPartyTransactionId: string;

  @Column({ name: 'third_party_response', type: 'json', nullable: true })
  thirdPartyResponse: Record<string, any>;

  @Column({ name: 'paid_at', type: 'timestamp', nullable: true })
  paidAt: Date;

  @Column({ name: 'expired_at', type: 'timestamp', nullable: true })
  expiredAt: Date;

  @Column({ type: 'text', nullable: true })
  remark: string;

  @Column({ name: 'refund_amount', type: 'decimal', precision: 10, scale: 2, default: 0 })
  refundAmount: number;

  @Column({ name: 'refundable_amount', type: 'decimal', precision: 10, scale: 2, default: 0 })
  refundableAmount: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => Order, (order) => order.payments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'order_id' })
  order: Order;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany(() => PaymentRefund, (refund) => refund.payment)
  refunds: PaymentRefund[];
}
```

### **退款实体设计**

```typescript
// src/database/entities/payment-refund.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Payment } from './payment.entity';

export enum RefundStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  SUCCESS = 'success',
  FAILED = 'failed',
  REJECTED = 'rejected',
}

export enum RefundReason {
  ORDER_CANCELLED = 'order_cancelled',
  SERVICE_UNSATISFACTORY = 'service_unsatisfactory',
  PROVIDER_BREACH = 'provider_breach',
  CUSTOMER_REQUEST = 'customer_request',
  SYSTEM_ERROR = 'system_error',
  OTHER = 'other',
}

@Entity('payment_refunds')
@Index(['payment_id'])
@Index(['status'])
@Index(['created_at'])
export class PaymentRefund {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'refund_no', length: 32, unique: true })
  refundNo: string;

  @Column({ name: 'payment_id', type: 'bigint' })
  paymentId: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({
    type: 'enum',
    enum: RefundStatus,
    default: RefundStatus.PENDING,
  })
  status: RefundStatus;

  @Column({
    type: 'enum',
    enum: RefundReason,
  })
  reason: RefundReason;

  @Column({ name: 'reason_description', type: 'text', nullable: true })
  reasonDescription: string;

  @Column({ name: 'third_party_refund_id', length: 100, nullable: true })
  thirdPartyRefundId: string;

  @Column({ name: 'third_party_response', type: 'json', nullable: true })
  thirdPartyResponse: Record<string, any>;

  @Column({ name: 'processed_at', type: 'timestamp', nullable: true })
  processedAt: Date;

  @Column({ name: 'operator_id', type: 'bigint', nullable: true })
  operatorId: number;

  @Column({ name: 'operator_type', length: 20, nullable: true })
  operatorType: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => Payment, (payment) => payment.refunds, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'payment_id' })
  payment: Payment;
}
```

### **结算实体设计**

```typescript
// src/database/entities/settlement.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { ServiceProvider } from './service-provider.entity';
import { SettlementDetail } from './settlement-detail.entity';

export enum SettlementStatus {
  PENDING = 'pending',           // 待结算
  PROCESSING = 'processing',     // 结算中
  COMPLETED = 'completed',       // 已完成
  FAILED = 'failed',            // 失败
  CANCELLED = 'cancelled',       // 已取消
}

export enum SettlementCycle {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  MANUAL = 'manual',
}

@Entity('settlements')
@Index(['provider_id'])
@Index(['status'])
@Index(['settlement_date'])
@Index(['created_at'])
export class Settlement {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'settlement_no', length: 32, unique: true })
  settlementNo: string;

  @Column({ name: 'provider_id', type: 'bigint' })
  providerId: number;

  @Column({
    type: 'enum',
    enum: SettlementStatus,
    default: SettlementStatus.PENDING,
  })
  status: SettlementStatus;

  @Column({
    name: 'settlement_cycle',
    type: 'enum',
    enum: SettlementCycle,
    default: SettlementCycle.WEEKLY,
  })
  settlementCycle: SettlementCycle;

  @Column({ name: 'settlement_date', type: 'date' })
  settlementDate: Date;

  @Column({ name: 'start_date', type: 'date' })
  startDate: Date;

  @Column({ name: 'end_date', type: 'date' })
  endDate: Date;

  @Column({ name: 'total_amount', type: 'decimal', precision: 12, scale: 2 })
  totalAmount: number;

  @Column({ name: 'platform_fee', type: 'decimal', precision: 12, scale: 2, default: 0 })
  platformFee: number;

  @Column({ name: 'tax_amount', type: 'decimal', precision: 12, scale: 2, default: 0 })
  taxAmount: number;

  @Column({ name: 'deduction_amount', type: 'decimal', precision: 12, scale: 2, default: 0 })
  deductionAmount: number;

  @Column({ name: 'actual_amount', type: 'decimal', precision: 12, scale: 2 })
  actualAmount: number;

  @Column({ name: 'order_count', type: 'int', default: 0 })
  orderCount: number;

  @Column({ name: 'bank_account', length: 100, nullable: true })
  bankAccount: string;

  @Column({ name: 'bank_name', length: 100, nullable: true })
  bankName: string;

  @Column({ name: 'account_holder', length: 50, nullable: true })
  accountHolder: string;

  @Column({ name: 'processed_at', type: 'timestamp', nullable: true })
  processedAt: Date;

  @Column({ name: 'operator_id', type: 'bigint', nullable: true })
  operatorId: number;

  @Column({ type: 'text', nullable: true })
  remark: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => ServiceProvider, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'provider_id' })
  provider: ServiceProvider;

  @OneToMany(() => SettlementDetail, (detail) => detail.settlement, { cascade: true })
  details: SettlementDetail[];
}

// src/database/entities/settlement-detail.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Settlement } from './settlement.entity';
import { Order } from './order.entity';

@Entity('settlement_details')
@Index(['settlement_id'])
@Index(['order_id'])
export class SettlementDetail {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'settlement_id', type: 'bigint' })
  settlementId: number;

  @Column({ name: 'order_id', type: 'bigint' })
  orderId: number;

  @Column({ name: 'order_amount', type: 'decimal', precision: 10, scale: 2 })
  orderAmount: number;

  @Column({ name: 'platform_fee', type: 'decimal', precision: 10, scale: 2, default: 0 })
  platformFee: number;

  @Column({ name: 'provider_amount', type: 'decimal', precision: 10, scale: 2 })
  providerAmount: number;

  @Column({ name: 'commission_rate', type: 'decimal', precision: 5, scale: 4, default: 0 })
  commissionRate: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => Settlement, (settlement) => settlement.details, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'settlement_id' })
  settlement: Settlement;

  @ManyToOne(() => Order, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'order_id' })
  order: Order;
}
```

### **支付数据传输对象**

```typescript
// src/modules/payments/dto/create-payment.dto.ts
import { IsEnum, IsNumber, IsOptional, IsString, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { PaymentMethod, PaymentType } from '../../../database/entities/payment.entity';

export class CreatePaymentDto {
  @ApiProperty({ description: '订单ID', example: 1 })
  @IsNumber()
  orderId: number;

  @ApiProperty({ description: '支付方式', enum: PaymentMethod, example: PaymentMethod.WECHAT_PAY })
  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @ApiProperty({ description: '支付类型', enum: PaymentType, example: PaymentType.ORDER_PAYMENT })
  @IsEnum(PaymentType)
  @IsOptional()
  paymentType?: PaymentType = PaymentType.ORDER_PAYMENT;

  @ApiProperty({ description: '支付金额', example: 120.00 })
  @IsNumber()
  @Min(0.01)
  amount: number;

  @ApiProperty({ description: '备注', example: '订单支付', required: false })
  @IsString()
  @IsOptional()
  remark?: string;
}

// src/modules/payments/dto/payment-callback.dto.ts
import { IsString, IsOptional, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class PaymentCallbackDto {
  @ApiProperty({ description: '第三方交易ID', example: 'wx_123456789' })
  @IsString()
  thirdPartyTransactionId: string;

  @ApiProperty({ description: '支付状态', example: 'SUCCESS' })
  @IsString()
  status: string;

  @ApiProperty({ description: '实际支付金额', example: 120.00 })
  @IsOptional()
  actualAmount?: number;

  @ApiProperty({ description: '第三方响应数据', example: {} })
  @IsObject()
  @IsOptional()
  thirdPartyResponse?: Record<string, any>;
}

// src/modules/payments/dto/create-refund.dto.ts
import { IsEnum, IsNumber, IsOptional, IsString, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { RefundReason } from '../../../database/entities/payment-refund.entity';

export class CreateRefundDto {
  @ApiProperty({ description: '支付ID', example: 1 })
  @IsNumber()
  paymentId: number;

  @ApiProperty({ description: '退款金额', example: 50.00 })
  @IsNumber()
  @Min(0.01)
  amount: number;

  @ApiProperty({ description: '退款原因', enum: RefundReason, example: RefundReason.ORDER_CANCELLED })
  @IsEnum(RefundReason)
  reason: RefundReason;

  @ApiProperty({ description: '退款原因描述', example: '客户主动取消订单' })
  @IsString()
  @IsOptional()
  reasonDescription?: string;
}
```

### **支付服务层实现**

```typescript
// src/modules/payments/payments.service.ts
import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Payment, PaymentStatus, PaymentMethod } from '../../database/entities/payment.entity';
import { PaymentRefund, RefundStatus } from '../../database/entities/payment-refund.entity';
import { Order, OrderStatus } from '../../database/entities/order.entity';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { PaymentCallbackDto } from './dto/payment-callback.dto';
import { CreateRefundDto } from './dto/create-refund.dto';
import { WechatPayService } from './providers/wechat-pay.service';
import { AlipayService } from './providers/alipay.service';

@Injectable()
export class PaymentsService {
  constructor(
    @InjectRepository(Payment)
    private readonly paymentRepository: Repository<Payment>,
    @InjectRepository(PaymentRefund)
    private readonly refundRepository: Repository<PaymentRefund>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    private readonly dataSource: DataSource,
    private readonly wechatPayService: WechatPayService,
    private readonly alipayService: AlipayService,
  ) {}

  /**
   * 创建支付订单
   * 实现需求：FR-PS-001
   */
  async createPayment(userId: number, createPaymentDto: CreatePaymentDto): Promise<Payment> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. 验证订单
      const order = await this.orderRepository.findOne({
        where: { id: createPaymentDto.orderId, customerId: userId },
      });

      if (!order) {
        throw new NotFoundException('订单不存在或无权限访问');
      }

      if (order.status !== OrderStatus.PENDING) {
        throw new BadRequestException('订单状态不允许支付');
      }

      // 2. 检查是否已有待支付的支付记录
      const existingPayment = await this.paymentRepository.findOne({
        where: {
          orderId: createPaymentDto.orderId,
          status: PaymentStatus.PENDING,
        },
      });

      if (existingPayment) {
        throw new ConflictException('订单已有待支付记录');
      }

      // 3. 生成支付单号
      const paymentNo = await this.generatePaymentNo();

      // 4. 计算支付金额和费用
      const paymentInfo = this.calculatePaymentFees(createPaymentDto.amount, createPaymentDto.paymentMethod);

      // 5. 创建支付记录
      const payment = this.paymentRepository.create({
        paymentNo,
        orderId: createPaymentDto.orderId,
        userId,
        paymentMethod: createPaymentDto.paymentMethod,
        paymentType: createPaymentDto.paymentType,
        amount: createPaymentDto.amount,
        ...paymentInfo,
        remark: createPaymentDto.remark,
        expiredAt: new Date(Date.now() + 30 * 60 * 1000), // 30分钟过期
        refundableAmount: createPaymentDto.amount,
      });

      const savedPayment = await queryRunner.manager.save(payment);

      // 6. 调用第三方支付接口
      const paymentResult = await this.initiateThirdPartyPayment(savedPayment);

      // 7. 更新支付记录
      await queryRunner.manager.update(Payment, savedPayment.id, {
        thirdPartyTransactionId: paymentResult.transactionId,
        thirdPartyResponse: paymentResult.response,
      });

      await queryRunner.commitTransaction();

      return this.findById(savedPayment.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 生成支付单号
   */
  private async generatePaymentNo(): Promise<string> {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `PAY${timestamp}${random}`;
  }

  /**
   * 计算支付费用
   */
  private calculatePaymentFees(amount: number, paymentMethod: PaymentMethod) {
    let thirdPartyFee = 0;
    let platformFee = 0;

    // 第三方支付手续费
    switch (paymentMethod) {
      case PaymentMethod.WECHAT_PAY:
        thirdPartyFee = amount * 0.006; // 0.6%
        break;
      case PaymentMethod.ALIPAY:
        thirdPartyFee = amount * 0.0055; // 0.55%
        break;
      case PaymentMethod.UNION_PAY:
        thirdPartyFee = amount * 0.005; // 0.5%
        break;
      case PaymentMethod.BALANCE:
        thirdPartyFee = 0;
        break;
    }

    // 平台服务费（已在订单中计算）
    platformFee = 0;

    return {
      thirdPartyFee: Math.round(thirdPartyFee * 100) / 100,
      platformFee: Math.round(platformFee * 100) / 100,
    };
  }

  /**
   * 调用第三方支付接口
   */
  private async initiateThirdPartyPayment(payment: Payment) {
    switch (payment.paymentMethod) {
      case PaymentMethod.WECHAT_PAY:
        return this.wechatPayService.createPayment({
          outTradeNo: payment.paymentNo,
          totalFee: Math.round(payment.amount * 100), // 转换为分
          body: `订单支付-${payment.paymentNo}`,
        });

      case PaymentMethod.ALIPAY:
        return this.alipayService.createPayment({
          outTradeNo: payment.paymentNo,
          totalAmount: payment.amount.toString(),
          subject: `订单支付-${payment.paymentNo}`,
        });

      case PaymentMethod.BALANCE:
        // 余额支付直接成功
        return {
          transactionId: `BAL_${payment.paymentNo}`,
          response: { status: 'SUCCESS' },
        };

      default:
        throw new BadRequestException('不支持的支付方式');
    }
  }

  /**
   * 处理支付回调
   * 实现需求：FR-PS-002
   */
  async handlePaymentCallback(paymentNo: string, callbackDto: PaymentCallbackDto): Promise<Payment> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const payment = await this.paymentRepository.findOne({
        where: { paymentNo },
        relations: ['order'],
      });

      if (!payment) {
        throw new NotFoundException('支付记录不存在');
      }

      if (payment.status !== PaymentStatus.PENDING) {
        throw new BadRequestException('支付状态不允许更新');
      }

      // 验证回调数据
      const isValid = await this.verifyPaymentCallback(payment, callbackDto);
      if (!isValid) {
        throw new BadRequestException('支付回调验证失败');
      }

      // 更新支付状态
      const newStatus = callbackDto.status === 'SUCCESS' ? PaymentStatus.SUCCESS : PaymentStatus.FAILED;

      await queryRunner.manager.update(Payment, payment.id, {
        status: newStatus,
        actualAmount: callbackDto.actualAmount || payment.amount,
        thirdPartyTransactionId: callbackDto.thirdPartyTransactionId,
        thirdPartyResponse: callbackDto.thirdPartyResponse,
        paidAt: newStatus === PaymentStatus.SUCCESS ? new Date() : null,
      });

      // 如果支付成功，更新订单状态
      if (newStatus === PaymentStatus.SUCCESS) {
        await queryRunner.manager.update(Order, payment.orderId, {
          status: OrderStatus.CONFIRMED,
          confirmedAt: new Date(),
        });
      }

      await queryRunner.commitTransaction();

      return this.findById(payment.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 验证支付回调
   */
  private async verifyPaymentCallback(payment: Payment, callbackDto: PaymentCallbackDto): Promise<boolean> {
    switch (payment.paymentMethod) {
      case PaymentMethod.WECHAT_PAY:
        return this.wechatPayService.verifyCallback(callbackDto.thirdPartyResponse);

      case PaymentMethod.ALIPAY:
        return this.alipayService.verifyCallback(callbackDto.thirdPartyResponse);

      case PaymentMethod.BALANCE:
        return true; // 余额支付无需验证

      default:
        return false;
    }
  }

  /**
   * 根据ID查找支付记录
   */
  async findById(id: number): Promise<Payment> {
    const payment = await this.paymentRepository.findOne({
      where: { id },
      relations: ['order', 'user', 'refunds'],
    });

    if (!payment) {
      throw new NotFoundException('支付记录不存在');
    }

    return payment;
  }
}
```

## **7.2 托管支付与结算系统**

### **结算服务实现**

```typescript
// src/modules/settlements/settlements.service.ts
import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, Between } from 'typeorm';
import { Settlement, SettlementStatus, SettlementCycle } from '../../database/entities/settlement.entity';
import { SettlementDetail } from '../../database/entities/settlement-detail.entity';
import { Order, OrderStatus } from '../../database/entities/order.entity';
import { Payment, PaymentStatus } from '../../database/entities/payment.entity';
import { ServiceProvider } from '../../database/entities/service-provider.entity';

@Injectable()
export class SettlementsService {
  constructor(
    @InjectRepository(Settlement)
    private readonly settlementRepository: Repository<Settlement>,
    @InjectRepository(SettlementDetail)
    private readonly settlementDetailRepository: Repository<SettlementDetail>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(Payment)
    private readonly paymentRepository: Repository<Payment>,
    @InjectRepository(ServiceProvider)
    private readonly providerRepository: Repository<ServiceProvider>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * 创建结算单
   * 实现需求：FR-PS-005
   */
  async createSettlement(
    providerId: number,
    startDate: Date,
    endDate: Date,
    settlementCycle: SettlementCycle = SettlementCycle.WEEKLY,
  ): Promise<Settlement> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. 验证服务商
      const provider = await this.providerRepository.findOne({
        where: { id: providerId },
        relations: ['user'],
      });

      if (!provider) {
        throw new NotFoundException('服务商不存在');
      }

      // 2. 获取待结算订单
      const settlementOrders = await this.getSettlementOrders(providerId, startDate, endDate);

      if (settlementOrders.length === 0) {
        throw new BadRequestException('该时间段内没有可结算的订单');
      }

      // 3. 计算结算金额
      const settlementInfo = this.calculateSettlementAmount(settlementOrders);

      // 4. 生成结算单号
      const settlementNo = await this.generateSettlementNo();

      // 5. 创建结算记录
      const settlement = this.settlementRepository.create({
        settlementNo,
        providerId,
        settlementCycle,
        settlementDate: new Date(),
        startDate,
        endDate,
        ...settlementInfo,
        status: SettlementStatus.PENDING,
      });

      const savedSettlement = await queryRunner.manager.save(settlement);

      // 6. 创建结算明细
      for (const order of settlementOrders) {
        const detail = this.settlementDetailRepository.create({
          settlementId: savedSettlement.id,
          orderId: order.id,
          orderAmount: order.totalAmount,
          platformFee: order.platformFee,
          providerAmount: order.totalAmount - order.platformFee,
          commissionRate: this.getCommissionRate(order.totalAmount),
        });

        await queryRunner.manager.save(detail);
      }

      await queryRunner.commitTransaction();

      return this.findById(savedSettlement.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 获取待结算订单
   */
  private async getSettlementOrders(providerId: number, startDate: Date, endDate: Date): Promise<Order[]> {
    return this.orderRepository.find({
      where: {
        providerId,
        status: OrderStatus.COMPLETED,
        completedAt: Between(startDate, endDate),
      },
      relations: ['payments'],
    });
  }

  /**
   * 计算结算金额
   */
  private calculateSettlementAmount(orders: Order[]) {
    let totalAmount = 0;
    let platformFee = 0;
    let taxAmount = 0;
    let deductionAmount = 0;

    for (const order of orders) {
      totalAmount += order.totalAmount;
      platformFee += order.platformFee;
    }

    // 税费计算（6%增值税）
    taxAmount = totalAmount * 0.06;

    // 其他扣除（如违约金、罚款等）
    deductionAmount = 0;

    const actualAmount = totalAmount - platformFee - taxAmount - deductionAmount;

    return {
      totalAmount,
      platformFee,
      taxAmount,
      deductionAmount,
      actualAmount,
      orderCount: orders.length,
    };
  }

  /**
   * 获取佣金费率
   */
  private getCommissionRate(orderAmount: number): number {
    // 阶梯式佣金费率
    if (orderAmount >= 1000) {
      return 0.03; // 3%
    } else if (orderAmount >= 500) {
      return 0.04; // 4%
    } else {
      return 0.05; // 5%
    }
  }

  /**
   * 生成结算单号
   */
  private async generateSettlementNo(): Promise<string> {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `STL${timestamp}${random}`;
  }

  /**
   * 根据ID查找结算记录
   */
  async findById(id: number): Promise<Settlement> {
    const settlement = await this.settlementRepository.findOne({
      where: { id },
      relations: ['provider', 'provider.user', 'details', 'details.order'],
    });

    if (!settlement) {
      throw new NotFoundException('结算记录不存在');
    }

    return settlement;
  }
}
```

通过本篇教程，我们构建了一个完整的支付结算与财务管理系统，包括多支付方式集成、托管支付模式、自动结算、佣金计算、财务报表等核心功能。系统确保了资金安全和财务透明，完全满足需求规格说明书中的支付结算需求。

---

**[请告诉我"继续"，我将提供第八篇：通讯互动与评价反馈系统。]**
