好的，我们进入前端部分的**第六篇：管理员后台界面实现**。

在本篇中，我们将为管理员角色构建一套全面的后台管理界面，涵盖数据概览、用户/技师管理、服务/配件管理、订单审核与派单、财务审核以及消息公告管理等核心功能。这将是平台运营管理的关键部分。

---

## 第六篇：管理员后台界面实现

### 6.0 管理员布局和权限控制

在开始具体页面实现之前，我们需要确保管理员角色能够正确访问后台界面，并拥有一个独立的导航布局。

#### 6.0.1 权限路由 (`PrivateRoute` 增强)

我们之前创建的 `PrivateRoute` 组件可以增强以支持不同角色的访问权限。

```tsx
// src/components/PrivateRoute.tsx (增强版)
import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useUser } from '../contexts/UserContext'; // 假设你有 UserContext 来存储用户信息

interface PrivateRouteProps {
  allowedRoles?: string[]; // 允许访问的角色列表
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({ allowedRoles }) => {
  const { user, loading } = useUser(); // 获取当前用户和加载状态

  if (loading) {
    return <div>Loading authentication...</div>; // 或一个 Spin 组件
  }

  if (!user) {
    // 用户未登录，重定向到登录页
    return <Navigate to="/login" replace />;
  }

  if (allowedRoles && !allowedRoles.includes(user.role)) {
    // 用户角色不在允许列表中，重定向到无权限页面或仪表盘
    return <Navigate to="/unauthorized" replace />; // 或到 /dashboard
  }

  // 认证成功且权限符合，渲染子路由
  return <Outlet />;
};

export default PrivateRoute;
```

#### 6.0.2 管理员专用布局 (`src/layouts/AdminLayout.tsx`)

为了让管理员后台有独立的导航和侧边栏，我们可以创建一个专门的布局组件。

```tsx
// src/layouts/AdminLayout.tsx
import React, { useEffect } from 'react';
import { Layout, Menu, theme, message } from 'antd';
import { UserOutlined, DashboardOutlined, ToolOutlined, FileTextOutlined, DollarCircleOutlined, NotificationOutlined } from '@ant-design/icons';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useUser } from '../contexts/UserContext';

const { Header, Content, Footer, Sider } = Layout;

const AdminLayout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useUser();
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  // 确保只有管理员能访问此布局
  useEffect(() => {
    if (user && user.role !== 'admin') {
      message.error('您没有权限访问管理员后台！');
      navigate('/dashboard');
    }
  }, [user, navigate]);


  const menuItems = [
    {
      key: '/admin/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表盘',
    },
    {
      key: '/admin/users',
      icon: <UserOutlined />,
      label: '用户管理',
    },
    {
      key: '/admin/services',
      icon: <ToolOutlined />,
      label: '服务与配件',
    },
    {
      key: '/admin/orders',
      icon: <FileTextOutlined />,
      label: '订单管理',
    },
    {
      key: '/admin/finance',
      icon: <DollarCircleOutlined />,
      label: '财务管理',
    },
    {
      key: '/admin/announcements',
      icon: <NotificationOutlined />,
      label: '消息公告',
    },
  ];

  const selectedKey = menuItems.find(item => location.pathname.startsWith(item.key))?.key || '/admin/dashboard';


  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        breakpoint="lg"
        collapsedWidth="0"
        onBreakpoint={(broken) => {
          console.log(broken);
        }}
        onCollapse={(collapsed, type) => {
          console.log(collapsed, type);
        }}
      >
        <div className="demo-logo-vertical" style={{ height: 32, margin: 16, background: 'rgba(255, 255, 255, 0.2)', borderRadius: 8, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <span style={{ color: '#fff', fontSize: 18, fontWeight: 'bold' }}>管理后台</span>
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[selectedKey]}
          onClick={({ key }) => navigate(key)}
          items={menuItems}
        />
      </Sider>
      <Layout>
        <Header style={{ padding: 0, background: colorBgContainer }}>
          <div style={{ float: 'right', marginRight: 24 }}>
            <span style={{ marginRight: 10 }}>欢迎，管理员 {user?.username}</span>
            {/* 可以在这里添加登出按钮等 */}
          </div>
        </Header>
        <Content style={{ margin: '24px 16px 0' }}>
          <div
            style={{
              padding: 24,
              minHeight: 360,
              background: colorBgContainer,
              borderRadius: borderRadiusLG,
            }}
          >
            <Outlet /> {/* 渲染子路由内容 */}
          </div>
        </Content>
        <Footer style={{ textAlign: 'center' }}>
          On-Demand Service Platform ©2024 Created by Your Company
        </Footer>
      </Layout>
    </Layout>
  );
};

export default AdminLayout;
```

**在 `App.tsx` 中配置管理员路由：**

```tsx
// src/App.tsx (部分)
import AdminLayout from './layouts/AdminLayout'; // 引入管理员布局
import AdminDashboardPage from './pages/admin/AdminDashboardPage'; // 引入仪表盘页面
import AdminUserManagementPage from './pages/admin/AdminUserManagementPage'; // 引入用户管理页面
import AdminServiceManagementPage from './pages/admin/AdminServiceManagementPage'; // 引入服务管理页面
import AdminOrderManagementPage from './pages/admin/AdminOrderManagementPage'; // 引入订单管理页面
import AdminFinanceManagementPage from './pages/admin/AdminFinanceManagementPage'; // 引入财务管理页面
import AdminAnnouncementManagementPage from './pages/admin/AdminAnnouncementManagementPage'; // 引入消息公告管理页面

function App() {
  return (
    <Router>
      <Layout className="layout">
        {/* ... Header, Content, Footer */}
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              {/* ... 公共路由 */}

              <Route element={<PrivateRoute allowedRoles={['admin']} />}> {/* 只有管理员能访问 */}
                <Route path="/admin" element={<AdminLayout />}> {/* 使用管理员布局 */}
                  <Route index element={<Navigate to="dashboard" replace />} /> {/* 默认跳转到仪表盘 */}
                  <Route path="dashboard" element={<AdminDashboardPage />} />
                  <Route path="users" element={<AdminUserManagementPage />} />
                  <Route path="services" element={<AdminServiceManagementPage />} />
                  <Route path="orders" element={<AdminOrderManagementPage />} />
                  <Route path="finance" element={<AdminFinanceManagementPage />} />
                  <Route path="announcements" element={<AdminAnnouncementManagementPage />} />
                </Route>
              </Route>

              {/* ... 其他受保护的用户/技师路由 */}
              <Route path="/orders" element={<UserOrderListPage />} />
              <Route path="/order/:id" element={<OrderDetailPage />} />
              <Route path="/order/:id/review" element={<OrderReviewPage />} />
              <Route path="/order/:id/after-sales" element={<OrderAfterSalesPage />} />
              <Route path="/notifications" element={<NotificationPage />} />

              <Route path="/technician/dashboard" element={<TechnicianDashboardPage />} />
              <Route path="/technician/tasks" element={<TechnicianOrderListPage />} />
              <Route path="/technician/order/:id" element={<OrderDetailPage />} />
              <Route path="/technician/reviews" element={<TechnicianReviewsPage />} />

              {/* ... 404 路由 */}
            </Routes>
          </div>
        </Content>
        {/* ... Footer */}
      </Layout>
    </Router>
  );
}
```

### 6.1 管理员仪表盘 (Dashboard)

管理员仪表盘是后台的入口，用于展示系统关键数据概览。

#### 6.1.1 仪表盘页面 (`src/pages/admin/AdminDashboardPage.tsx`)

```tsx
// src/pages/admin/AdminDashboardPage.tsx
import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Spin, Divider, Typography } from 'antd';
import { UserOutlined, ToolOutlined, FileTextOutlined, DollarOutlined } from '@ant-design/icons';
import api from '../../services/api';
// import { Chart } from '@ant-design/charts'; // 如果需要图表，需要安装 @ant-design/charts

const { Title, Text } = Typography;

interface DashboardStats {
  totalUsers: number;
  totalTechnicians: number;
  totalOrders: number;
  completedOrders: number;
  pendingOrders: number;
  totalRevenue: number;
  // 可以添加更多统计数据，如：
  // dailyNewUsers: number;
  // monthlyOrders: number;
  // topServices: { name: string; count: number }[];
}

const AdminDashboardPage: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    setLoading(true);
    try {
      // 假设后端提供 /api/admin/dashboard/stats 接口
      const response = await api.get('/admin/dashboard/stats');
      setStats(response.data);
    } catch (error) {
      console.error('获取仪表盘数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading || !stats) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}><Spin size="large" tip="加载仪表盘数据..." /></div>
    );
  }

  // 假设 Ant Design Charts 已经安装并引入
  // const demoChartData = [
  //   { type: '一月', value: 38 },
  //   { type: '二月', value: 52 },
  //   { type: '三月', value: 61 },
  //   { type: '四月', value: 145 },
  //   { type: '五月', value: 48 },
  // ];
  // const config = {
  //   data: demoChartData,
  //   xField: 'type',
  //   yField: 'value',
  //   seriesField: 'type',
  //   label: {
  //     position: 'middle',
  //     style: {
  //       fill: '#FFFFFF',
  //       opacity: 0.6,
  //     },
  //   },
  //   xAxis: {
  //     label: {
  //       autoHide: true,
  //       autoRotate: false,
  //     },
  //   },
  //   meta: {
  //     type: {
  //       alias: '月份',
  //     },
  //     value: {
  //       alias: '订单量',
  //     },
  //   },
  // };

  return (
    <div>
      <Title level={3}>系统概览</Title>
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic title="总用户数" value={stats.totalUsers} prefix={<UserOutlined />} />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic title="总技师数" value={stats.totalTechnicians} prefix={<ToolOutlined />} />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic title="总订单数" value={stats.totalOrders} prefix={<FileTextOutlined />} />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic title="已完成订单" value={stats.completedOrders} prefix={<CheckOutlined />} />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic title="待处理订单" value={stats.pendingOrders} prefix={<ClockCircleOutlined />} />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic title="总营收" value={stats.totalRevenue} precision={2} prefix="¥" />
          </Card>
        </Col>
      </Row>

      <Divider />
      <Title level={4}>数据趋势 (示例，需集成图表库)</Title>
      {/*
        {Chart && ( // 只有在 Chart 存在时才渲染
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Card title="每月订单量">
                <Column {...config} />
              </Card>
            </Col>
            <Col span={12}>
              <Card title="用户增长趋势">
                <Line {...config} />
              </Card>
            </Col>
          </Row>
        )}
      */}
    </div>
  );
};

export default AdminDashboardPage;
```

### 6.2 用户与技师管理

管理员需要管理平台的所有用户和技师账户。

#### 6.2.1 用户与技师管理页面 (`src/pages/admin/AdminUserManagementPage.tsx`)

```tsx
// src/pages/admin/AdminUserManagementPage.tsx
import React, { useState, useEffect } from 'react';
import { Card, Table, Input, Button, Space, Tag, Modal, Form, Select, message, Popconfirm, Avatar, Typography } from 'antd';
import { SearchOutlined, EditOutlined, DeleteOutlined, UserOutlined, ToolOutlined } from '@ant-design/icons';
import api from '../../services/api';

const { Option } = Select;
const { Text } = Typography;

interface User {
  id: number;
  username: string;
  phoneNumber: string;
  email?: string;
  role: 'user' | 'technician' | 'admin';
  status: 'active' | 'inactive' | 'suspended'; // 账户状态
  createdAt: string;
  avatarUrl?: string;
  // 技师特有字段
  specialties?: string[];
  rating?: number;
  completedOrders?: number;
}

const AdminUserManagementPage: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [filterRole, setFilterRole] = useState<string | undefined>(undefined);
  const [filterStatus, setFilterStatus] = useState<string | undefined>(undefined);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchUsers();
  }, [searchText, filterRole, filterStatus]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const params = {
        search: searchText,
        role: filterRole,
        status: filterStatus,
      };
      // 假设后端 /api/admin/users 接口支持查询参数
      const response = await api.get('/admin/users', { params });
      setUsers(response.data);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('加载用户列表失败。');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  const handleEdit = (user: User) => {
    setEditingUser(user);
    form.setFieldsValue(user);
    setIsModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await api.delete(`/admin/users/${id}`); // 假设后端删除用户 API
      message.success('用户删除成功！');
      fetchUsers();
    } catch (error) {
      console.error('删除用户失败:', error);
      message.error('删除用户失败。');
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      if (editingUser) {
        await api.put(`/admin/users/${editingUser.id}`, values); // 假设后端更新用户 API
        message.success('用户信息更新成功！');
      } else {
        // 新增用户，如果需要
        // await api.post('/admin/users', values);
        // message.success('用户添加成功！');
      }
      setIsModalVisible(false);
      setEditingUser(null);
      form.resetFields();
      fetchUsers();
    } catch (error) {
      console.error('更新/添加用户失败:', error);
      message.error('操作失败。');
    } finally {
      setLoading(false);
    }
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    setEditingUser(null);
    form.resetFields();
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '头像',
      dataIndex: 'avatarUrl',
      key: 'avatarUrl',
      render: (text: string, record: User) => <Avatar src={text} icon={<UserOutlined />} />,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '手机号',
      dataIndex: 'phoneNumber',
      key: 'phoneNumber',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      render: (text: string) => text || '-',
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => {
        let color = '';
        if (role === 'admin') color = 'gold';
        else if (role === 'technician') color = 'geekblue';
        else color = 'green';
        return <Tag color={color}>{role.toUpperCase()}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let color = '';
        if (status === 'active') color = 'success';
        else if (status === 'suspended') color = 'error';
        else color = 'default';
        return <Tag color={color}>{status.toUpperCase()}</Tag>;
      },
    },
    {
      title: '注册时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: User) => (
        <Space size="middle">
          <Button icon={<EditOutlined />} onClick={() => handleEdit(record)}>编辑</Button>
          <Popconfirm
            title="确定删除此用户吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button icon={<DeleteOutlined />} danger>删除</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card title="用户与技师管理">
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Input.Search
            placeholder="搜索用户名或手机号"
            onSearch={handleSearch}
            style={{ width: 250 }}
            enterButton
          />
          <Select
            style={{ width: 150 }}
            placeholder="筛选角色"
            allowClear
            onChange={value => setFilterRole(value)}
          >
            <Option value="user">用户</Option>
            <Option value="technician">技师</Option>
            <Option value="admin">管理员</Option>
          </Select>
          <Select
            style={{ width: 150 }}
            placeholder="筛选状态"
            allowClear
            onChange={value => setFilterStatus(value)}
          >
            <Option value="active">活跃</Option>
            <Option value="inactive">不活跃</Option>
            <Option value="suspended">封禁</Option>
          </Select>
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={users}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />

      <Modal
        title={editingUser ? '编辑用户信息' : '新增用户'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        confirmLoading={loading}
      >
        <Form form={form} layout="vertical">
          <Form.Item name="username" label="用户名" rules={[{ required: true, message: '请输入用户名！' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="phoneNumber" label="手机号" rules={[{ required: true, message: '请输入手机号！' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="email" label="邮箱">
            <Input />
          </Form.Item>
          <Form.Item name="role" label="角色" rules={[{ required: true, message: '请选择角色！' }]}>
            <Select>
              <Option value="user">用户</Option>
              <Option value="technician">技师</Option>
              <Option value="admin">管理员</Option>
            </Select>
          </Form.Item>
          <Form.Item name="status" label="状态" rules={[{ required: true, message: '请选择状态！' }]}>
            <Select>
              <Option value="active">活跃</Option>
              <Option value="inactive">不活跃</Option>
              <Option value="suspended">封禁</Option>
            </Select>
          </Form.Item>
          {/* 如果是技师，可以添加技师专属字段，如 specialties */}
          {form.getFieldValue('role') === 'technician' && (
            <Form.Item name="specialties" label="擅长技能">
              {/* 这里需要一个 Select.Option 列表，来自服务分类数据 */}
              <Select mode="multiple" placeholder="请选择擅长技能">
                <Option value="水电维修">水电维修</Option>
                <Option value="家电维修">家电维修</Option>
                <Option value="开锁换锁">开锁换锁</Option>
                {/* 更多技能 */}
              </Select>
            </Form.Item>
          )}
        </Form>
      </Modal>
    </Card>
  );
};

export default AdminUserManagementPage;
```

### 6.3 服务与配件管理

管理平台提供的服务类别、具体服务项以及相关配件的库存。

#### 6.3.1 服务与配件管理页面 (`src/pages/admin/AdminServiceManagementPage.tsx`)

```tsx
// src/pages/admin/AdminServiceManagementPage.tsx
import React, { useState, useEffect } from 'react';
import { Card, Tabs, Table, Input, Button, Space, Modal, Form, Select, message, Popconfirm, InputNumber, Upload } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, UploadOutlined } from '@ant-design/icons';
import api from '../../services/api';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

interface ServiceCategory {
  id: number;
  name: string;
  description?: string;
}

interface ServiceItem {
  id: number;
  name: string;
  description: string;
  price: number;
  category: ServiceCategory; // 关联的服务类别
  imageUrl?: string;
  available: boolean; // 是否可用
}

interface Part {
  id: number;
  name: string;
  description?: string;
  price: number;
  stock: number; // 库存
  imageUrl?: string;
}

const AdminServiceManagementPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('categories');
  const [loading, setLoading] = useState(true);

  // 类别管理
  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [isCategoryModalVisible, setIsCategoryModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<ServiceCategory | null>(null);
  const [categoryForm] = Form.useForm();

  // 服务项管理
  const [serviceItems, setServiceItems] = useState<ServiceItem[]>([]);
  const [isServiceModalVisible, setIsServiceModalVisible] = useState(false);
  const [editingService, setEditingService] = useState<ServiceItem | null>(null);
  const [serviceForm] = Form.useForm();

  // 配件管理
  const [parts, setParts] = useState<Part[]>([]);
  const [isPartModalVisible, setIsPartModalVisible] = useState(false);
  const [editingPart, setEditingPart] = useState<Part | null>(null);
  const [partForm] = Form.useForm();

  useEffect(() => {
    fetchCategories();
    fetchServiceItems();
    fetchParts();
  }, []);

  // --- 类别管理函数 ---
  const fetchCategories = async () => {
    setLoading(true);
    try {
      const response = await api.get('/admin/service-categories');
      setCategories(response.data);
    } catch (error) {
      console.error('获取服务类别失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCategory = () => {
    setEditingCategory(null);
    categoryForm.resetFields();
    setIsCategoryModalVisible(true);
  };

  const handleEditCategory = (category: ServiceCategory) => {
    setEditingCategory(category);
    categoryForm.setFieldsValue(category);
    setIsCategoryModalVisible(true);
  };

  const handleDeleteCategory = async (id: number) => {
    try {
      await api.delete(`/admin/service-categories/${id}`);
      message.success('服务类别删除成功！');
      fetchCategories();
    } catch (error) {
      console.error('删除服务类别失败:', error);
      message.error('删除失败，可能存在关联的服务项。');
    }
  };

  const handleCategoryModalOk = async () => {
    try {
      const values = await categoryForm.validateFields();
      setLoading(true);
      if (editingCategory) {
        await api.put(`/admin/service-categories/${editingCategory.id}`, values);
        message.success('服务类别更新成功！');
      } else {
        await api.post('/admin/service-categories', values);
        message.success('服务类别添加成功！');
      }
      setIsCategoryModalVisible(false);
      setEditingCategory(null);
      categoryForm.resetFields();
      fetchCategories();
    } catch (error) {
      console.error('操作服务类别失败:', error);
      message.error('操作失败。');
    } finally {
      setLoading(false);
    }
  };

  // --- 服务项管理函数 ---
  const fetchServiceItems = async () => {
    setLoading(true);
    try {
      const response = await api.get('/admin/service-items');
      setServiceItems(response.data);
    } catch (error) {
      console.error('获取服务项失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddService = () => {
    setEditingService(null);
    serviceForm.resetFields();
    setIsServiceModalVisible(true);
  };

  const handleEditService = (service: ServiceItem) => {
    setEditingService(service);
    // Antd Form 默认只设置第一层，对于嵌套对象 category.id 需要单独处理
    serviceForm.setFieldsValue({ ...service, categoryId: service.category.id });
    setIsServiceModalVisible(true);
  };

  const handleDeleteService = async (id: number) => {
    try {
      await api.delete(`/admin/service-items/${id}`);
      message.success('服务项删除成功！');
      fetchServiceItems();
    } catch (error) {
      console.error('删除服务项失败:', error);
      message.error('删除失败。');
    }
  };

  const handleServiceModalOk = async () => {
    try {
      const values = await serviceForm.validateFields();
      setLoading(true);
      if (editingService) {
        await api.put(`/admin/service-items/${editingService.id}`, values);
        message.success('服务项更新成功！');
      } else {
        await api.post('/admin/service-items', values);
        message.success('服务项添加成功！');
      }
      setIsServiceModalVisible(false);
      setEditingService(null);
      serviceForm.resetFields();
      fetchServiceItems();
    } catch (error) {
      console.error('操作服务项失败:', error);
      message.error('操作失败。');
    } finally {
      setLoading(false);
    }
  };

  // --- 配件管理函数 ---
  const fetchParts = async () => {
    setLoading(true);
    try {
      const response = await api.get('/admin/parts');
      setParts(response.data);
    } catch (error) {
      console.error('获取配件失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddPart = () => {
    setEditingPart(null);
    partForm.resetFields();
    setIsPartModalVisible(true);
  };

  const handleEditPart = (part: Part) => {
    setEditingPart(part);
    partForm.setFieldsValue(part);
    setIsPartModalVisible(true);
  };

  const handleDeletePart = async (id: number) => {
    try {
      await api.delete(`/admin/parts/${id}`);
      message.success('配件删除成功！');
      fetchParts();
    } catch (error) {
      console.error('删除配件失败:', error);
      message.error('删除失败。');
    }
  };

  const handlePartModalOk = async () => {
    try {
      const values = await partForm.validateFields();
      setLoading(true);
      if (editingPart) {
        await api.put(`/admin/parts/${editingPart.id}`, values);
        message.success('配件更新成功！');
      } else {
        await api.post('/admin/parts', values);
        message.success('配件添加成功！');
      }
      setIsPartModalVisible(false);
      setEditingPart(null);
      partForm.resetFields();
      fetchParts();
    } catch (error) {
      console.error('操作配件失败:', error);
      message.error('操作失败。');
    } finally {
      setLoading(false);
    }
  };

  // --- Ant Design Table Columns ---
  const categoryColumns = [
    { title: 'ID', dataIndex: 'id', key: 'id' },
    { title: '类别名称', dataIndex: 'name', key: 'name' },
    { title: '描述', dataIndex: 'description', key: 'description', render: (text: string) => text || '-' },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: ServiceCategory) => (
        <Space size="middle">
          <Button icon={<EditOutlined />} onClick={() => handleEditCategory(record)}>编辑</Button>
          <Popconfirm title="确定删除吗？" onConfirm={() => handleDeleteCategory(record.id)}>
            <Button icon={<DeleteOutlined />} danger>删除</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const serviceItemColumns = [
    { title: 'ID', dataIndex: 'id', key: 'id' },
    { title: '服务名称', dataIndex: 'name', key: 'name' },
    { title: '描述', dataIndex: 'description', key: 'description', ellipsis: true },
    { title: '价格', dataIndex: 'price', key: 'price', render: (price: number) => `¥${price.toFixed(2)}` },
    { title: '所属类别', dataIndex: ['category', 'name'], key: 'category' },
    {
      title: '是否可用',
      dataIndex: 'available',
      key: 'available',
      render: (available: boolean) => <Tag color={available ? 'green' : 'red'}>{available ? '可用' : '不可用'}</Tag>,
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: ServiceItem) => (
        <Space size="middle">
          <Button icon={<EditOutlined />} onClick={() => handleEditService(record)}>编辑</Button>
          <Popconfirm title="确定删除吗？" onConfirm={() => handleDeleteService(record.id)}>
            <Button icon={<DeleteOutlined />} danger>删除</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const partColumns = [
    { title: 'ID', dataIndex: 'id', key: 'id' },
    { title: '配件名称', dataIndex: 'name', key: 'name' },
    { title: '描述', dataIndex: 'description', key: 'description', ellipsis: true },
    { title: '价格', dataIndex: 'price', key: 'price', render: (price: number) => `¥${price.toFixed(2)}` },
    { title: '库存', dataIndex: 'stock', key: 'stock' },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: Part) => (
        <Space size="middle">
          <Button icon={<EditOutlined />} onClick={() => handleEditPart(record)}>编辑</Button>
          <Popconfirm title="确定删除吗？" onConfirm={() => handleDeletePart(record.id)}>
            <Button icon={<DeleteOutlined />} danger>删除</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card title="服务与配件管理">
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="服务类别" key="categories">
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAddCategory} style={{ marginBottom: 16 }}>
            添加类别
          </Button>
          <Table
            columns={categoryColumns}
            dataSource={categories}
            rowKey="id"
            loading={loading}
            pagination={{ pageSize: 10 }}
          />
        </TabPane>

        <TabPane tab="服务项" key="serviceItems">
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAddService} style={{ marginBottom: 16 }}>
            添加服务项
          </Button>
          <Table
            columns={serviceItemColumns}
            dataSource={serviceItems}
            rowKey="id"
            loading={loading}
            pagination={{ pageSize: 10 }}
          />
        </TabPane>

        <TabPane tab="配件管理" key="parts">
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAddPart} style={{ marginBottom: 16 }}>
            添加配件
          </Button>
          <Table
            columns={partColumns}
            dataSource={parts}
            rowKey="id"
            loading={loading}
            pagination={{ pageSize: 10 }}
          />
        </TabPane>
      </Tabs>

      {/* 类别编辑/新增 Modal */}
      <Modal
        title={editingCategory ? '编辑服务类别' : '新增服务类别'}
        open={isCategoryModalVisible}
        onOk={handleCategoryModalOk}
        onCancel={() => { setIsCategoryModalVisible(false); categoryForm.resetFields(); }}
        confirmLoading={loading}
      >
        <Form form={categoryForm} layout="vertical">
          <Form.Item name="name" label="类别名称" rules={[{ required: true, message: '请输入类别名称！' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="description" label="描述">
            <TextArea rows={2} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 服务项编辑/新增 Modal */}
      <Modal
        title={editingService ? '编辑服务项' : '新增服务项'}
        open={isServiceModalVisible}
        onOk={handleServiceModalOk}
        onCancel={() => { setIsServiceModalVisible(false); serviceForm.resetFields(); }}
        confirmLoading={loading}
      >
        <Form form={serviceForm} layout="vertical">
          <Form.Item name="name" label="服务名称" rules={[{ required: true, message: '请输入服务名称！' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="description" label="描述" rules={[{ required: true, message: '请输入服务描述！' }]}>
            <TextArea rows={3} />
          </Form.Item>
          <Form.Item name="price" label="价格" rules={[{ required: true, message: '请输入价格！' }]}>
            <InputNumber min={0} step={0.01} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name="categoryId" label="所属类别" rules={[{ required: true, message: '请选择所属类别！' }]}>
            <Select placeholder="请选择服务类别">
              {categories.map(cat => (
                <Option key={cat.id} value={cat.id}>{cat.name}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="available" label="是否可用" valuePropName="checked">
            <Switch />
          </Form.Item>
          <Form.Item name="imageUrl" label="服务图片 (URL)">
            <Input placeholder="请输入图片URL或上传" />
            {/* 实际生产环境会集成图片上传组件 */}
            <Upload
              name="file"
              action="/api/upload/image" // 替换为你的图片上传接口
              listType="picture"
              maxCount={1}
              onChange={(info) => {
                if (info.file.status === 'done') {
                  message.success(`${info.file.name} 图片上传成功`);
                  serviceForm.setFieldsValue({ imageUrl: info.file.response.url }); // 假设返回的url在response中
                } else if (info.file.status === 'error') {
                  message.error(`${info.file.name} 图片上传失败.`);
                }
              }}
            >
              <Button icon={<UploadOutlined />}>上传图片</Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>

      {/* 配件编辑/新增 Modal */}
      <Modal
        title={editingPart ? '编辑配件' : '新增配件'}
        open={isPartModalVisible}
        onOk={handlePartModalOk}
        onCancel={() => { setIsPartModalVisible(false); partForm.resetFields(); }}
        confirmLoading={loading}
      >
        <Form form={partForm} layout="vertical">
          <Form.Item name="name" label="配件名称" rules={[{ required: true, message: '请输入配件名称！' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="description" label="描述">
            <TextArea rows={2} />
          </Form.Item>
          <Form.Item name="price" label="价格" rules={[{ required: true, message: '请输入价格！' }]}>
            <InputNumber min={0} step={0.01} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name="stock" label="库存" rules={[{ required: true, message: '请输入库存！' }]}>
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name="imageUrl" label="配件图片 (URL)">
            <Input placeholder="请输入图片URL或上传" />
            <Upload name="file" action="/api/upload/image" listType="picture" maxCount={1}>
              <Button icon={<UploadOutlined />}>上传图片</Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default AdminServiceManagementPage;
```

### 6.4 订单审核与派单

管理员查看所有订单，并可以进行审核、修改状态和人工派单。

#### 6.4.1 订单管理页面 (`src/pages/admin/AdminOrderManagementPage.tsx`)

```tsx
// src/pages/admin/AdminOrderManagementPage.tsx
import React, { useState, useEffect } from 'react';
import { Card, Table, Input, Button, Space, Tag, Modal, Form, Select, message, Popconfirm, DatePicker, Typography } from 'antd';
import { SearchOutlined, EditOutlined, EyeOutlined, UserOutlined, ToolOutlined } from '@ant-design/icons';
import api from '../../services/api';
import moment from 'moment';

const { Option } = Select;
const { Text } = Typography;
const { RangePicker } = DatePicker;

// 与之前订单类型类似，增加更多后台管理需要的字段
interface AdminOrder {
  id: number;
  orderNumber: string;
  serviceName: string;
  status: 'pending' | 'accepted' | 'inProgress' | 'completed' | 'cancelled' | 'refunded' | 'quoted';
  totalAmount: number;
  scheduledTime: string;
  createdAt: string;
  user: { id: number; username: string; phoneNumber: string; };
  technician?: { id: number; username: string; phoneNumber: string; };
  serviceAddress: string;
  quotedPrice?: number;
  quoteDescription?: string;
}

interface TechnicianListItem {
  id: number;
  username: string;
  phoneNumber: string;
  specialties: string[];
  rating: number;
}


const AdminOrderManagementPage: React.FC = () => {
  const [orders, setOrders] = useState<AdminOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [filterStatus, setFilterStatus] = useState<string | undefined>(undefined);
  const [filterDateRange, setFilterDateRange] = useState<[moment.Moment | null, moment.Moment | null] | null>(null);

  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [editingOrder, setEditingOrder] = useState<AdminOrder | null>(null);
  const [editForm] = Form.useForm();

  const [isDispatchModalVisible, setIsDispatchModalVisible] = useState(false);
  const [dispatchingOrder, setDispatchingOrder] = useState<AdminOrder | null>(null);
  const [dispatchForm] = Form.useForm();
  const [availableTechnicians, setAvailableTechnicians] = useState<TechnicianListItem[]>([]);


  // 订单状态映射，与用户端保持一致或更详细
  const orderStatusMap: { [key: string]: { text: string; color: string } } = {
    pending: { text: '待接受', color: 'blue' },
    accepted: { text: '已接受', color: 'geekblue' },
    inProgress: { text: '服务中', color: 'processing' },
    completed: { text: '已完成', color: 'success' },
    cancelled: { text: '已取消', color: 'default' },
    refunded: { text: '已退款', color: 'warning' },
    quoted: { text: '已报价待确认', color: 'magenta' },
  };

  useEffect(() => {
    fetchOrders();
  }, [searchText, filterStatus, filterDateRange]);

  const fetchOrders = async () => {
    setLoading(true);
    try {
      const params = {
        search: searchText,
        status: filterStatus,
        startDate: filterDateRange && filterDateRange[0] ? filterDateRange[0].toISOString() : undefined,
        endDate: filterDateRange && filterDateRange[1] ? filterDateRange[1].toISOString() : undefined,
      };
      // 假设后端 /api/admin/orders 接口
      const response = await api.get('/admin/orders', { params });
      setOrders(response.data);
    } catch (error) {
      console.error('获取订单列表失败:', error);
      message.error('加载订单列表失败。');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  const handleViewDetails = (order: AdminOrder) => {
    // 可以跳转到通用订单详情页，并确保是管理员视图
    window.open(`/admin/order/${order.id}`, '_blank'); // 或者在Modal中显示更多详情
  };

  const handleEditOrder = (order: AdminOrder) => {
    setEditingOrder(order);
    editForm.setFieldsValue({
      ...order,
      scheduledTime: moment(order.scheduledTime),
      // 如果技师是对象，可能需要设置 technicianId
      technicianId: order.technician ? order.technician.id : undefined
    });
    setIsEditModalVisible(true);
  };

  const handleEditModalOk = async () => {
    try {
      const values = await editForm.validateFields();
      setLoading(true);
      const payload = {
        ...values,
        scheduledTime: values.scheduledTime ? values.scheduledTime.toISOString() : undefined,
      };
      await api.put(`/admin/orders/${editingOrder!.id}`, payload); // 假设后端更新订单 API
      message.success('订单信息更新成功！');
      setIsEditModalVisible(false);
      setEditingOrder(null);
      editForm.resetFields();
      fetchOrders();
    } catch (error) {
      console.error('更新订单失败:', error);
      message.error('更新订单失败。');
    } finally {
      setLoading(false);
    }
  };

  const handleDispatchOrder = async (order: AdminOrder) => {
    setDispatchingOrder(order);
    setIsDispatchModalVisible(true);
    // 获取可用的技师列表，可能需要根据服务类别来过滤
    try {
      const response = await api.get(`/admin/technicians/available?serviceId=${order.serviceName}`); // 假设获取可用技师 API
      setAvailableTechnicians(response.data);
    } catch (error) {
      console.error('获取可用技师失败:', error);
      message.error('获取可用技师失败。');
    }
  };

  const handleDispatchModalOk = async () => {
    try {
      const values = await dispatchForm.validateFields();
      setLoading(true);
      await api.post(`/admin/orders/${dispatchingOrder!.id}/dispatch`, { technicianId: values.technicianId }); // 假设后端派单 API
      message.success('订单派发成功！');
      setIsDispatchModalVisible(false);
      setDispatchingOrder(null);
      dispatchForm.resetFields();
      fetchOrders();
    } catch (error) {
      console.error('派单失败:', error);
      message.error('派单失败。');
    } finally {
      setLoading(false);
    }
  };


  const columns = [
    {
      title: '订单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
    },
    {
      title: '服务名称',
      dataIndex: 'serviceName',
      key: 'serviceName',
    },
    {
      title: '用户',
      dataIndex: ['user', 'username'],
      key: 'user',
      render: (_: any, record: AdminOrder) => (
        <Space>
          <UserOutlined /> {record.user.username}
          <Text type="secondary" copyable>{record.user.phoneNumber}</Text>
        </Space>
      )
    },
    {
      title: '技师',
      dataIndex: ['technician', 'username'],
      key: 'technician',
      render: (text: string, record: AdminOrder) => record.technician ? (
        <Space>
          <ToolOutlined /> {record.technician.username}
          <Text type="secondary" copyable>{record.technician.phoneNumber}</Text>
        </Space>
      ) : <Tag>未分配</Tag>,
    },
    {
      title: '预约时间',
      dataIndex: 'scheduledTime',
      key: 'scheduledTime',
      render: (text: string) => moment(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={orderStatusMap[status]?.color || 'default'}>
          {orderStatusMap[status]?.text || status}
        </Tag>
      ),
    },
    {
      title: '金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount: number) => `¥${amount.toFixed(2)}`,
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: AdminOrder) => (
        <Space size="middle">
          <Button icon={<EyeOutlined />} onClick={() => handleViewDetails(record)}>详情</Button>
          <Button icon={<EditOutlined />} onClick={() => handleEditOrder(record)}>编辑</Button>
          {record.status === 'pending' && (
            <Button type="primary" onClick={() => handleDispatchOrder(record)}>派单</Button>
          )}
          {(record.status === 'pending' || record.status === 'accepted') && (
            <Popconfirm title="确定取消此订单吗？" onConfirm={async () => {
              try {
                await api.post(`/admin/orders/${record.id}/cancel`); // 假设后端管理员取消订单 API
                message.success('订单已取消！');
                fetchOrders();
              } catch (e) {
                message.error('取消失败。');
              }
            }}>
              <Button danger>取消</Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <Card title="订单管理">
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Input.Search
            placeholder="搜索订单号/服务/用户"
            onSearch={handleSearch}
            style={{ width: 250 }}
            enterButton
          />
          <Select
            style={{ width: 150 }}
            placeholder="筛选状态"
            allowClear
            onChange={value => setFilterStatus(value)}
          >
            {Object.keys(orderStatusMap).map(key => (
              <Option key={key} value={key}>{orderStatusMap[key].text}</Option>
            ))}
          </Select>
          <RangePicker
            showTime
            format="YYYY-MM-DD HH:mm"
            placeholder={['开始时间', '结束时间']}
            onChange={dates => setFilterDateRange(dates)}
          />
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={orders}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />

      {/* 订单编辑 Modal */}
      <Modal
        title="编辑订单信息"
        open={isEditModalVisible}
        onOk={handleEditModalOk}
        onCancel={() => { setIsEditModalVisible(false); setEditingOrder(null); editForm.resetFields(); }}
        confirmLoading={loading}
      >
        <Form form={editForm} layout="vertical">
          <Form.Item name="orderNumber" label="订单号">
            <Input disabled />
          </Form.Item>
          <Form.Item name="serviceName" label="服务名称" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="status" label="订单状态" rules={[{ required: true }]}>
            <Select>
              {Object.keys(orderStatusMap).map(key => (
                <Option key={key} value={key}>{orderStatusMap[key].text}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="totalAmount" label="总金额" rules={[{ required: true }]}>
            <InputNumber min={0} step={0.01} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name="scheduledTime" label="预约时间" rules={[{ required: true }]}>
            <DatePicker showTime format="YYYY-MM-DD HH:mm" style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name="serviceAddress" label="服务地址" rules={[{ required: true }]}>
            <Input.TextArea rows={2} />
          </Form.Item>
          {/* 可以在这里添加技师选择，但派单有专门的Modal */}
        </Form>
      </Modal>

      {/* 派单 Modal */}
      <Modal
        title={`派单给订单: ${dispatchingOrder?.orderNumber}`}
        open={isDispatchModalVisible}
        onOk={handleDispatchModalOk}
        onCancel={() => { setIsDispatchModalVisible(false); setDispatchingOrder(null); dispatchForm.resetFields(); }}
        confirmLoading={loading}
      >
        <Form form={dispatchForm} layout="vertical">
          <Form.Item name="technicianId" label="选择技师" rules={[{ required: true, message: '请选择要派发的技师！' }]}>
            <Select placeholder="选择技师">
              {availableTechnicians.map(tech => (
                <Option key={tech.id} value={tech.id}>
                  {tech.username} ({tech.phoneNumber}) - 评分: {tech.rating?.toFixed(1) || '无'} - 擅长: {tech.specialties.join(', ')}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default AdminOrderManagementPage;
```

### 6.5 财务审核

管理员管理技师的佣金提现申请。

#### 6.5.1 财务管理页面 (`src/pages/admin/AdminFinanceManagementPage.tsx`)

```tsx
// src/pages/admin/AdminFinanceManagementPage.tsx
import React, { useState, useEffect } from 'react';
import { Card, Table, Tag, Button, Space, message, Popconfirm, Typography } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, DollarOutlined } from '@ant-design/icons';
import api from '../../services/api';
import moment from 'moment';

const { Title, Text } = Typography;

interface WithdrawalRequest {
  id: number;
  technician: {
    id: number;
    username: string;
    phoneNumber: string;
  };
  amount: number;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  processedAt?: string;
  remark?: string; // 驳回原因等
}

const AdminFinanceManagementPage: React.FC = () => {
  const [withdrawalRequests, setWithdrawalRequests] = useState<WithdrawalRequest[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchWithdrawalRequests();
  }, []);

  const fetchWithdrawalRequests = async () => {
    setLoading(true);
    try {
      // 假设后端 /api/admin/finance/withdrawals 接口
      const response = await api.get('/admin/finance/withdrawals');
      setWithdrawalRequests(response.data);
    } catch (error) {
      console.error('获取提现请求失败:', error);
      message.error('加载提现请求失败。');
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (id: number) => {
    try {
      await api.post(`/admin/finance/withdrawals/${id}/approve`);
      message.success('提现申请已批准！');
      fetchWithdrawalRequests();
    } catch (error) {
      console.error('批准提现失败:', error);
      message.error('批准失败。');
    }
  };

  const handleReject = async (id: number) => {
    // 弹出一个 Modal 让管理员输入驳回原因
    Modal.confirm({
      title: '驳回提现申请',
      content: (
        <Form layout="vertical">
          <Form.Item name="remark" label="驳回原因" rules={[{ required: true, message: '请填写驳回原因！' }]}>
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      ),
      onOk: async (close) => {
        const formValues = await new Promise(resolve => { // 解决Form的问题
          const tempForm = Form.useForm()[0];
          tempForm.validateFields().then(resolve);
        });
        try {
          await api.post(`/admin/finance/withdrawals/${id}/reject`, formValues);
          message.success('提现申请已驳回！');
          fetchWithdrawalRequests();
          close();
        } catch (error) {
          console.error('驳回提现失败:', error);
          message.error('驳回失败。');
        }
      },
      okText: '确认驳回',
      cancelText: '取消',
      width: 400,
    });
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '技师',
      dataIndex: ['technician', 'username'],
      key: 'technician',
      render: (_: any, record: WithdrawalRequest) => (
        <Space>
          <UserOutlined /> {record.technician.username}
          <Text type="secondary" copyable>{record.technician.phoneNumber}</Text>
        </Space>
      )
    },
    {
      title: '提现金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => <Text strong type="danger">¥{amount.toFixed(2)}</Text>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let color = '';
        if (status === 'pending') color = 'blue';
        else if (status === 'approved') color = 'success';
        else color = 'error';
        return <Tag color={color}>{status.toUpperCase()}</Tag>;
      },
    },
    {
      title: '申请时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => moment(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '处理时间',
      dataIndex: 'processedAt',
      key: 'processedAt',
      render: (text: string) => text ? moment(text).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      render: (text: string) => text || '-',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: WithdrawalRequest) => (
        <Space size="middle">
          {record.status === 'pending' && (
            <>
              <Popconfirm title="确定批准此提现申请吗？" onConfirm={() => handleApprove(record.id)}>
                <Button icon={<CheckCircleOutlined />} type="primary">批准</Button>
              </Popconfirm>
              <Button icon={<CloseCircleOutlined />} danger onClick={() => handleReject(record.id)}>驳回</Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <Card title="财务管理 - 提现审核">
      <Table
        columns={columns}
        dataSource={withdrawalRequests}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />
    </Card>
  );
};

export default AdminFinanceManagementPage;
```

### 6.6 消息公告管理

管理员可以发布系统公告给所有用户或特定角色。

#### 6.6.1 消息公告管理页面 (`src/pages/admin/AdminAnnouncementManagementPage.tsx`)

```tsx
// src/pages/admin/AdminAnnouncementManagementPage.tsx
import React, { useState, useEffect } from 'react';
import { Card, Table, Input, Button, Space, Tag, Modal, Form, Select, message, Popconfirm, Typography } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import api from '../../services/api';
import moment from 'moment';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface Announcement {
  id: number;
  title: string;
  content: string;
  targetRole: 'all' | 'user' | 'technician'; // 目标角色
  publishedAt: string;
}

const AdminAnnouncementManagementPage: React.FC = () => {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingAnnouncement, setEditingAnnouncement] = useState<Announcement | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchAnnouncements();
  }, []);

  const fetchAnnouncements = async () => {
    setLoading(true);
    try {
      // 假设后端 /api/admin/announcements 接口
      const response = await api.get('/admin/announcements');
      setAnnouncements(response.data);
    } catch (error) {
      console.error('获取公告列表失败:', error);
      message.error('加载公告列表失败。');
    } finally {
      setLoading(false);
    }
  };

  const handleAddAnnouncement = () => {
    setEditingAnnouncement(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditAnnouncement = (announcement: Announcement) => {
    setEditingAnnouncement(announcement);
    form.setFieldsValue(announcement);
    setIsModalVisible(true);
  };

  const handleDeleteAnnouncement = async (id: number) => {
    try {
      await api.delete(`/admin/announcements/${id}`);
      message.success('公告删除成功！');
      fetchAnnouncements();
    } catch (error) {
      console.error('删除公告失败:', error);
      message.error('删除失败。');
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      if (editingAnnouncement) {
        await api.put(`/admin/announcements/${editingAnnouncement.id}`, values);
        message.success('公告更新成功！');
      } else {
        await api.post('/admin/announcements', values);
        message.success('公告发布成功！');
      }
      setIsModalVisible(false);
      setEditingAnnouncement(null);
      form.resetFields();
      fetchAnnouncements();
    } catch (error) {
      console.error('操作公告失败:', error);
      message.error('操作失败。');
    } finally {
      setLoading(false);
    }
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    setEditingAnnouncement(null);
    form.resetFields();
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
    },
    {
      title: '目标用户',
      dataIndex: 'targetRole',
      key: 'targetRole',
      render: (role: string) => {
        let text = '';
        let color = '';
        if (role === 'all') { text = '所有用户'; color = 'blue'; }
        else if (role === 'user') { text = '普通用户'; color = 'green'; }
        else if (role === 'technician') { text = '技师'; color = 'purple'; }
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '发布时间',
      dataIndex: 'publishedAt',
      key: 'publishedAt',
      render: (text: string) => moment(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: Announcement) => (
        <Space size="middle">
          <Button icon={<EditOutlined />} onClick={() => handleEditAnnouncement(record)}>编辑</Button>
          <Popconfirm
            title="确定删除此公告吗？"
            onConfirm={() => handleDeleteAnnouncement(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button icon={<DeleteOutlined />} danger>删除</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card title="消息公告管理">
      <Button type="primary" icon={<PlusOutlined />} onClick={handleAddAnnouncement} style={{ marginBottom: 16 }}>
        发布新公告
      </Button>
      <Table
        columns={columns}
        dataSource={announcements}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />

      <Modal
        title={editingAnnouncement ? '编辑公告' : '发布新公告'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        confirmLoading={loading}
      >
        <Form form={form} layout="vertical">
          <Form.Item name="title" label="标题" rules={[{ required: true, message: '请输入公告标题！' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="content" label="内容" rules={[{ required: true, message: '请输入公告内容！' }]}>
            <TextArea rows={4} />
          </Form.Item>
          <Form.Item name="targetRole" label="目标用户" rules={[{ required: true, message: '请选择目标用户！' }]}>
            <Select>
              <Option value="all">所有用户</Option>
              <Option value="user">普通用户</Option>
              <Option value="technician">技师</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default AdminAnnouncementManagementPage;
```

---

**本篇总结：**

在这一篇中，我们构建了**管理员后台界面**的核心模块：

* **管理员布局和权限控制：** 实现了管理员专属的布局组件 `AdminLayout`，并通过 `PrivateRoute` 确保只有管理员角色能够访问后台页面。
* **管理员仪表盘：** 展示了系统整体概览数据，为管理员提供了宏观的运营视图。
* **用户与技师管理：** 提供了用户和技师账户的列表、搜索、筛选、编辑和删除功能，是账户生命周期管理的核心。
* **服务与配件管理：** 实现了服务类别、服务项和配件的增、删、改、查功能，保证平台服务内容的灵活性。
* **订单审核与派单：** 允许管理员查看所有订单，并进行人工派单、订单状态修改和取消等操作，提供了人工干预订单流程的能力。
* **财务审核：** 管理员可以查看技师的提现申请，并进行批准或驳回操作，维护平台的财务健康。
* **消息公告管理：** 提供了发布和管理系统公告的功能，方便管理员向用户和技师发布重要通知。

至此，我们的前端部分已经完成了所有核心功能模块的实现。这些代码提供了基础的 UI 和与后端 API 的联调逻辑。在实际项目中，还需要根据具体需求进行更多的细节优化、错误处理、表单验证、性能优化以及更复杂的交互设计。

您对管理员后台界面的实现有什么疑问吗？如果没有，我们将进入**后端部分**的实现。