# 第九篇：React前端项目初始化与基础配置

## 概述

在前面的教程中，我们已经完成了后端API的开发，包括用户管理、服务商管理、订单管理、支付系统、通讯系统等核心功能。本篇教程将开始前端开发，使用现代化的技术栈构建用户友好的Web界面。

我们将实现：
- 基于Vite + React + TypeScript的前端项目
- Ant Design 5.x UI组件库集成
- HTTP请求与状态管理配置
- 路由配置与权限控制
- 开发环境优化配置

## 技术栈

- **构建工具**: Vite 4.x
- **前端框架**: React 18.x + TypeScript
- **UI组件库**: Ant Design 5.x
- **路由管理**: React Router 6.x
- **状态管理**: Zustand + React Query
- **HTTP客户端**: Axios
- **样式方案**: CSS Modules + Less

## 项目结构规划

```
frontend/
├── public/                   # 静态资源
│   ├── favicon.ico
│   └── index.html
├── src/
│   ├── components/          # 通用组件
│   │   ├── Layout/         # 布局组件
│   │   ├── Form/           # 表单组件
│   │   └── Common/         # 其他通用组件
│   ├── pages/              # 页面组件
│   │   ├── Auth/           # 认证相关页面
│   │   ├── User/           # 用户端页面
│   │   ├── Provider/       # 服务商端页面
│   │   └── Admin/          # 管理员端页面
│   ├── hooks/              # 自定义Hooks
│   ├── services/           # API服务
│   ├── stores/             # 状态管理
│   ├── utils/              # 工具函数
│   ├── types/              # TypeScript类型定义
│   ├── constants/          # 常量定义
│   ├── styles/             # 全局样式
│   ├── App.tsx             # 根组件
│   └── main.tsx            # 入口文件
├── package.json
├── vite.config.ts          # Vite配置
├── tsconfig.json           # TypeScript配置
└── README.md
```

---

## 9.1 前端开发环境搭建

### 9.1.1 项目初始化

首先创建基于Vite的React + TypeScript项目：

```bash
# 创建项目
npm create vite@latest home-repair-frontend -- --template react-ts

# 进入项目目录
cd home-repair-frontend

# 安装依赖
npm install
```

### 9.1.2 核心依赖安装

安装项目所需的核心依赖包：

```bash
# UI组件库
npm install antd @ant-design/icons

# 路由管理
npm install react-router-dom

# 状态管理
npm install zustand @tanstack/react-query

# HTTP客户端
npm install axios

# 工具库
npm install dayjs lodash-es

# 类型定义
npm install -D @types/lodash-es

# 样式相关
npm install -D less

# 开发工具
npm install -D @types/node
```

### 9.1.3 Vite配置优化

配置Vite构建工具，优化开发体验：

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],

  // 路径别名配置
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@services': path.resolve(__dirname, './src/services'),
      '@stores': path.resolve(__dirname, './src/stores'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@types': path.resolve(__dirname, './src/types'),
      '@constants': path.resolve(__dirname, './src/constants'),
      '@styles': path.resolve(__dirname, './src/styles'),
    },
  },

  // CSS预处理器配置
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        modifyVars: {
          // Ant Design主题定制
          '@primary-color': '#1890ff',
          '@link-color': '#1890ff',
          '@success-color': '#52c41a',
          '@warning-color': '#faad14',
          '@error-color': '#f5222d',
          '@font-size-base': '14px',
          '@heading-color': 'rgba(0, 0, 0, 0.85)',
          '@text-color': 'rgba(0, 0, 0, 0.65)',
          '@text-color-secondary': 'rgba(0, 0, 0, 0.45)',
          '@disabled-color': 'rgba(0, 0, 0, 0.25)',
          '@border-radius-base': '6px',
          '@box-shadow-base': '0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
        },
      },
    },
  },

  // 开发服务器配置
  server: {
    port: 3000,
    open: true,
    cors: true,
    proxy: {
      // API代理配置
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false,
      },
    },
  },

  // 构建配置
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]',
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd'],
          router: ['react-router-dom'],
        },
      },
    },
  },

  // 环境变量配置
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
  },
});
```

### 9.1.4 TypeScript配置

配置TypeScript编译选项和路径映射：

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@components/*": ["src/components/*"],
      "@pages/*": ["src/pages/*"],
      "@hooks/*": ["src/hooks/*"],
      "@services/*": ["src/services/*"],
      "@stores/*": ["src/stores/*"],
      "@utils/*": ["src/utils/*"],
      "@types/*": ["src/types/*"],
      "@constants/*": ["src/constants/*"],
      "@styles/*": ["src/styles/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

```json
// tsconfig.node.json
{
  "compilerOptions": {
    "composite": true,
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true
  },
  "include": ["vite.config.ts"]
}
```

### 9.1.5 项目基础文件配置

创建项目的基础配置文件：

```typescript
// src/types/index.ts
// 全局类型定义

// API响应基础类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  phone: string;
  avatar?: string;
  role: UserRole;
  status: UserStatus;
  createdAt: string;
  updatedAt: string;
}

export enum UserRole {
  USER = 'user',
  SERVICE_PROVIDER = 'service_provider',
  ADMIN = 'admin',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BANNED = 'banned',
}

// 认证相关类型
export interface LoginRequest {
  username: string;
  password: string;
  captcha?: string;
}

export interface LoginResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

// 服务相关类型
export interface Service {
  id: string;
  name: string;
  description: string;
  categoryId: string;
  price: number;
  unit: string;
  duration: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ServiceCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  parentId?: string;
  children?: ServiceCategory[];
  isActive: boolean;
}

// 订单相关类型
export interface Order {
  id: string;
  orderNo: string;
  userId: string;
  serviceProviderId: string;
  serviceId: string;
  status: OrderStatus;
  totalAmount: number;
  scheduledTime: string;
  address: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

// 表单相关类型
export interface FormField {
  name: string;
  label: string;
  type: 'input' | 'textarea' | 'select' | 'date' | 'upload';
  required?: boolean;
  placeholder?: string;
  options?: { label: string; value: any }[];
  rules?: any[];
}

// 路由相关类型
export interface RouteConfig {
  path: string;
  component: React.ComponentType;
  exact?: boolean;
  meta?: {
    title?: string;
    requireAuth?: boolean;
    roles?: UserRole[];
  };
}
```

```typescript
// src/constants/index.ts
// 应用常量定义

// API相关常量
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';
export const API_TIMEOUT = 10000;

// 存储键名
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
  THEME: 'theme',
  LANGUAGE: 'language',
} as const;

// 路由路径
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  FORGOT_PASSWORD: '/forgot-password',

  // 用户端路由
  USER: {
    DASHBOARD: '/user/dashboard',
    PROFILE: '/user/profile',
    ORDERS: '/user/orders',
    SERVICES: '/user/services',
    MESSAGES: '/user/messages',
  },

  // 服务商端路由
  PROVIDER: {
    DASHBOARD: '/provider/dashboard',
    PROFILE: '/provider/profile',
    ORDERS: '/provider/orders',
    SERVICES: '/provider/services',
    EARNINGS: '/provider/earnings',
    MESSAGES: '/provider/messages',
  },

  // 管理员端路由
  ADMIN: {
    DASHBOARD: '/admin/dashboard',
    USERS: '/admin/users',
    PROVIDERS: '/admin/providers',
    ORDERS: '/admin/orders',
    SERVICES: '/admin/services',
    REPORTS: '/admin/reports',
  },
} as const;

// 订单状态映射
export const ORDER_STATUS_MAP = {
  pending: { text: '待确认', color: 'orange' },
  confirmed: { text: '已确认', color: 'blue' },
  in_progress: { text: '进行中', color: 'processing' },
  completed: { text: '已完成', color: 'success' },
  cancelled: { text: '已取消', color: 'error' },
} as const;

// 用户角色映射
export const USER_ROLE_MAP = {
  user: { text: '普通用户', color: 'default' },
  service_provider: { text: '服务商', color: 'blue' },
  admin: { text: '管理员', color: 'red' },
} as const;

// 分页配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: ['10', '20', '50', '100'],
  SHOW_SIZE_CHANGER: true,
  SHOW_QUICK_JUMPER: true,
} as const;

// 文件上传配置
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif'],
  ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'application/msword'],
} as const;

// 表单验证规则
export const VALIDATION_RULES = {
  REQUIRED: { required: true, message: '此字段为必填项' },
  EMAIL: {
    type: 'email' as const,
    message: '请输入有效的邮箱地址',
  },
  PHONE: {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入有效的手机号码',
  },
  PASSWORD: {
    min: 6,
    max: 20,
    message: '密码长度应在6-20位之间',
  },
} as const;
```

---

## 9.2 HTTP请求与状态管理

### 9.2.1 Axios配置与请求拦截器

配置HTTP客户端，处理请求和响应拦截：

```typescript
// src/services/http.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { message } from 'antd';
import { API_BASE_URL, API_TIMEOUT, STORAGE_KEYS } from '@constants/index';
import type { ApiResponse } from '@types/index';

// 创建axios实例
const http: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
http.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加认证token
    const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加请求时间戳
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      };
    }

    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response;

    // 检查业务状态码
    if (data.success) {
      return response;
    } else {
      // 业务错误处理
      message.error(data.message || '请求失败');
      return Promise.reject(new Error(data.message || '请求失败'));
    }
  },
  (error) => {
    // HTTP错误处理
    if (error.response) {
      const { status, data } = error.response;

      switch (status) {
        case 401:
          // 未授权，清除token并跳转登录
          localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
          localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
          localStorage.removeItem(STORAGE_KEYS.USER_INFO);
          window.location.href = '/login';
          message.error('登录已过期，请重新登录');
          break;
        case 403:
          message.error('没有权限访问该资源');
          break;
        case 404:
          message.error('请求的资源不存在');
          break;
        case 500:
          message.error('服务器内部错误');
          break;
        default:
          message.error(data?.message || '网络错误，请稍后重试');
      }
    } else if (error.request) {
      message.error('网络连接失败，请检查网络设置');
    } else {
      message.error('请求配置错误');
    }

    return Promise.reject(error);
  }
);

// 封装常用请求方法
export const httpService = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    return http.get(url, config).then(res => res.data.data);
  },

  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return http.post(url, data, config).then(res => res.data.data);
  },

  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return http.put(url, data, config).then(res => res.data.data);
  },

  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    return http.delete(url, config).then(res => res.data.data);
  },

  upload: <T = any>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> => {
    return http.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
    }).then(res => res.data.data);
  },
};

export default http;
```

### 9.2.2 全局状态管理

使用Zustand实现轻量级状态管理：

```typescript
// src/stores/authStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { User, LoginResponse } from '@types/index';
import { STORAGE_KEYS } from '@constants/index';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

interface AuthActions {
  login: (loginData: LoginResponse) => void;
  logout: () => void;
  updateUser: (user: Partial<User>) => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      // 状态
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      // 操作
      login: (loginData: LoginResponse) => {
        const { user, accessToken } = loginData;
        localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, accessToken);
        set({
          user,
          token: accessToken,
          isAuthenticated: true,
          isLoading: false,
        });
      },

      logout: () => {
        localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
        localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        });
      },

      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...userData },
          });
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: STORAGE_KEYS.USER_INFO,
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
```

```typescript
// src/stores/appStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { STORAGE_KEYS } from '@constants/index';

interface AppState {
  theme: 'light' | 'dark';
  language: 'zh-CN' | 'en-US';
  collapsed: boolean;
  breadcrumbs: Array<{ title: string; path?: string }>;
}

interface AppActions {
  setTheme: (theme: 'light' | 'dark') => void;
  setLanguage: (language: 'zh-CN' | 'en-US') => void;
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setBreadcrumbs: (breadcrumbs: Array<{ title: string; path?: string }>) => void;
}

export const useAppStore = create<AppState & AppActions>()(
  persist(
    (set) => ({
      // 状态
      theme: 'light',
      language: 'zh-CN',
      collapsed: false,
      breadcrumbs: [],

      // 操作
      setTheme: (theme) => set({ theme }),
      setLanguage: (language) => set({ language }),
      toggleSidebar: () => set((state) => ({ collapsed: !state.collapsed })),
      setSidebarCollapsed: (collapsed) => set({ collapsed }),
      setBreadcrumbs: (breadcrumbs) => set({ breadcrumbs }),
    }),
    {
      name: 'app-settings',
      partialize: (state) => ({
        theme: state.theme,
        language: state.language,
        collapsed: state.collapsed,
      }),
    }
  )
);
```

### 9.2.3 React Query配置

配置数据获取和缓存管理：

```typescript
// src/services/queryClient.ts
import { QueryClient } from '@tanstack/react-query';
import { message } from 'antd';

// 创建QueryClient实例
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // 数据缓存时间
      staleTime: 5 * 60 * 1000, // 5分钟
      // 缓存保持时间
      cacheTime: 10 * 60 * 1000, // 10分钟
      // 重试配置
      retry: (failureCount, error: any) => {
        // 4xx错误不重试
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false;
        }
        return failureCount < 3;
      },
      // 重新获取配置
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      onError: (error: any) => {
        message.error(error?.message || '操作失败');
      },
    },
  },
});
```

---

## 9.3 路由配置与权限控制

### 9.3.1 路由配置

使用React Router 6.x配置应用路由：

```typescript
// src/router/index.tsx
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { lazy, Suspense } from 'react';
import { Spin } from 'antd';
import AuthGuard from '@components/AuthGuard';
import Layout from '@components/Layout';
import { UserRole } from '@types/index';

// 懒加载组件
const Login = lazy(() => import('@pages/Auth/Login'));
const Register = lazy(() => import('@pages/Auth/Register'));
const ForgotPassword = lazy(() => import('@pages/Auth/ForgotPassword'));

// 用户端页面
const UserDashboard = lazy(() => import('@pages/User/Dashboard'));
const UserProfile = lazy(() => import('@pages/User/Profile'));
const UserOrders = lazy(() => import('@pages/User/Orders'));
const UserServices = lazy(() => import('@pages/User/Services'));
const UserMessages = lazy(() => import('@pages/User/Messages'));

// 服务商端页面
const ProviderDashboard = lazy(() => import('@pages/Provider/Dashboard'));
const ProviderProfile = lazy(() => import('@pages/Provider/Profile'));
const ProviderOrders = lazy(() => import('@pages/Provider/Orders'));
const ProviderServices = lazy(() => import('@pages/Provider/Services'));
const ProviderEarnings = lazy(() => import('@pages/Provider/Earnings'));
const ProviderMessages = lazy(() => import('@pages/Provider/Messages'));

// 管理员端页面
const AdminDashboard = lazy(() => import('@pages/Admin/Dashboard'));
const AdminUsers = lazy(() => import('@pages/Admin/Users'));
const AdminProviders = lazy(() => import('@pages/Admin/Providers'));
const AdminOrders = lazy(() => import('@pages/Admin/Orders'));
const AdminServices = lazy(() => import('@pages/Admin/Services'));
const AdminReports = lazy(() => import('@pages/Admin/Reports'));

// 加载组件
const LoadingComponent = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '200px'
  }}>
    <Spin size="large" />
  </div>
);

// 路由配置
export const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to="/user/dashboard" replace />,
  },

  // 认证相关路由
  {
    path: '/login',
    element: (
      <Suspense fallback={<LoadingComponent />}>
        <Login />
      </Suspense>
    ),
  },
  {
    path: '/register',
    element: (
      <Suspense fallback={<LoadingComponent />}>
        <Register />
      </Suspense>
    ),
  },
  {
    path: '/forgot-password',
    element: (
      <Suspense fallback={<LoadingComponent />}>
        <ForgotPassword />
      </Suspense>
    ),
  },

  // 用户端路由
  {
    path: '/user',
    element: (
      <AuthGuard allowedRoles={[UserRole.USER]}>
        <Layout userType="user" />
      </AuthGuard>
    ),
    children: [
      {
        path: 'dashboard',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <UserDashboard />
          </Suspense>
        ),
      },
      {
        path: 'profile',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <UserProfile />
          </Suspense>
        ),
      },
      {
        path: 'orders',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <UserOrders />
          </Suspense>
        ),
      },
      {
        path: 'services',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <UserServices />
          </Suspense>
        ),
      },
      {
        path: 'messages',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <UserMessages />
          </Suspense>
        ),
      },
    ],
  },

  // 服务商端路由
  {
    path: '/provider',
    element: (
      <AuthGuard allowedRoles={[UserRole.SERVICE_PROVIDER]}>
        <Layout userType="provider" />
      </AuthGuard>
    ),
    children: [
      {
        path: 'dashboard',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <ProviderDashboard />
          </Suspense>
        ),
      },
      {
        path: 'profile',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <ProviderProfile />
          </Suspense>
        ),
      },
      {
        path: 'orders',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <ProviderOrders />
          </Suspense>
        ),
      },
      {
        path: 'services',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <ProviderServices />
          </Suspense>
        ),
      },
      {
        path: 'earnings',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <ProviderEarnings />
          </Suspense>
        ),
      },
      {
        path: 'messages',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <ProviderMessages />
          </Suspense>
        ),
      },
    ],
  },

  // 管理员端路由
  {
    path: '/admin',
    element: (
      <AuthGuard allowedRoles={[UserRole.ADMIN]}>
        <Layout userType="admin" />
      </AuthGuard>
    ),
    children: [
      {
        path: 'dashboard',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <AdminDashboard />
          </Suspense>
        ),
      },
      {
        path: 'users',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <AdminUsers />
          </Suspense>
        ),
      },
      {
        path: 'providers',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <AdminProviders />
          </Suspense>
        ),
      },
      {
        path: 'orders',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <AdminOrders />
          </Suspense>
        ),
      },
      {
        path: 'services',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <AdminServices />
          </Suspense>
        ),
      },
      {
        path: 'reports',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <AdminReports />
          </Suspense>
        ),
      },
    ],
  },

  // 404页面
  {
    path: '*',
    element: <div>页面不存在</div>,
  },
]);
```

### 9.3.2 权限守卫组件

实现路由权限控制：

```typescript
// src/components/AuthGuard/index.tsx
import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { useAuthStore } from '@stores/authStore';
import { UserRole } from '@types/index';

interface AuthGuardProps {
  children: React.ReactNode;
  allowedRoles?: UserRole[];
  requireAuth?: boolean;
}

const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  allowedRoles = [],
  requireAuth = true,
}) => {
  const location = useLocation();
  const { user, isAuthenticated, isLoading } = useAuthStore();

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
      }}>
        <Spin size="large" />
      </div>
    );
  }

  // 如果需要认证但用户未登录，跳转到登录页
  if (requireAuth && !isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 如果指定了角色要求，检查用户角色
  if (allowedRoles.length > 0 && user) {
    if (!allowedRoles.includes(user.role)) {
      // 根据用户角色跳转到对应的默认页面
      const defaultRoutes = {
        [UserRole.USER]: '/user/dashboard',
        [UserRole.SERVICE_PROVIDER]: '/provider/dashboard',
        [UserRole.ADMIN]: '/admin/dashboard',
      };
      return <Navigate to={defaultRoutes[user.role]} replace />;
    }
  }

  return <>{children}</>;
};

export default AuthGuard;
```

### 9.3.3 应用入口文件

配置应用的根组件和提供者：

```typescript
// src/App.tsx
import React from 'react';
import { RouterProvider } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { router } from '@/router';
import { queryClient } from '@services/queryClient';
import { useAppStore } from '@stores/appStore';
import '@styles/global.less';

// 设置dayjs中文
dayjs.locale('zh-cn');

const App: React.FC = () => {
  const { theme, language } = useAppStore();

  return (
    <QueryClientProvider client={queryClient}>
      <ConfigProvider
        locale={language === 'zh-CN' ? zhCN : undefined}
        theme={{
          algorithm: theme === 'dark' ? undefined : undefined, // 可以配置暗色主题
          token: {
            colorPrimary: '#1890ff',
            borderRadius: 6,
          },
        }}
      >
        <AntdApp>
          <RouterProvider router={router} />
        </AntdApp>
      </ConfigProvider>

      {/* 开发环境显示React Query调试工具 */}
      {import.meta.env.DEV && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  );
};

export default App;
```

```typescript
// src/main.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

// 创建根节点并渲染应用
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
```

### 9.3.4 全局样式配置

```less
// src/styles/global.less
// 全局样式定义

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  font-size: 14px;
  line-height: 1.5715;
  color: rgba(0, 0, 0, 0.85);
  background-color: #f0f2f5;
}

#root {
  height: 100%;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 通用工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.full-height {
  height: 100%;
}

.full-width {
  width: 100%;
}

// 间距工具类
.m-0 { margin: 0; }
.m-1 { margin: 8px; }
.m-2 { margin: 16px; }
.m-3 { margin: 24px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 8px; }
.ml-2 { margin-left: 16px; }
.ml-3 { margin-left: 24px; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 8px; }
.mr-2 { margin-right: 16px; }
.mr-3 { margin-right: 24px; }

.p-0 { padding: 0; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 8px; }
.pt-2 { padding-top: 16px; }
.pt-3 { padding-top: 24px; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 8px; }
.pb-2 { padding-bottom: 16px; }
.pb-3 { padding-bottom: 24px; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 8px; }
.pl-2 { padding-left: 16px; }
.pl-3 { padding-left: 24px; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 8px; }
.pr-2 { padding-right: 16px; }
.pr-3 { padding-right: 24px; }

// 响应式断点
@media (max-width: 576px) {
  .hidden-xs {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .hidden-sm {
    display: none !important;
  }
}

@media (max-width: 992px) {
  .hidden-md {
    display: none !important;
  }
}

@media (max-width: 1200px) {
  .hidden-lg {
    display: none !important;
  }
}
```

## 总结

本篇教程完成了React前端项目的初始化与基础配置，包括：

### 主要功能

1. **项目初始化**
   - Vite + React + TypeScript项目搭建
   - 核心依赖包安装和配置
   - 开发环境优化配置

2. **HTTP请求与状态管理**
   - Axios请求拦截器配置
   - Zustand轻量级状态管理
   - React Query数据缓存管理

3. **路由配置与权限控制**
   - React Router 6.x路由配置
   - 懒加载和代码分割
   - 基于角色的权限控制

### 技术亮点

- **现代化技术栈**: Vite + React 18 + TypeScript
- **组件化设计**: 模块化的项目结构
- **类型安全**: 完整的TypeScript类型定义
- **性能优化**: 懒加载、代码分割、缓存策略

### 下一步

在下一篇教程中，我们将实现用户端界面，包括登录注册、服务浏览、订单管理等核心功能页面。
