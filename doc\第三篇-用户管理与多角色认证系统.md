### **《新一代在线家政维修服务平台：从需求到实现的企业级开发实战》**

#### **第三篇：用户管理与多角色认证系统**

**摘要：** 基于需求规格说明书中的用户管理需求（FR-UM-001至FR-UM-007），本篇将构建一个完整的多角色用户管理系统。我们将实现用户注册、登录认证、JWT权限控制、用户个人中心等核心功能，支持终端用户、服务商、管理员三种角色的统一管理。

---

## **3.1 统一用户体系构建**

### **用户模块结构设计**

首先创建用户管理模块的完整结构：

```bash
# 创建用户模块
nest g module modules/users
nest g controller modules/users
nest g service modules/users

# 创建认证模块
nest g module modules/auth
nest g controller modules/auth
nest g service modules/auth
```

### **用户数据传输对象（DTO）**

```typescript
// src/modules/users/dto/create-user.dto.ts
import { IsEmail, IsEnum, IsOptional, IsPhoneNumber, IsString, MinLength, MaxLength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { UserRole } from '../../../database/entities/user.entity';

export class CreateUserDto {
  @ApiProperty({ description: '用户名', example: 'john_doe' })
  @IsString()
  @MinLength(3, { message: '用户名至少3个字符' })
  @MaxLength(50, { message: '用户名最多50个字符' })
  username: string;

  @ApiProperty({ description: '手机号', example: '13800138000' })
  @IsPhoneNumber('CN', { message: '请输入有效的中国大陆手机号' })
  phone: string;

  @ApiProperty({ description: '邮箱', example: '<EMAIL>', required: false })
  @IsEmail({}, { message: '请输入有效的邮箱地址' })
  @IsOptional()
  email?: string;

  @ApiProperty({ description: '密码', example: 'password123' })
  @IsString()
  @MinLength(6, { message: '密码至少6个字符' })
  @MaxLength(50, { message: '密码最多50个字符' })
  password: string;

  @ApiProperty({ description: '用户角色', enum: UserRole, example: UserRole.CUSTOMER })
  @IsEnum(UserRole, { message: '无效的用户角色' })
  @IsOptional()
  role?: UserRole = UserRole.CUSTOMER;

  @ApiProperty({ description: '真实姓名', example: '张三', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(50, { message: '真实姓名最多50个字符' })
  realName?: string;
}

// src/modules/users/dto/login.dto.ts
import { IsString, MinLength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({ description: '用户名或手机号', example: 'john_doe' })
  @IsString()
  usernameOrPhone: string;

  @ApiProperty({ description: '密码', example: 'password123' })
  @IsString()
  @MinLength(6, { message: '密码至少6个字符' })
  password: string;
}

// src/modules/users/dto/update-user.dto.ts
import { PartialType, OmitType } from '@nestjs/swagger';
import { CreateUserDto } from './create-user.dto';

export class UpdateUserDto extends PartialType(
  OmitType(CreateUserDto, ['password', 'role'] as const)
) {}

// src/modules/users/dto/change-password.dto.ts
import { IsString, MinLength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ChangePasswordDto {
  @ApiProperty({ description: '当前密码' })
  @IsString()
  currentPassword: string;

  @ApiProperty({ description: '新密码' })
  @IsString()
  @MinLength(6, { message: '新密码至少6个字符' })
  newPassword: string;
}
```

### **用户服务层实现**

```typescript
// src/modules/users/users.service.ts
import { Injectable, ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { User, UserRole, UserStatus } from '../../database/entities/user.entity';
import { UserProfile } from '../../database/entities/user-profile.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ChangePasswordDto } from './dto/change-password.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserProfile)
    private readonly userProfileRepository: Repository<UserProfile>,
  ) {}

  /**
   * 创建新用户
   * 实现需求：FR-UM-001, FR-UM-002
   */
  async create(createUserDto: CreateUserDto): Promise<User> {
    const { username, phone, email, password, ...userData } = createUserDto;

    // 检查用户名、手机号、邮箱是否已存在
    const existingUser = await this.userRepository.findOne({
      where: [
        { username },
        { phone },
        ...(email ? [{ email }] : []),
      ],
    });

    if (existingUser) {
      if (existingUser.username === username) {
        throw new ConflictException('用户名已存在');
      }
      if (existingUser.phone === phone) {
        throw new ConflictException('手机号已存在');
      }
      if (existingUser.email === email) {
        throw new ConflictException('邮箱已存在');
      }
    }

    // 加密密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const user = this.userRepository.create({
      username,
      phone,
      email,
      passwordHash,
      ...userData,
    });

    const savedUser = await this.userRepository.save(user);

    // 创建用户详细信息
    const profile = this.userProfileRepository.create({
      userId: savedUser.id,
      nickname: username,
    });
    await this.userProfileRepository.save(profile);

    return this.findById(savedUser.id);
  }

  /**
   * 根据ID查找用户
   */
  async findById(id: number): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['profile', 'serviceProvider'],
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    return user;
  }

  /**
   * 根据用户名或手机号查找用户
   * 实现需求：FR-UM-003
   */
  async findByUsernameOrPhone(usernameOrPhone: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: [
        { username: usernameOrPhone },
        { phone: usernameOrPhone },
      ],
      relations: ['profile', 'serviceProvider'],
    });
  }

  /**
   * 验证用户密码
   */
  async validatePassword(user: User, password: string): Promise<boolean> {
    return bcrypt.compare(password, user.passwordHash);
  }

  /**
   * 更新用户信息
   * 实现需求：FR-UM-004
   */
  async update(id: number, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findById(id);

    // 检查用户名、手机号、邮箱的唯一性
    if (updateUserDto.username || updateUserDto.phone || updateUserDto.email) {
      const existingUser = await this.userRepository.findOne({
        where: [
          ...(updateUserDto.username ? [{ username: updateUserDto.username }] : []),
          ...(updateUserDto.phone ? [{ phone: updateUserDto.phone }] : []),
          ...(updateUserDto.email ? [{ email: updateUserDto.email }] : []),
        ],
      });

      if (existingUser && existingUser.id !== id) {
        throw new ConflictException('用户名、手机号或邮箱已存在');
      }
    }

    await this.userRepository.update(id, updateUserDto);
    return this.findById(id);
  }

  /**
   * 修改密码
   */
  async changePassword(id: number, changePasswordDto: ChangePasswordDto): Promise<void> {
    const user = await this.findById(id);

    // 验证当前密码
    const isCurrentPasswordValid = await this.validatePassword(user, changePasswordDto.currentPassword);
    if (!isCurrentPasswordValid) {
      throw new BadRequestException('当前密码错误');
    }

    // 加密新密码
    const saltRounds = 12;
    const newPasswordHash = await bcrypt.hash(changePasswordDto.newPassword, saltRounds);

    await this.userRepository.update(id, { passwordHash: newPasswordHash });
  }

  /**
   * 更新用户状态
   * 实现需求：FR-UM-007
   */
  async updateStatus(id: number, status: UserStatus): Promise<User> {
    await this.userRepository.update(id, { status });
    return this.findById(id);
  }

  /**
   * 分页查询用户列表
   */
  async findAll(
    page: number = 1,
    limit: number = 10,
    role?: UserRole,
    status?: UserStatus,
    search?: string,
  ) {
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.profile', 'profile')
      .leftJoinAndSelect('user.serviceProvider', 'serviceProvider')
      .orderBy('user.createdAt', 'DESC');

    if (role) {
      queryBuilder.andWhere('user.role = :role', { role });
    }

    if (status) {
      queryBuilder.andWhere('user.status = :status', { status });
    }

    if (search) {
      queryBuilder.andWhere(
        '(user.username LIKE :search OR user.phone LIKE :search OR user.realName LIKE :search)',
        { search: `%${search}%` }
      );
    }

    const [users, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      data: users,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * 软删除用户
   */
  async remove(id: number): Promise<void> {
    const user = await this.findById(id);
    await this.userRepository.update(id, { status: UserStatus.INACTIVE });
  }
}
```

## **3.2 JWT认证与权限控制**

### **JWT策略配置**

```typescript
// src/modules/auth/strategies/jwt.strategy.ts
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../../users/users.service';

export interface JwtPayload {
  sub: number;
  username: string;
  role: string;
  iat?: number;
  exp?: number;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly configService: ConfigService,
    private readonly usersService: UsersService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
    });
  }

  async validate(payload: JwtPayload) {
    const user = await this.usersService.findById(payload.sub);
    
    if (!user || user.status !== 'active') {
      throw new UnauthorizedException('用户不存在或已被禁用');
    }

    return user;
  }
}

// src/modules/auth/strategies/local.strategy.ts
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { AuthService } from '../auth.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly authService: AuthService) {
    super({
      usernameField: 'usernameOrPhone',
      passwordField: 'password',
    });
  }

  async validate(usernameOrPhone: string, password: string) {
    const user = await this.authService.validateUser(usernameOrPhone, password);
    
    if (!user) {
      throw new UnauthorizedException('用户名或密码错误');
    }

    return user;
  }
}
```

### **基于角色的访问控制（RBAC）**

```typescript
// src/common/decorators/roles.decorator.ts
import { SetMetadata } from '@nestjs/common';
import { UserRole } from '../../database/entities/user.entity';

export const ROLES_KEY = 'roles';
export const Roles = (...roles: UserRole[]) => SetMetadata(ROLES_KEY, roles);

// src/common/guards/roles.guard.ts
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserRole } from '../../database/entities/user.entity';
import { ROLES_KEY } from '../decorators/roles.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<UserRole[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.role === role);
  }
}

// src/common/decorators/current-user.decorator.ts
import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { User } from '../../database/entities/user.entity';

export const CurrentUser = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): User => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
  },
);
```

### **认证服务实现**

```typescript
// src/modules/auth/auth.service.ts
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { User } from '../../database/entities/user.entity';
import { JwtPayload } from './strategies/jwt.strategy';

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
  ) {}

  /**
   * 验证用户凭据
   * 实现需求：FR-UM-003
   */
  async validateUser(usernameOrPhone: string, password: string): Promise<User | null> {
    const user = await this.usersService.findByUsernameOrPhone(usernameOrPhone);
    
    if (!user) {
      return null;
    }

    const isPasswordValid = await this.usersService.validatePassword(user, password);
    
    if (!isPasswordValid) {
      return null;
    }

    if (user.status !== 'active') {
      throw new UnauthorizedException('账户已被禁用');
    }

    return user;
  }

  /**
   * 用户登录
   */
  async login(user: User) {
    const payload: JwtPayload = {
      sub: user.id,
      username: user.username,
      role: user.role,
    };

    return {
      access_token: this.jwtService.sign(payload),
      token_type: 'Bearer',
      expires_in: '7d',
      user: {
        id: user.id,
        username: user.username,
        phone: user.phone,
        email: user.email,
        role: user.role,
        status: user.status,
        realName: user.realName,
        avatarUrl: user.avatarUrl,
        profile: user.profile,
        serviceProvider: user.serviceProvider,
      },
    };
  }

  /**
   * 获取当前用户信息
   */
  async getProfile(userId: number) {
    return this.usersService.findById(userId);
  }

  /**
   * 刷新Token
   */
  async refreshToken(user: User) {
    return this.login(user);
  }
}
```

## **3.3 用户个人中心功能**

### **用户控制器实现**

```typescript
// src/modules/users/users.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { User, UserRole, UserStatus } from '../../database/entities/user.entity';

@ApiTags('用户管理')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post('register')
  @ApiOperation({ summary: '用户注册' })
  @ApiResponse({ status: 201, description: '注册成功' })
  async register(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取当前用户信息' })
  async getProfile(@CurrentUser() user: User) {
    return this.usersService.findById(user.id);
  }

  @Put('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新用户信息' })
  async updateProfile(
    @CurrentUser() user: User,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    return this.usersService.update(user.id, updateUserDto);
  }

  @Put('change-password')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '修改密码' })
  async changePassword(
    @CurrentUser() user: User,
    @Body() changePasswordDto: ChangePasswordDto,
  ) {
    await this.usersService.changePassword(user.id, changePasswordDto);
    return { message: '密码修改成功' };
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取用户列表（管理员）' })
  async findAll(
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('limit', ParseIntPipe) limit: number = 10,
    @Query('role') role?: UserRole,
    @Query('status') status?: UserStatus,
    @Query('search') search?: string,
  ) {
    return this.usersService.findAll(page, limit, role, status, search);
  }

  @Put(':id/status')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新用户状态（管理员）' })
  async updateStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body('status') status: UserStatus,
  ) {
    return this.usersService.updateStatus(id, status);
  }
}
```

通过本篇教程，我们构建了一个完整的多角色用户管理和认证系统，完全满足需求规格说明书中的用户管理需求。系统支持安全的用户注册、登录、权限控制和个人信息管理功能。

---

**[请告诉我"继续"，我将提供第四篇：服务商管理与多阶段审核系统。]**
