### **《新一代在线家政维修服务平台：从零构建企业级O2O服务生态》**

#### **第五篇：服务商管理与审核系统——构建可信的服务商生态**

**摘要：** 服务商是平台的核心资源，服务商的质量直接决定了平台的服务水平和用户满意度。本篇将详细讲解如何构建一个完整的服务商管理系统，包括服务商注册、多阶段审核流程、资质认证、技能管理、服务区域设置等关键功能。

---

**一、服务商实体设计**

### 1. 服务商主体实体

```typescript
// src/modules/providers/entities/service-provider.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { ProviderCertification } from './provider-certification.entity';
import { ProviderService } from './provider-service.entity';

export enum CertificationStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

@Entity('service_providers')
export class ServiceProvider {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'user_id', type: 'bigint' })
  userId: number;

  @Column({ name: 'business_name', length: 100, nullable: true })
  businessName: string;

  @Column({ name: 'business_license', length: 100, nullable: true })
  businessLicense: string;

  @Column({
    name: 'certification_status',
    type: 'enum',
    enum: CertificationStatus,
    default: CertificationStatus.PENDING,
  })
  certificationStatus: CertificationStatus;

  @Column({ name: 'certification_note', type: 'text', nullable: true })
  certificationNote: string;

  @Column({ name: 'service_radius', type: 'int', default: 10 })
  serviceRadius: number;

  @Column({ name: 'base_longitude', type: 'decimal', precision: 10, scale: 7, nullable: true })
  baseLongitude: number;

  @Column({ name: 'base_latitude', type: 'decimal', precision: 10, scale: 7, nullable: true })
  baseLatitude: number;

  @Column({ name: 'working_hours', type: 'json', nullable: true })
  workingHours: Record<string, any>;

  @Column({ type: 'text', nullable: true })
  introduction: string;

  @Column({ name: 'experience_years', type: 'int', default: 0 })
  experienceYears: number;

  @Column({ name: 'total_orders', type: 'int', default: 0 })
  totalOrders: number;

  @Column({ name: 'rating_average', type: 'decimal', precision: 3, scale: 2, default: 0.00 })
  ratingAverage: number;

  @Column({ name: 'rating_count', type: 'int', default: 0 })
  ratingCount: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @OneToOne(() => User, (user) => user.serviceProvider, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany(() => ProviderCertification, (cert) => cert.provider, { cascade: true })
  certifications: ProviderCertification[];

  @OneToMany(() => ProviderService, (service) => service.provider, { cascade: true })
  services: ProviderService[];
}
```

### 2. 服务商认证实体

```typescript
// src/modules/providers/entities/provider-certification.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ServiceProvider } from './service-provider.entity';

export enum CertificationStatus {
  VALID = 'valid',
  EXPIRED = 'expired',
  REVOKED = 'revoked',
}

@Entity('provider_certifications')
export class ProviderCertification {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'provider_id', type: 'bigint' })
  providerId: number;

  @Column({ name: 'certification_type', length: 50 })
  certificationType: string;

  @Column({ name: 'certification_name', length: 100 })
  certificationName: string;

  @Column({ name: 'certificate_number', length: 100, nullable: true })
  certificateNumber: string;

  @Column({ name: 'certificate_url', length: 500, nullable: true })
  certificateUrl: string;

  @Column({ name: 'issue_date', type: 'date', nullable: true })
  issueDate: Date;

  @Column({ name: 'expire_date', type: 'date', nullable: true })
  expireDate: Date;

  @Column({
    type: 'enum',
    enum: CertificationStatus,
    default: CertificationStatus.VALID,
  })
  status: CertificationStatus;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => ServiceProvider, (provider) => provider.certifications, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'provider_id' })
  provider: ServiceProvider;
}
```

### 3. 服务商服务关联实体

```typescript
// src/modules/providers/entities/provider-service.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ServiceProvider } from './service-provider.entity';
import { ServiceItem } from '../../services/entities/service-item.entity';

@Entity('provider_services')
export class ProviderService {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'provider_id', type: 'bigint' })
  providerId: number;

  @Column({ name: 'service_item_id', type: 'bigint' })
  serviceItemId: number;

  @Column({ name: 'custom_price', type: 'decimal', precision: 10, scale: 2, nullable: true })
  customPrice: number;

  @Column({ name: 'is_available', type: 'boolean', default: true })
  isAvailable: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => ServiceProvider, (provider) => provider.services, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'provider_id' })
  provider: ServiceProvider;

  @ManyToOne(() => ServiceItem, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'service_item_id' })
  serviceItem: ServiceItem;
}
```

**二、服务商数据传输对象**

### 1. 服务商注册DTO

```typescript
// src/modules/providers/dto/create-provider.dto.ts
import { IsString, IsOptional, IsNumber, IsEnum, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class WorkingHoursDto {
  @ApiProperty({ description: '星期几 (0-6, 0为周日)', example: 1 })
  @IsNumber()
  dayOfWeek: number;

  @ApiProperty({ description: '开始时间', example: '09:00' })
  @IsString()
  startTime: string;

  @ApiProperty({ description: '结束时间', example: '18:00' })
  @IsString()
  endTime: string;

  @ApiProperty({ description: '是否可用', example: true })
  @IsOptional()
  isAvailable?: boolean = true;
}

export class CreateProviderDto {
  @ApiProperty({ description: '商户名称', example: '张师傅家电维修' })
  @IsOptional()
  @IsString()
  businessName?: string;

  @ApiProperty({ description: '营业执照号', example: '91110000123456789X' })
  @IsOptional()
  @IsString()
  businessLicense?: string;

  @ApiProperty({ description: '服务半径(公里)', example: 15 })
  @IsOptional()
  @IsNumber()
  serviceRadius?: number = 10;

  @ApiProperty({ description: '服务基地经度', example: 116.397128 })
  @IsOptional()
  @IsNumber()
  baseLongitude?: number;

  @ApiProperty({ description: '服务基地纬度', example: 39.916527 })
  @IsOptional()
  @IsNumber()
  baseLatitude?: number;

  @ApiProperty({ description: '工作时间设置', type: [WorkingHoursDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkingHoursDto)
  workingHours?: WorkingHoursDto[];

  @ApiProperty({ description: '服务商介绍', example: '专业家电维修，10年经验' })
  @IsOptional()
  @IsString()
  introduction?: string;

  @ApiProperty({ description: '从业年限', example: 10 })
  @IsOptional()
  @IsNumber()
  experienceYears?: number = 0;
}
```

### 2. 认证申请DTO

```typescript
// src/modules/providers/dto/create-certification.dto.ts
import { IsString, IsOptional, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCertificationDto {
  @ApiProperty({ description: '认证类型', example: 'skill' })
  @IsString()
  certificationType: string;

  @ApiProperty({ description: '认证名称', example: '家电维修技师证' })
  @IsString()
  certificationName: string;

  @ApiProperty({ description: '证书编号', example: 'CERT123456' })
  @IsOptional()
  @IsString()
  certificateNumber?: string;

  @ApiProperty({ description: '证书图片URL', example: 'https://example.com/cert.jpg' })
  @IsOptional()
  @IsString()
  certificateUrl?: string;

  @ApiProperty({ description: '颁发日期', example: '2020-01-01' })
  @IsOptional()
  @IsDateString()
  issueDate?: string;

  @ApiProperty({ description: '过期日期', example: '2025-01-01' })
  @IsOptional()
  @IsDateString()
  expireDate?: string;
}
```

**三、服务商服务层实现**

### 1. 服务商基础服务

```typescript
// src/modules/providers/providers.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ServiceProvider, CertificationStatus } from './entities/service-provider.entity';
import { ProviderCertification } from './entities/provider-certification.entity';
import { CreateProviderDto } from './dto/create-provider.dto';
import { UpdateProviderDto } from './dto/update-provider.dto';
import { CreateCertificationDto } from './dto/create-certification.dto';

@Injectable()
export class ProvidersService {
  constructor(
    @InjectRepository(ServiceProvider)
    private readonly providerRepository: Repository<ServiceProvider>,
    @InjectRepository(ProviderCertification)
    private readonly certificationRepository: Repository<ProviderCertification>,
  ) {}

  /**
   * 创建服务商档案
   */
  async create(userId: number, createProviderDto: CreateProviderDto): Promise<ServiceProvider> {
    // 检查用户是否已经是服务商
    const existingProvider = await this.providerRepository.findOne({
      where: { userId },
    });

    if (existingProvider) {
      throw new ConflictException('用户已经是服务商');
    }

    const provider = this.providerRepository.create({
      userId,
      ...createProviderDto,
      workingHours: createProviderDto.workingHours || this.getDefaultWorkingHours(),
    });

    return this.providerRepository.save(provider);
  }

  /**
   * 获取默认工作时间
   */
  private getDefaultWorkingHours() {
    return Array.from({ length: 7 }, (_, index) => ({
      dayOfWeek: index,
      startTime: '09:00',
      endTime: '18:00',
      isAvailable: index >= 1 && index <= 5, // 周一到周五
    }));
  }

  /**
   * 根据ID查找服务商
   */
  async findById(id: number): Promise<ServiceProvider> {
    const provider = await this.providerRepository.findOne({
      where: { id },
      relations: ['user', 'certifications', 'services', 'services.serviceItem'],
    });

    if (!provider) {
      throw new NotFoundException('服务商不存在');
    }

    return provider;
  }

  /**
   * 根据用户ID查找服务商
   */
  async findByUserId(userId: number): Promise<ServiceProvider | null> {
    return this.providerRepository.findOne({
      where: { userId },
      relations: ['user', 'certifications', 'services'],
    });
  }

  /**
   * 更新服务商信息
   */
  async update(id: number, updateProviderDto: UpdateProviderDto): Promise<ServiceProvider> {
    const provider = await this.findById(id);
    
    await this.providerRepository.update(id, updateProviderDto);
    return this.findById(id);
  }

  /**
   * 添加认证信息
   */
  async addCertification(
    providerId: number,
    createCertificationDto: CreateCertificationDto,
  ): Promise<ProviderCertification> {
    const provider = await this.findById(providerId);

    const certification = this.certificationRepository.create({
      providerId,
      ...createCertificationDto,
      issueDate: createCertificationDto.issueDate ? new Date(createCertificationDto.issueDate) : null,
      expireDate: createCertificationDto.expireDate ? new Date(createCertificationDto.expireDate) : null,
    });

    return this.certificationRepository.save(certification);
  }

  /**
   * 审核服务商
   */
  async reviewProvider(
    id: number,
    status: CertificationStatus,
    note?: string,
  ): Promise<ServiceProvider> {
    const provider = await this.findById(id);

    if (provider.certificationStatus === CertificationStatus.APPROVED) {
      throw new BadRequestException('服务商已通过审核');
    }

    await this.providerRepository.update(id, {
      certificationStatus: status,
      certificationNote: note,
    });

    return this.findById(id);
  }

  /**
   * 分页查询服务商列表
   */
  async findAll(
    page: number = 1,
    limit: number = 10,
    status?: CertificationStatus,
    city?: string,
  ) {
    const queryBuilder = this.providerRepository
      .createQueryBuilder('provider')
      .leftJoinAndSelect('provider.user', 'user')
      .leftJoinAndSelect('provider.certifications', 'certifications')
      .orderBy('provider.createdAt', 'DESC');

    if (status) {
      queryBuilder.andWhere('provider.certificationStatus = :status', { status });
    }

    if (city) {
      queryBuilder
        .leftJoinAndSelect('user.profile', 'profile')
        .andWhere('profile.city = :city', { city });
    }

    const [providers, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      data: providers,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * 搜索附近的服务商
   */
  async findNearbyProviders(
    longitude: number,
    latitude: number,
    radius: number = 10,
    serviceItemId?: number,
  ) {
    const queryBuilder = this.providerRepository
      .createQueryBuilder('provider')
      .leftJoinAndSelect('provider.user', 'user')
      .leftJoinAndSelect('provider.services', 'services')
      .leftJoinAndSelect('services.serviceItem', 'serviceItem')
      .where('provider.certificationStatus = :status', { status: CertificationStatus.APPROVED })
      .andWhere(
        `(6371 * acos(cos(radians(:latitude)) * cos(radians(provider.baseLatitude)) * 
         cos(radians(provider.baseLongitude) - radians(:longitude)) + 
         sin(radians(:latitude)) * sin(radians(provider.baseLatitude)))) <= :radius`,
        { latitude, longitude, radius },
      );

    if (serviceItemId) {
      queryBuilder.andWhere('services.serviceItemId = :serviceItemId', { serviceItemId });
    }

    return queryBuilder
      .orderBy('provider.ratingAverage', 'DESC')
      .addOrderBy('provider.totalOrders', 'DESC')
      .getMany();
  }

  /**
   * 更新服务商评分
   */
  async updateRating(providerId: number, newRating: number): Promise<void> {
    const provider = await this.findById(providerId);
    
    const totalRating = provider.ratingAverage * provider.ratingCount + newRating;
    const newRatingCount = provider.ratingCount + 1;
    const newRatingAverage = totalRating / newRatingCount;

    await this.providerRepository.update(providerId, {
      ratingAverage: Math.round(newRatingAverage * 100) / 100,
      ratingCount: newRatingCount,
    });
  }
}
```

**四、审核工作流实现**

### 1. 审核服务

```typescript
// src/modules/providers/review.service.ts
import { Injectable } from '@nestjs/common';
import { ProvidersService } from './providers.service';
import { CertificationStatus } from './entities/service-provider.entity';

export interface ReviewDecision {
  status: CertificationStatus;
  note?: string;
  reviewerId: number;
}

@Injectable()
export class ReviewService {
  constructor(private readonly providersService: ProvidersService) {}

  /**
   * 自动审核检查
   */
  async autoReview(providerId: number): Promise<ReviewDecision | null> {
    const provider = await this.providersService.findById(providerId);
    
    // 基础信息完整性检查
    const hasBasicInfo = provider.businessName && 
                        provider.introduction && 
                        provider.baseLongitude && 
                        provider.baseLatitude;

    // 认证信息检查
    const hasCertifications = provider.certifications && provider.certifications.length > 0;

    if (!hasBasicInfo) {
      return {
        status: CertificationStatus.REJECTED,
        note: '基础信息不完整，请补充商户名称、介绍和服务地址',
        reviewerId: 0, // 系统自动审核
      };
    }

    if (!hasCertifications) {
      return {
        status: CertificationStatus.REJECTED,
        note: '请上传相关技能认证证书',
        reviewerId: 0,
      };
    }

    // 如果通过基础检查，进入人工审核
    return null;
  }

  /**
   * 人工审核
   */
  async manualReview(
    providerId: number,
    reviewerId: number,
    decision: ReviewDecision,
  ): Promise<void> {
    await this.providersService.reviewProvider(
      providerId,
      decision.status,
      decision.note,
    );

    // 这里可以添加审核日志记录
    // await this.auditLogService.log({
    //   action: 'provider_review',
    //   targetId: providerId,
    //   reviewerId,
    //   decision,
    // });
  }
}
```

通过本篇教程，我们构建了一个完整的服务商管理和审核系统，包括服务商注册、资质认证、多阶段审核流程等核心功能。这确保了平台上服务商的质量和可信度。

---

**[请告诉我"继续"，我将提供第六篇：服务分类与项目管理系统。]**
