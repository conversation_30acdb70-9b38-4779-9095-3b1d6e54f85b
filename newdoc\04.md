好的，我们继续进行定制化系列教程的第四篇。在之前的篇章中，我们已经完成了项目初始化、数据库基础设计、以及用户和技师的身份认证与权限管理。现在，我们将进入系统的核心业务逻辑——服务管理和订单管理。

---

### **《新一代在线家政维修服务平台：从需求到实现的高效开发实战》**

#### **定制化系列教程 第四篇：核心业务模块一：服务与订单管理**

**摘要：** 本篇教程将聚焦于“家维在线系统”的服务和订单两大核心模块。我们将实现服务类别的创建与管理、服务的增删改查，并为用户提供创建订单、查看订单列表与详情，以及取消订单的功能。这将是系统业务流程的关键部分。

---

### **4.1 服务管理模块 (Service Module)**

服务管理模块将涵盖对维修服务及其分类的增删改查操作。根据您的设计文档，“服务分类管理”和“服务信息管理”是分开的，我们将分别实现。

**1. 创建服务分类模块：**

```bash
nest generate module serviceCategory
nest generate service serviceCategory
nest generate controller serviceCategory
```

**2. 配置 ServiceCategoryModule (src/serviceCategory/service-category.module.ts):**

```typescript
// src/serviceCategory/service-category.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServiceCategoryService } from './service-category.service';
import { ServiceCategoryController } from './service-category.controller';
import { ServiceCategory } from './entities/service-category.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ServiceCategory])],
  controllers: [ServiceCategoryController],
  providers: [ServiceCategoryService],
  exports: [ServiceCategoryService], // 导出 ServiceCategoryService 以便其他模块使用
})
export class ServiceCategoryModule {}
```

**3. 实现 ServiceCategoryService (src/serviceCategory/service-category.service.ts):**

```typescript
// src/serviceCategory/service-category.service.ts
import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ServiceCategory } from './entities/service-category.entity';
import { CreateServiceCategoryDto } from './dto/create-service-category.dto';
import { UpdateServiceCategoryDto } from './dto/update-service-category.dto';

@Injectable()
export class ServiceCategoryService {
  constructor(
    @InjectRepository(ServiceCategory)
    private serviceCategoryRepository: Repository<ServiceCategory>,
  ) {}

  /**
   * 创建服务分类
   * @param createServiceCategoryDto 服务分类数据
   */
  async create(createServiceCategoryDto: CreateServiceCategoryDto): Promise<ServiceCategory> {
    const { categoryName } = createServiceCategoryDto;
    const existingCategory = await this.serviceCategoryRepository.findOne({ where: { categoryName } });
    if (existingCategory) {
      throw new BadRequestException(`服务分类 '${categoryName}' 已存在`);
    }

    const newCategory = this.serviceCategoryRepository.create({
      categoryId: `SC${Date.now()}${Math.floor(Math.random() * 1000)}`, // 简单ID生成
      categoryName,
    });
    return this.serviceCategoryRepository.save(newCategory);
  }

  /**
   * 获取所有服务分类
   */
  async findAll(): Promise<ServiceCategory[]> {
    return this.serviceCategoryRepository.find();
  }

  /**
   * 根据ID获取单个服务分类
   * @param categoryId 分类ID
   */
  async findOne(categoryId: string): Promise<ServiceCategory> {
    const category = await this.serviceCategoryRepository.findOne({ where: { categoryId } });
    if (!category) {
      throw new NotFoundException(`服务分类 ID 为 ${categoryId} 未找到`);
    }
    return category;
  }

  /**
   * 更新服务分类
   * @param categoryId 分类ID
   * @param updateServiceCategoryDto 更新数据
   */
  async update(categoryId: string, updateServiceCategoryDto: UpdateServiceCategoryDto): Promise<ServiceCategory> {
    const category = await this.serviceCategoryRepository.findOne({ where: { categoryId } });
    if (!category) {
      throw new NotFoundException(`服务分类 ID 为 ${categoryId} 未找到`);
    }
    // 检查更新后的名称是否已存在（排除自身）
    if (updateServiceCategoryDto.categoryName && updateServiceCategoryDto.categoryName !== category.categoryName) {
        const existingCategory = await this.serviceCategoryRepository.findOne({
            where: { categoryName: updateServiceCategoryDto.categoryName }
        });
        if (existingCategory) {
            throw new BadRequestException(`服务分类名称 '${updateServiceCategoryDto.categoryName}' 已存在`);
        }
    }
    Object.assign(category, updateServiceCategoryDto);
    return this.serviceCategoryRepository.save(category);
  }

  /**
   * 删除服务分类
   * @param categoryId 分类ID
   */
  async remove(categoryId: string): Promise<void> {
    const result = await this.serviceCategoryRepository.delete(categoryId);
    if (result.affected === 0) {
      throw new NotFoundException(`服务分类 ID 为 ${categoryId} 未找到`);
    }
  }
}
```

**4. 实现 ServiceCategoryController (src/serviceCategory/service-category.controller.ts):**

```typescript
// src/serviceCategory/service-category.controller.ts
import { Controller, Post, Get, Put, Delete, Body, Param, HttpStatus, UseGuards } from '@nestjs/common';
import { ServiceCategoryService } from './service-category.service';
import { CreateServiceCategoryDto } from './dto/create-service-category.dto';
import { UpdateServiceCategoryDto } from './dto/update-service-category.dto';
import { AuthGuard } from '@nestjs/passport';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { ServiceCategory } from './entities/service-category.entity';

@Controller('api/service-category')
export class ServiceCategoryController {
  constructor(private readonly serviceCategoryService: ServiceCategoryService) {}

  /**
   * 创建服务分类 (仅限管理员)
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin') // 只有管理员可以创建服务分类
  @Post()
  async create(@Body() createServiceCategoryDto: CreateServiceCategoryDto) {
    const category = await this.serviceCategoryService.create(createServiceCategoryDto);
    return {
      statusCode: HttpStatus.CREATED,
      message: '服务分类创建成功',
      data: category,
    };
  }

  /**
   * 获取所有服务分类 (所有用户可见)
   */
  @Get()
  async findAll() {
    const categories = await this.serviceCategoryService.findAll();
    return {
      statusCode: HttpStatus.OK,
      data: categories,
    };
  }

  /**
   * 根据ID获取服务分类 (所有用户可见)
   */
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const category = await this.serviceCategoryService.findOne(id);
    return {
      statusCode: HttpStatus.OK,
      data: category,
    };
  }

  /**
   * 更新服务分类 (仅限管理员)
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @Put(':id')
  async update(@Param('id') id: string, @Body() updateServiceCategoryDto: UpdateServiceCategoryDto) {
    const updatedCategory = await this.serviceCategoryService.update(id, updateServiceCategoryDto);
    return {
      statusCode: HttpStatus.OK,
      message: '服务分类更新成功',
      data: updatedCategory,
    };
  }

  /**
   * 删除服务分类 (仅限管理员)
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.serviceCategoryService.remove(id);
    return {
      statusCode: HttpStatus.NO_CONTENT, // 204 No Content，表示成功删除但无返回内容
      message: '服务分类删除成功',
    };
  }
}
```

**5. 定义服务分类 DTO (src/serviceCategory/dto/):**

```typescript
// src/serviceCategory/dto/create-service-category.dto.ts
import { IsNotEmpty, IsString, MaxLength } from 'class-validator';

export class CreateServiceCategoryDto {
  @IsNotEmpty({ message: '分类名称不能为空' })
  @IsString({ message: '分类名称必须是字符串' })
  @MaxLength(100, { message: '分类名称长度不能超过100个字符' })
  categoryName: string;
}
```

```typescript
// src/serviceCategory/dto/update-service-category.dto.ts
import { IsOptional, IsString, MaxLength } from 'class-validator';

export class UpdateServiceCategoryDto {
  @IsOptional()
  @IsString({ message: '分类名称必须是字符串' })
  @MaxLength(100, { message: '分类名称长度不能超过100个字符' })
  categoryName?: string;
}
```

**6. 创建服务模块：**

```bash
nest generate module service
nest generate service service
nest generate controller service
```

**7. 配置 ServiceModule (src/service/service.module.ts):**

```typescript
// src/service/service.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServiceService } from './service.service';
import { ServiceController } from './service.controller';
import { Service } from './entities/service.entity';
import { ServiceCategoryModule } from '../serviceCategory/service-category.module'; // 导入 ServiceCategoryModule

@Module({
  imports: [
    TypeOrmModule.forFeature([Service]),
    ServiceCategoryModule, // 导入 ServiceCategoryModule 以便 ServiceService 可以访问 ServiceCategoryService
  ],
  controllers: [ServiceController],
  providers: [ServiceService],
  exports: [ServiceService], // 导出 ServiceService 以便 OrderModule 使用
})
export class ServiceModule {}
```

**8. 实现 ServiceService (src/service/service.service.ts):**

```typescript
// src/service/service.service.ts
import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Service } from './entities/service.entity';
import { CreateServiceDto } from './dto/create-service.dto';
import { UpdateServiceDto } from './dto/update-service.dto';
import { ServiceCategoryService } from '../serviceCategory/service-category.service'; // 导入 ServiceCategoryService

@Injectable()
export class ServiceService {
  constructor(
    @InjectRepository(Service)
    private serviceRepository: Repository<Service>,
    private serviceCategoryService: ServiceCategoryService, // 注入 ServiceCategoryService
  ) {}

  /**
   * 创建服务 (管理员)
   * @param createServiceDto 服务数据
   */
  async create(createServiceDto: CreateServiceDto): Promise<Service> {
    const { serviceName, categoryId } = createServiceDto;

    // 检查服务名称是否已存在
    const existingService = await this.serviceRepository.findOne({ where: { serviceName } });
    if (existingService) {
      throw new BadRequestException(`服务名称 '${serviceName}' 已存在`);
    }

    // 检查服务分类是否存在
    const category = await this.serviceCategoryService.findOne(categoryId);
    if (!category) {
        throw new NotFoundException(`服务分类 ID 为 ${categoryId} 不存在`);
    }

    const newService = this.serviceRepository.create({
      serviceId: `S${Date.now()}${Math.floor(Math.random() * 1000)}`, // 简单ID生成
      ...createServiceDto,
    });
    return this.serviceRepository.save(newService);
  }

  /**
   * 获取所有服务 (用户/访客可见，可能需要分页和过滤)
   */
  async findAll(): Promise<Service[]> {
    return this.serviceRepository.find({ relations: ['category'] }); // 查询时带上分类信息
  }

  /**
   * 根据ID获取服务详情 (用户/访客可见)
   * @param serviceId 服务ID
   */
  async findOne(serviceId: string): Promise<Service> {
    const service = await this.serviceRepository.findOne({ where: { serviceId }, relations: ['category'] });
    if (!service) {
      throw new NotFoundException(`服务 ID 为 ${serviceId} 未找到`);
    }
    return service;
  }

  /**
   * 更新服务信息 (管理员)
   * @param serviceId 服务ID
   * @param updateServiceDto 更新数据
   */
  async update(serviceId: string, updateServiceDto: UpdateServiceDto): Promise<Service> {
    const service = await this.serviceRepository.findOne({ where: { serviceId } });
    if (!service) {
      throw new NotFoundException(`服务 ID 为 ${serviceId} 未找到`);
    }
    // 检查更新后的服务名称是否已存在（排除自身）
    if (updateServiceDto.serviceName && updateServiceDto.serviceName !== service.serviceName) {
        const existingService = await this.serviceRepository.findOne({
            where: { serviceName: updateServiceDto.serviceName }
        });
        if (existingService) {
            throw new BadRequestException(`服务名称 '${updateServiceDto.serviceName}' 已存在`);
        }
    }
    // 如果修改了分类ID，需要验证新分类是否存在
    if (updateServiceDto.categoryId && updateServiceDto.categoryId !== service.categoryId) {
        const newCategory = await this.serviceCategoryService.findOne(updateServiceDto.categoryId);
        if (!newCategory) {
            throw new NotFoundException(`新服务分类 ID 为 ${updateServiceDto.categoryId} 不存在`);
        }
    }
    Object.assign(service, updateServiceDto);
    return this.serviceRepository.save(service);
  }

  /**
   * 删除服务 (管理员)
   * @param serviceId 服务ID
   */
  async remove(serviceId: string): Promise<void> {
    const result = await this.serviceRepository.delete(serviceId);
    if (result.affected === 0) {
      throw new NotFoundException(`服务 ID 为 ${serviceId} 未找到`);
    }
  }

  /**
   * 更改服务状态 (上架/下架) (管理员)
   * @param serviceId 服务ID
   * @param status 新状态 (0:下架, 1:上架)
   */
  async updateStatus(serviceId: string, status: number): Promise<Service> {
    const service = await this.serviceRepository.findOne({ where: { serviceId } });
    if (!service) {
      throw new NotFoundException(`服务 ID 为 ${serviceId} 未找到`);
    }
    if (status !== 0 && status !== 1) {
        throw new BadRequestException('状态值必须为 0 (下架) 或 1 (上架)');
    }
    service.status = status;
    return this.serviceRepository.save(service);
  }
}
```

**9. 实现 ServiceController (src/service/service.controller.ts):**

```typescript
// src/service/service.controller.ts
import { Controller, Post, Get, Put, Delete, Body, Param, HttpStatus, UseGuards } from '@nestjs/common';
import { ServiceService } from './service.service';
import { CreateServiceDto } from './dto/create-service.dto';
import { UpdateServiceDto } from './dto/update-service.dto';
import { AuthGuard } from '@nestjs/passport';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';

@Controller('api/service')
export class ServiceController {
  constructor(private readonly serviceService: ServiceService) {}

  /**
   * 创建服务 (仅限管理员)
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @Post()
  async create(@Body() createServiceDto: CreateServiceDto) {
    const service = await this.serviceService.create(createServiceDto);
    return {
      statusCode: HttpStatus.CREATED,
      message: '服务创建成功',
      data: service,
    };
  }

  /**
   * 获取所有服务列表 (用户/访客可见)
   * 可以添加查询参数进行过滤和分页
   */
  @Get()
  async findAll() {
    const services = await this.serviceService.findAll();
    return {
      statusCode: HttpStatus.OK,
      data: services,
    };
  }

  /**
   * 根据ID获取服务详情 (用户/访客可见)
   */
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const service = await this.serviceService.findOne(id);
    return {
      statusCode: HttpStatus.OK,
      data: service,
    };
  }

  /**
   * 更新服务信息 (仅限管理员)
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @Put(':id')
  async update(@Param('id') id: string, @Body() updateServiceDto: UpdateServiceDto) {
    const updatedService = await this.serviceService.update(id, updateServiceDto);
    return {
      statusCode: HttpStatus.OK,
      message: '服务信息更新成功',
      data: updatedService,
    };
  }

  /**
   * 删除服务 (仅限管理员)
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.serviceService.remove(id);
    return {
      statusCode: HttpStatus.NO_CONTENT,
      message: '服务删除成功',
    };
  }

  /**
   * 更改服务状态 (上架/下架) (仅限管理员)
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @Put(':id/status/:status') // status: 0 for off-shelf, 1 for on-shelf
  async updateStatus(@Param('id') id: string, @Param('status') status: number) {
    const updatedService = await this.serviceService.updateStatus(id, Number(status));
    return {
      statusCode: HttpStatus.OK,
      message: `服务状态更新成功为 ${status === 1 ? '上架' : '下架'}`,
      data: updatedService,
    };
  }
}
```

**10. 定义服务 DTO (src/service/dto/):**

```typescript
// src/service/dto/create-service.dto.ts
import { IsNotEmpty, IsString, MaxLength, IsNumber, IsUrl, IsOptional, IsArray, ArrayMinSize, IsEnum, IsInt, Min, Max } from 'class-validator';
import { Type } from 'class-transformer'; // 用于类型转换

export class CreateServiceDto {
  @IsNotEmpty({ message: '服务名称不能为空' })
  @IsString({ message: '服务名称必须是字符串' })
  @MaxLength(100, { message: '服务名称长度不能超过100个字符' })
  serviceName: string;

  @IsNotEmpty({ message: '服务分类ID不能为空' })
  @IsString({ message: '服务分类ID必须是字符串' })
  categoryId: string;

  @IsNotEmpty({ message: '服务描述不能为空' })
  @IsString({ message: '服务描述必须是字符串' })
  description: string;

  @IsNotEmpty({ message: '基础价格不能为空' })
  @IsNumber({}, { message: '基础价格必须是数字' })
  @Min(0, { message: '基础价格不能为负数' })
  basePrice: number;

  @IsOptional()
  @IsUrl({}, { message: '图片URL格式不正确' })
  imageUrl?: string;

  @IsOptional()
  @IsInt({ message: '状态必须是整数' })
  @Min(0, { message: '状态值不能小于0' })
  @Max(1, { message: '状态值不能大于1' })
  status?: number; // 0:下架, 1:上架

  @IsOptional()
  @IsArray({ message: '标签必须是数组' })
  @IsString({ each: true, message: '每个标签必须是字符串' })
  tags?: string[];
}
```

```typescript
// src/service/dto/update-service.dto.ts
import { IsOptional, IsString, MaxLength, IsNumber, IsUrl, IsArray, ArrayMinSize, IsInt, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateServiceDto {
  @IsOptional()
  @IsString({ message: '服务名称必须是字符串' })
  @MaxLength(100, { message: '服务名称长度不能超过100个字符' })
  serviceName?: string;

  @IsOptional()
  @IsString({ message: '服务分类ID必须是字符串' })
  categoryId?: string;

  @IsOptional()
  @IsString({ message: '服务描述必须是字符串' })
  description?: string;

  @IsOptional()
  @IsNumber({}, { message: '基础价格必须是数字' })
  @Min(0, { message: '基础价格不能为负数' })
  basePrice?: number;

  @IsOptional()
  @IsUrl({}, { message: '图片URL格式不正确' })
  imageUrl?: string;

  @IsOptional()
  @IsInt({ message: '状态必须是整数' })
  @Min(0, { message: '状态值不能小于0' })
  @Max(1, { message: '状态值不能大于1' })
  status?: number; // 0:下架, 1:上架

  @IsOptional()
  @IsArray({ message: '标签必须是数组' })
  @IsString({ each: true, message: '每个标签必须是字符串' })
  tags?: string[];
}
```

**11. 在 `AppModule` 中导入 `ServiceCategoryModule` 和 `ServiceModule`：**

```typescript
// src/app.module.ts
import { Module } from '@nestjs/common';
// ... 其他导入
import { ServiceCategoryModule } from './serviceCategory/service-category.module';
import { ServiceModule } from './service/service.module';

@Module({
  imports: [
    // ... TypeOrmModule.forRoot 和 ConfigModule.forRoot
    UserModule,
    TechnicianModule,
    AuthModule,
    ServiceCategoryModule, // 导入服务分类模块
    ServiceModule,       // 导入服务模块
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
```

### **4.2 订单模块 (Order Module)**

订单模块是家维在线系统的核心。它涉及用户创建订单、查看订单，以及技师和管理员对订单的后续处理。

**1. 创建订单模块：**

```bash
nest generate module order
nest generate service order
nest generate controller order
```

**2. 配置 OrderModule (src/order/order.module.ts):**

订单模块需要访问 `UserModule` (获取用户信息), `ServiceModule` (获取服务信息)。

```typescript
// src/order/order.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrderService } from './order.service';
import { OrderController } from './order.controller';
import { Order } from './entities/order.entity';
import { UserModule } from '../user/user.module'; // 导入 UserModule
import { ServiceModule } from '../service/service.module'; // 导入 ServiceModule
import { TechnicianModule } from '../technician/technician.module'; // 导入 TechnicianModule

@Module({
  imports: [
    TypeOrmModule.forFeature([Order]),
    UserModule,
    ServiceModule,
    TechnicianModule, // 导入技师模块，用于派单等场景
  ],
  controllers: [OrderController],
  providers: [OrderService],
  exports: [OrderService], // 导出 OrderService，如果其他模块（如财务）需要使用
})
export class OrderModule {}
```

**3. 实现 OrderService (src/order/order.service.ts):**

```typescript
// src/order/order.service.ts
import { Injectable, BadRequestException, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Order } from './entities/order.entity';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { UserService } from '../user/user.service';
import { ServiceService } from '../service/service.service';
import { Technician } from '../technician/entities/technician.entity'; // 导入 Technician 实体
import { TechnicianService } from '../technician/technician.service'; // 导入 TechnicianService

// 定义订单状态枚举，便于管理
export enum OrderStatus {
  PENDING_PAYMENT = '待支付',
  PENDING_ASSIGNMENT = '待安排', // 或待派单
  PENDING_SERVICE = '待服务',
  IN_SERVICE = '服务中',
  COMPLETED = '已完成',
  CANCELED = '已取消',
  AFTER_SALES = '售后中',
  REFUNDING = '退款中',
  REFUNDED = '已退款',
}

// 定义支付状态枚举
export enum PayStatus {
  UNPAID = 0,
  PAID = 1,
  FAILED = 2,
}

@Injectable()
export class OrderService {
  constructor(
    @InjectRepository(Order)
    private orderRepository: Repository<Order>,
    private userService: UserService,
    private serviceService: ServiceService,
    private technicianService: TechnicianService, // 注入技师服务
  ) {}

  /**
   * 创建订单 (用户端)
   * @param userId 用户ID
   * @param createOrderDto 订单数据
   */
  async createOrder(userId: string, createOrderDto: CreateOrderDto): Promise<Order> {
    const { serviceId, appointmentTime, address, contact, phone, remarks } = createOrderDto;

    const user = await this.userService.findOne(userId); // 验证用户是否存在
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    const service = await this.serviceService.findOne(serviceId); // 验证服务是否存在且已上架
    if (!service || service.status !== 1) {
      throw new BadRequestException('服务不存在或已下架');
    }

    // 根据服务基础价格计算订单金额（这里简化，实际可能涉及复杂计价）
    const amount = service.basePrice;

    const newOrder = this.orderRepository.create({
      orderNumber: `ORD${Date.now()}${Math.floor(Math.random() * 1000)}`, // 简单订单号生成
      userId: user.userId,
      serviceId: service.serviceId,
      serviceName: service.serviceName, // 冗余字段，方便查询
      appointmentTime: new Date(appointmentTime),
      address,
      contact,
      phone,
      amount,
      status: OrderStatus.PENDING_PAYMENT, // 初始状态为待支付
      payStatus: PayStatus.UNPAID, // 支付状态为未支付
      remarks,
    });

    return this.orderRepository.save(newOrder);
  }

  /**
   * 用户获取订单列表 (用户端)
   * @param userId 用户ID
   * @param status 可选：按订单状态过滤
   */
  async getUserOrders(userId: string, status?: OrderStatus): Promise<Order[]> {
    const whereCondition: any = { userId };
    if (status) {
      whereCondition.status = status;
    }
    return this.orderRepository.find({
      where: whereCondition,
      relations: ['service', 'technician'], // 加载关联的服务和技师信息
      order: { createTime: 'DESC' }, // 按创建时间倒序
    });
  }

  /**
   * 获取订单详情 (用户、技师、管理员)
   * @param orderNumber 订单号
   */
  async getOrderDetail(orderNumber: string): Promise<Order> {
    const order = await this.orderRepository.findOne({
      where: { orderNumber },
      relations: ['user', 'service', 'technician'], // 加载所有关联信息
    });
    if (!order) {
      throw new NotFoundException(`订单号 ${orderNumber} 未找到`);
    }
    return order;
  }

  /**
   * 用户取消订单 (用户端)
   * 只能取消待支付或待安排的订单
   * @param userId 用户ID
   * @param orderNumber 订单号
   */
  async cancelOrder(userId: string, orderNumber: string): Promise<Order> {
    const order = await this.orderRepository.findOne({ where: { orderNumber, userId } });
    if (!order) {
      throw new NotFoundException('订单未找到或您无权操作');
    }
    // 只有待支付或待安排的订单可以取消
    if (order.status !== OrderStatus.PENDING_PAYMENT && order.status !== OrderStatus.PENDING_ASSIGNMENT) {
      throw new BadRequestException(`订单当前状态为 "${order.status}"，无法取消`);
    }

    order.status = OrderStatus.CANCELED;
    return this.orderRepository.save(order);
  }

  /**
   * 模拟订单支付成功 (用于测试，实际集成支付网关)
   * @param orderNumber 订单号
   */
  async simulatePaymentSuccess(orderNumber: string): Promise<Order> {
    const order = await this.orderRepository.findOne({ where: { orderNumber } });
    if (!order) {
      throw new NotFoundException(`订单号 ${orderNumber} 未找到`);
    }
    if (order.payStatus === PayStatus.PAID) {
      throw new BadRequestException('订单已支付');
    }
    if (order.status === OrderStatus.CANCELED) {
        throw new BadRequestException('已取消的订单无法支付');
    }

    order.payStatus = PayStatus.PAID;
    order.payTime = new Date();
    // 支付成功后，订单状态从“待支付”变为“待安排”
    if (order.status === OrderStatus.PENDING_PAYMENT) {
        order.status = OrderStatus.PENDING_ASSIGNMENT;
    }

    return this.orderRepository.save(order);
  }

  /**
   * 管理员/客服获取所有订单列表 (管理员端)
   * 可带过滤和分页
   * @param query 查询参数 (可选：status, payStatus, technicianId, userId, keywords, pagination)
   */
  async getAllOrders(query: any): Promise<Order[]> {
    const { status, payStatus, technicianId, userId, keywords, page = 1, limit = 10 } = query;
    const offset = (page - 1) * limit;

    const queryBuilder = this.orderRepository.createQueryBuilder('order')
      .leftJoinAndSelect('order.user', 'user')
      .leftJoinAndSelect('order.service', 'service')
      .leftJoinAndSelect('order.technician', 'technician');

    if (status) {
      queryBuilder.andWhere('order.status = :status', { status });
    }
    if (payStatus !== undefined) {
      queryBuilder.andWhere('order.payStatus = :payStatus', { payStatus });
    }
    if (technicianId) {
      queryBuilder.andWhere('order.technicianId = :technicianId', { technicianId });
    }
    if (userId) {
      queryBuilder.andWhere('order.userId = :userId', { userId });
    }
    if (keywords) {
      queryBuilder.andWhere('(order.orderNumber LIKE :keywords OR order.serviceName LIKE :keywords OR order.contact LIKE :keywords OR order.phone LIKE :keywords)', { keywords: `%${keywords}%` });
    }

    queryBuilder.orderBy('order.createTime', 'DESC')
      .offset(offset)
      .limit(limit);

    return queryBuilder.getMany();
  }

  /**
   * 申请售后/退款 (用户端)
   * 只能对已完成或已支付的订单进行操作
   * @param orderNumber 订单号
   * @param userId 用户ID
   * @param reason 售后/退款原因
   * @param photos 相关图片 (可选)
   */
  async applyAfterSales(orderNumber: string, userId: string, reason: string, photos?: string[]): Promise<Order> {
    const order = await this.orderRepository.findOne({ where: { orderNumber, userId } });
    if (!order) {
      throw new NotFoundException('订单未找到或您无权操作');
    }

    // 只有已完成或已支付（但未完成）的订单才能申请售后/退款
    if (order.status !== OrderStatus.COMPLETED && order.payStatus === PayStatus.PAID) {
      throw new BadRequestException(`订单当前状态为 "${order.status}"，无法申请售后/退款`);
    }

    order.status = OrderStatus.AFTER_SALES; // 标记为售后中
    // 可以在这里添加售后详情字段，或者关联到独立的售后服务表
    order.remarks = `售后/退款申请：${reason}${photos ? '，附图：' + JSON.stringify(photos) : ''}`;
    // 如果是退款，可能还需要将状态设置为 OrderStatus.REFUNDING
    // order.status = OrderStatus.REFUNDING; // 根据业务需求决定
    return this.orderRepository.save(order);
  }

  /**
   * 技师获取待处理订单列表
   * @param technicianId 技师ID
   */
  async getPendingTechnicianOrders(technicianId: string): Promise<Order[]> {
    return this.orderRepository.find({
      where: {
        technicianId: technicianId,
        technicianAccepted: false, // 技师未接受
        status: OrderStatus.PENDING_ASSIGNMENT, // 待技师接受的订单
      },
      relations: ['user', 'service'],
      order: { createTime: 'ASC' },
    });
  }

  /**
   * 技师接受订单
   * @param orderNumber 订单号
   * @param technicianId 技师ID
   */
  async acceptOrder(orderNumber: string, technicianId: string): Promise<Order> {
    const order = await this.orderRepository.findOne({
      where: {
        orderNumber,
        technicianId, // 确保是分配给该技师的订单
        technicianAccepted: false,
        status: OrderStatus.PENDING_ASSIGNMENT,
      },
    });

    if (!order) {
      throw new NotFoundException('订单未找到，或您无权操作，或订单状态不允许接受');
    }

    order.technicianAccepted = true;
    order.status = OrderStatus.PENDING_SERVICE; // 技师接受后，变为待服务
    return this.orderRepository.save(order);
  }

  /**
   * 技师拒绝订单
   * @param orderNumber 订单号
   * @param technicianId 技师ID
   * @param rejectReason 拒绝原因
   */
  async rejectOrder(orderNumber: string, technicianId: string, rejectReason: string): Promise<Order> {
    const order = await this.orderRepository.findOne({
      where: {
        orderNumber,
        technicianId,
        technicianAccepted: false,
        status: OrderStatus.PENDING_ASSIGNMENT,
      },
    });

    if (!order) {
      throw new NotFoundException('订单未找到，或您无权操作，或订单状态不允许拒绝');
    }

    order.technicianRejected = true;
    order.rejectReason = rejectReason;
    // 拒绝后订单可能回到“待安排”状态，需要重新派单，或者取消
    // 这里我们先将其状态改回待安排，方便管理员重新派单
    order.status = OrderStatus.PENDING_ASSIGNMENT;
    order.technicianId = null; // 清除派单技师
    order.technicianAccepted = false;
    order.technicianRejected = false; // 重置拒绝状态，以便重新派单

    return this.orderRepository.save(order);
  }

  // 管理员分配订单给技师 (将在第五篇的派单模块中实现)
  async assignOrderToTechnician(orderNumber: string, technicianId: string): Promise<Order> {
    const order = await this.orderRepository.findOne({ where: { orderNumber } });
    if (!order) {
      throw new NotFoundException(`订单号 ${orderNumber} 未找到`);
    }
    // 确保订单状态适合分配 (例如：待安排)
    if (order.status !== OrderStatus.PENDING_ASSIGNMENT && order.status !== OrderStatus.CANCELED) {
      throw new BadRequestException(`订单状态为 "${order.status}"，无法进行派单操作`);
    }
    const technician = await this.technicianService.findOne(technicianId);
    if (!technician) {
      throw new NotFoundException(`技师 ID 为 ${technicianId} 未找到`);
    }
    // 检查技师是否空闲
    // if (technician.status !== 1) { // 假设1为空闲
    //   throw new BadRequestException('技师目前不空闲，无法分配');
    // }

    order.technicianId = technicianId;
    order.technicianAccepted = false; // 重新派单后，技师需要重新接受
    order.technicianRejected = false;
    order.status = OrderStatus.PENDING_ASSIGNMENT; // 重新回到待安排，等待技师确认
    return this.orderRepository.save(order);
  }
}
```

**4. 实现 OrderController (src/order/order.controller.ts):**

```typescript
// src/order/order.controller.ts
import { Controller, Post, Get, Put, Delete, Body, Param, HttpStatus, UseGuards, Request, Query } from '@nestjs/common';
import { OrderService, OrderStatus, PayStatus } from './order.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto'; // 假设有
import { CancelOrderDto } from './dto/cancel-order.dto';
import { ApplyAfterSalesDto } from './dto/apply-after-sales.dto';
import { AssignOrderDto } from './dto/assign-order.dto';
import { AcceptRejectOrderDto } from './dto/accept-reject-order.dto';

import { AuthGuard } from '@nestjs/passport';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { AuthenticatedUser } from '../auth/jwt.strategy'; // 导入 AuthenticatedUser

@Controller('api/order')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  /**
   * 创建订单 (用户端)
   * POST /api/order
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('user')
  @Post()
  async createOrder(@Request() req, @Body() createOrderDto: CreateOrderDto) {
    const userId = (req.user as AuthenticatedUser).userId; // 从认证信息中获取用户ID
    const order = await this.orderService.createOrder(userId, createOrderDto);
    return {
      statusCode: HttpStatus.CREATED,
      message: '订单创建成功，请等待支付',
      data: order,
    };
  }

  /**
   * 用户获取订单列表 (用户端)
   * GET /api/order/my
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('user')
  @Get('my')
  async getUserOrders(@Request() req, @Query('status') status?: OrderStatus) {
    const userId = (req.user as AuthenticatedUser).userId;
    const orders = await this.orderService.getUserOrders(userId, status);
    return {
      statusCode: HttpStatus.OK,
      data: orders,
    };
  }

  /**
   * 获取订单详情 (用户、技师、管理员)
   * GET /api/order/:orderNumber
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('user', 'technician', 'admin') // 任何相关角色都可以查看
  @Get(':orderNumber')
  async getOrderDetail(@Param('orderNumber') orderNumber: string) {
    const order = await this.orderService.getOrderDetail(orderNumber);
    return {
      statusCode: HttpStatus.OK,
      data: order,
    };
  }

  /**
   * 用户取消订单 (用户端)
   * PUT /api/order/:orderNumber/cancel
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('user')
  @Put(':orderNumber/cancel')
  async cancelOrder(@Request() req, @Param('orderNumber') orderNumber: string) {
    const userId = (req.user as AuthenticatedUser).userId;
    const updatedOrder = await this.orderService.cancelOrder(userId, orderNumber);
    return {
      statusCode: HttpStatus.OK,
      message: '订单已取消',
      data: updatedOrder,
    };
  }

  /**
   * 模拟支付成功接口 (用户端，实际由支付回调触发)
   * POST /api/order/:orderNumber/pay-success (仅用于测试)
   */
  @Post(':orderNumber/pay-success')
  async simulatePaymentSuccess(@Param('orderNumber') orderNumber: string) {
    const updatedOrder = await this.orderService.simulatePaymentSuccess(orderNumber);
    return {
      statusCode: HttpStatus.OK,
      message: '订单支付成功',
      data: updatedOrder,
    };
  }

  /**
   * 用户申请售后/退款
   * POST /api/order/:orderNumber/after-sales
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('user')
  @Post(':orderNumber/after-sales')
  async applyAfterSales(
    @Request() req,
    @Param('orderNumber') orderNumber: string,
    @Body() applyAfterSalesDto: ApplyAfterSalesDto,
  ) {
    const userId = (req.user as AuthenticatedUser).userId;
    const updatedOrder = await this.orderService.applyAfterSales(orderNumber, userId, applyAfterSalesDto.reason, applyAfterSalesDto.photos);
    return {
      statusCode: HttpStatus.OK,
      message: '售后/退款申请已提交',
      data: updatedOrder,
    };
  }

  // ----------- 技师相关接口 -----------

  /**
   * 技师获取待处理订单列表
   * GET /api/order/technician/pending
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('technician')
  @Get('technician/pending')
  async getTechnicianPendingOrders(@Request() req) {
    const technicianId = (req.user as AuthenticatedUser).technicianId;
    const orders = await this.orderService.getPendingTechnicianOrders(technicianId);
    return {
      statusCode: HttpStatus.OK,
      data: orders,
    };
  }

  /**
   * 技师接受订单
   * PUT /api/order/:orderNumber/accept
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('technician')
  @Put(':orderNumber/accept')
  async acceptOrder(@Request() req, @Param('orderNumber') orderNumber: string) {
    const technicianId = (req.user as AuthenticatedUser).technicianId;
    const updatedOrder = await this.orderService.acceptOrder(orderNumber, technicianId);
    return {
      statusCode: HttpStatus.OK,
      message: '订单接受成功，状态变更为待服务',
      data: updatedOrder,
    };
  }

  /**
   * 技师拒绝订单
   * PUT /api/order/:orderNumber/reject
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('technician')
  @Put(':orderNumber/reject')
  async rejectOrder(@Request() req, @Param('orderNumber') orderNumber: string, @Body() acceptRejectOrderDto: AcceptRejectOrderDto) {
    const technicianId = (req.user as AuthenticatedUser).technicianId;
    const updatedOrder = await this.orderService.rejectOrder(orderNumber, technicianId, acceptRejectOrderDto.reason);
    return {
      statusCode: HttpStatus.OK,
      message: '订单已拒绝，将重新派单',
      data: updatedOrder,
    };
  }

  // ----------- 管理员相关接口 -----------

  /**
   * 管理员获取所有订单列表
   * GET /api/order/admin/all
   * 可以通过查询参数进行过滤，例如：?status=已完成&payStatus=1
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @Get('admin/all')
  async getAllOrders(@Query() query: any) {
    const orders = await this.orderService.getAllOrders(query);
    return {
      statusCode: HttpStatus.OK,
      data: orders,
    };
  }

  /**
   * 管理员分配订单给技师 (将在第五篇的派单模块中进一步完善)
   * PUT /api/order/:orderNumber/assign
   */
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin')
  @Put(':orderNumber/assign')
  async assignOrder(@Param('orderNumber') orderNumber: string, @Body() assignOrderDto: AssignOrderDto) {
    const updatedOrder = await this.orderService.assignOrderToTechnician(orderNumber, assignOrderDto.technicianId);
    return {
      statusCode: HttpStatus.OK,
      message: `订单 ${orderNumber} 已成功分配给技师 ${assignOrderDto.technicianId}`,
      data: updatedOrder,
    };
  }
}
```

**5. 定义订单 DTO (src/order/dto/):**

```typescript
// src/order/dto/create-order.dto.ts
import { IsNotEmpty, IsString, IsDateString, IsMobilePhone, IsOptional, MaxLength } from 'class-validator';

export class CreateOrderDto {
  @IsNotEmpty({ message: '服务ID不能为空' })
  @IsString({ message: '服务ID必须是字符串' })
  serviceId: string;

  @IsNotEmpty({ message: '预约时间不能为空' })
  @IsDateString({}, { message: '预约时间格式不正确，应为有效日期字符串' })
  appointmentTime: string; // 使用字符串，在 Service 层转换为 Date

  @IsNotEmpty({ message: '地址不能为空' })
  @IsString({ message: '地址必须是字符串' })
  @MaxLength(255, { message: '地址长度不能超过255个字符' })
  address: string;

  @IsNotEmpty({ message: '联系人不能为空' })
  @IsString({ message: '联系人必须是字符串' })
  @MaxLength(100, { message: '联系人长度不能超过100个字符' })
  contact: string;

  @IsNotEmpty({ message: '联系电话不能为空' })
  @IsString({ message: '联系电话必须是字符串' })
  // @IsMobilePhone('zh-CN', { message: '联系电话格式不正确' })
  @MaxLength(20, { message: '联系电话长度不能超过20个字符' })
  phone: string;

  @IsOptional()
  @IsString({ message: '备注必须是字符串' })
  @MaxLength(500, { message: '备注长度不能超过500个字符' })
  remarks?: string;
}
```

```typescript
// src/order/dto/cancel-order.dto.ts
// 目前取消订单只需 orderNumber，不需要额外 body
// 如果需要取消原因等，可以再添加字段
export class CancelOrderDto {
    // 预留字段，如果以后需要取消原因等
}
```

```typescript
// src/order/dto/apply-after-sales.dto.ts
import { IsNotEmpty, IsString, IsOptional, IsArray, IsUrl } from 'class-validator';

export class ApplyAfterSalesDto {
  @IsNotEmpty({ message: '申请原因不能为空' })
  @IsString({ message: '申请原因必须是字符串' })
  reason: string;

  @IsOptional()
  @IsArray({ message: '图片列表必须是数组' })
  @IsUrl({}, { each: true, message: '图片URL格式不正确' })
  photos?: string[];
}
```

```typescript
// src/order/dto/assign-order.dto.ts
import { IsNotEmpty, IsString } from 'class-validator';

export class AssignOrderDto {
  @IsNotEmpty({ message: '技师ID不能为空' })
  @IsString({ message: '技师ID必须是字符串' })
  technicianId: string;
}
```

```typescript
// src/order/dto/accept-reject-order.dto.ts
import { IsString, IsOptional, IsNotEmpty } from 'class-validator';

export class AcceptRejectOrderDto {
  @IsOptional()
  @IsString({ message: '拒绝原因必须是字符串' })
  @IsNotEmpty({ message: '拒绝原因不能为空', groups: ['reject'] }) // 仅在拒绝时强制要求
  reason?: string;
}
```

**6. 在 `AppModule` 中导入 `OrderModule`：**

```typescript
// src/app.module.ts
import { Module } from '@nestjs/common';
// ... 其他导入
import { OrderModule } from './order/order.module';

@Module({
  imports: [
    // ... TypeOrmModule.forRoot 和 ConfigModule.forRoot
    UserModule,
    TechnicianModule,
    AuthModule,
    ServiceCategoryModule,
    ServiceModule,
    OrderModule, // 导入订单模块
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
```

### **测试服务与订单 API**

在完成所有代码编写后，保存所有文件并重新启动 NestJS 应用 (`npm run start:dev`)。

**测试步骤：**

1.  **准备数据：**
    * **注册一个普通用户** (POST `/api/user/register`)，并获取其 JWT Token。
    * **注册一个管理员用户** (POST `/api/user/register`)，并在数据库中手动将其 `roles` 字段更新为 `JSON_ARRAY('admin')`，然后重新登录获取管理员 JWT Token。
    * **注册一个技师** (POST `/api/technician/register`)，并获取其 JWT Token。
    * **管理员创建服务分类** (POST `/api/service-category`，使用管理员 Token)：
        * Body: `{"categoryName": "家电维修"}`
        * 获取 `categoryId`，例如 `SC1716672000000_example`
    * **管理员创建服务** (POST `/api/service`，使用管理员 Token)：
        * Body:
            ```json
            {
                "serviceName": "空调清洗",
                "categoryId": "您刚刚创建的分类ID",
                "description": "专业空调清洗服务",
                "basePrice": 150.00,
                "imageUrl": "https://example.com/ac_clean.jpg",
                "status": 1,
                "tags": ["清洗", "空调", "夏季"]
            }
            ```
        * 获取 `serviceId`，例如 `S1716673000000_example`

2.  **用户创建订单 (POST `http://localhost:3000/api/order`，使用普通用户 Token)**
    * **Header:** `Authorization: Bearer <用户JWT Token>`
    * **Body (JSON):**
        ```json
        {
            "serviceId": "您刚刚创建的服务ID",
            "appointmentTime": "2025-06-01T14:30:00Z",
            "address": "上海市浦东新区张江高科",
            "contact": "李四",
            "phone": "13911112222",
            "remarks": "请带工具箱"
        }
        ```
    * **预期响应:** `statusCode: 201`, `message: "订单创建成功..."`, `data` 包含新创建的订单信息。记住 `orderNumber`。

3.  **用户获取订单列表 (GET `http://localhost:3000/api/order/my`，使用普通用户 Token)**
    * **Header:** `Authorization: Bearer <用户JWT Token>`
    * **预期响应:** `statusCode: 200`, `data` 包含用户的订单列表。

4.  **用户获取订单详情 (GET `http://localhost:3000/api/order/<订单号>`，使用普通用户 Token)**
    * **Header:** `Authorization: Bearer <用户JWT Token>`
    * **预期响应:** `statusCode: 200`, `data` 包含订单详细信息。

5.  **模拟订单支付成功 (POST `http://localhost:3000/api/order/<订单号>/pay-success`)**
    * **注意：** 此接口目前无认证，仅用于测试。实际需要严格控制。
    * **预期响应:** `statusCode: 200`, `message: "订单支付成功"`, `data` 订单状态变为 `待安排`，`payStatus` 变为 `1`。

6.  **技师获取待处理订单 (GET `http://localhost:3000/api/order/technician/pending`，使用技师 Token)**
    * **Header:** `Authorization: Bearer <技师JWT Token>`
    * **预期响应:** `statusCode: 200`, `data` 包含分配给该技师（或待分配给该技师）且技师未接受的订单列表。**注意：** 您可能需要先用管理员接口将订单分配给技师，或者手动在数据库中将订单 `technicianId` 设置为该技师ID，并确保 `technicianAccepted` 为 `false` 且 `status` 为 `待安排`。

7.  **技师接受订单 (PUT `http://localhost:3000/api/order/<订单号>/accept`，使用技师 Token)**
    * **Header:** `Authorization: Bearer <技师JWT Token>`
    * **Body:** (空)
    * **预期响应:** `statusCode: 200`, 订单状态变为 `待服务`，`technicianAccepted` 变为 `true`。

8.  **技师拒绝订单 (PUT `http://localhost:3000/api/order/<订单号>/reject`，使用技师 Token)**
    * **Header:** `Authorization: Bearer <技师JWT Token>`
    * **Body (JSON):** `{"reason": "时间冲突"}`
    * **预期响应:** `statusCode: 200`, 订单状态可能回到 `待安排`，技师ID清空，`technicianRejected` 变为 `true`。

9.  **管理员获取所有订单 (GET `http://localhost:3000/api/order/admin/all`，使用管理员 Token)**
    * **Header:** `Authorization: Bearer <管理员JWT Token>`
    * **预期响应:** `statusCode: 200`, `data` 包含所有订单列表。可以尝试添加查询参数 `?status=已完成` 或 `?payStatus=1` 进行过滤。

10. **管理员分配订单 (PUT `http://localhost:3000/api/order/<订单号>/assign`，使用管理员 Token)**
    * **Header:** `Authorization: Bearer <管理员JWT Token>`
    * **Body (JSON):** `{"technicianId": "您技师的ID"}`
    * **预期响应:** `statusCode: 200`, 订单的 `technicianId` 被设置，状态变为 `待安排`。

**第四篇总结：**

恭喜！通过本篇教程，您已经成功：

* 实现了服务分类的 CRUD API，并进行了管理员权限控制。
* 实现了服务的 CRUD API，同样具备管理员权限，并提供了用户查询接口。
* **最重要的，实现了订单模块的基础功能，包括：**
    * 用户创建订单。
    * 用户查询和取消订单。
    * 模拟支付成功（为后续真正的支付集成打下基础）。
    * 用户申请售后/退款。
    * 技师查看、接受和拒绝分配的订单。
    * 管理员查看所有订单和分配订单。

这些功能涵盖了家维在线系统的核心业务流程，标志着项目的一个重要里程碑。

---

**[请告诉我“继续”，我们将进入第五篇：核心业务模块二：维修工作流与派单，进一步完善订单的生命周期管理，特别是维修技师的服务流程。]**