# 第十一篇：服务商端界面实现

## 概述

在前面的教程中，我们已经完成了用户端界面的实现。本篇教程将实现服务商端的完整界面，包括服务商注册认证、订单接单管理、服务管理、收入统计等核心功能。

我们将实现：
- 服务商注册与资质认证
- 订单接单与管理系统
- 服务项目管理
- 收入统计与提现
- 客户评价管理
- 个人资料与设置

## 技术栈

- **UI框架**: Ant Design 5.x
- **图表库**: @ant-design/charts (基于G2Plot)
- **状态管理**: Zustand + React Query
- **表单处理**: Ant Design Form + React Hook Form
- **文件上传**: Ant Design Upload
- **地图服务**: 高德地图API

## 页面结构规划

```
src/pages/Provider/
├── Dashboard/              # 服务商仪表盘
│   ├── index.tsx
│   └── index.module.less
├── Profile/                # 个人资料管理
│   ├── index.tsx
│   ├── Certification.tsx   # 资质认证
│   └── index.module.less
├── Orders/                 # 订单管理
│   ├── index.tsx
│   ├── OrderDetail.tsx     # 订单详情
│   └── index.module.less
├── Services/               # 服务管理
│   ├── index.tsx
│   ├── ServiceForm.tsx     # 服务编辑
│   └── index.module.less
├── Earnings/               # 收入管理
│   ├── index.tsx
│   ├── Withdraw.tsx        # 提现管理
│   └── index.module.less
└── Messages/               # 消息中心
    ├── index.tsx
    └── index.module.less
```

---

## 11.1 服务商仪表盘

### 11.1.1 数据概览与统计

实现服务商的数据概览仪表盘，展示关键业务指标：

```typescript
// src/pages/Provider/Dashboard/index.tsx
import React, { useState } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Button,
  Table,
  Tag,
  Space,
  DatePicker,
  Select,
  Progress,
  Avatar,
  List,
  Typography,
} from 'antd';
import {
  DollarOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  StarOutlined,
  TrendingUpOutlined,
  CalendarOutlined,
  MessageOutlined,
  BellOutlined,
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Column, Line, Pie } from '@ant-design/charts';
import { providerService } from '@services/providerService';
import { orderService } from '@services/orderService';
import { ORDER_STATUS_MAP } from '@constants/index';
import dayjs from 'dayjs';
import styles from './index.module.less';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Text, Title } = Typography;

interface DashboardStats {
  totalEarnings: number;
  monthlyEarnings: number;
  totalOrders: number;
  completedOrders: number;
  averageRating: number;
  totalReviews: number;
  pendingOrders: number;
  todayOrders: number;
}

const ProviderDashboard: React.FC = () => {
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);
  const [period, setPeriod] = useState<'week' | 'month' | 'year'>('month');

  // 获取仪表盘统计数据
  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['provider-dashboard-stats', dateRange],
    queryFn: () => providerService.getDashboardStats({
      startDate: dateRange[0].format('YYYY-MM-DD'),
      endDate: dateRange[1].format('YYYY-MM-DD'),
    }),
  });

  // 获取收入趋势数据
  const { data: earningsTrend } = useQuery({
    queryKey: ['provider-earnings-trend', period],
    queryFn: () => providerService.getEarningsTrend(period),
  });

  // 获取订单趋势数据
  const { data: ordersTrend } = useQuery({
    queryKey: ['provider-orders-trend', period],
    queryFn: () => providerService.getOrdersTrend(period),
  });

  // 获取最近订单
  const { data: recentOrders } = useQuery({
    queryKey: ['provider-recent-orders'],
    queryFn: () => orderService.getProviderOrders({
      page: 1,
      limit: 5,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    }),
  });

  // 获取待处理事项
  const { data: pendingTasks } = useQuery({
    queryKey: ['provider-pending-tasks'],
    queryFn: () => providerService.getPendingTasks(),
  });

  // 统计卡片数据
  const statisticCards = [
    {
      title: '本月收入',
      value: stats?.monthlyEarnings || 0,
      prefix: '¥',
      icon: <DollarOutlined />,
      color: '#52c41a',
      suffix: '元',
    },
    {
      title: '总订单数',
      value: stats?.totalOrders || 0,
      icon: <ShoppingCartOutlined />,
      color: '#1890ff',
    },
    {
      title: '完成订单',
      value: stats?.completedOrders || 0,
      icon: <UserOutlined />,
      color: '#722ed1',
    },
    {
      title: '平均评分',
      value: stats?.averageRating || 0,
      precision: 1,
      icon: <StarOutlined />,
      color: '#fa8c16',
      suffix: '分',
    },
  ];

  // 收入趋势图配置
  const earningsConfig = {
    data: earningsTrend || [],
    xField: 'date',
    yField: 'amount',
    smooth: true,
    color: '#52c41a',
    point: {
      size: 4,
      shape: 'circle',
    },
    tooltip: {
      formatter: (datum: any) => ({
        name: '收入',
        value: `¥${datum.amount}`,
      }),
    },
  };

  // 订单趋势图配置
  const ordersConfig = {
    data: ordersTrend || [],
    xField: 'date',
    yField: 'count',
    columnWidthRatio: 0.6,
    color: '#1890ff',
    tooltip: {
      formatter: (datum: any) => ({
        name: '订单数',
        value: `${datum.count}单`,
      }),
    },
  };

  // 订单状态分布配置
  const orderStatusData = [
    { type: '待确认', value: stats?.pendingOrders || 0 },
    { type: '进行中', value: stats?.inProgressOrders || 0 },
    { type: '已完成', value: stats?.completedOrders || 0 },
    { type: '已取消', value: stats?.cancelledOrders || 0 },
  ];

  const statusPieConfig = {
    data: orderStatusData,
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    interactions: [{ type: 'element-active' }],
  };

  return (
    <div className={styles.dashboardContainer}>
      {/* 页面头部 */}
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Title level={2}>服务商工作台</Title>
          <Text type="secondary">
            欢迎回来，今天是 {dayjs().format('YYYY年MM月DD日')}
          </Text>
        </div>
        <div className={styles.headerRight}>
          <Space>
            <RangePicker
              value={dateRange}
              onChange={(dates) => dates && setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
            />
            <Select
              value={period}
              onChange={setPeriod}
              style={{ width: 100 }}
            >
              <Option value="week">本周</Option>
              <Option value="month">本月</Option>
              <Option value="year">本年</Option>
            </Select>
          </Space>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className={styles.statsCards}>
        {statisticCards.map((card, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card>
              <Statistic
                title={card.title}
                value={card.value}
                precision={card.precision}
                prefix={card.prefix}
                suffix={card.suffix}
                valueStyle={{ color: card.color }}
              />
              <div className={styles.cardIcon} style={{ color: card.color }}>
                {card.icon}
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      <Row gutter={[16, 16]}>
        {/* 收入趋势 */}
        <Col xs={24} lg={12}>
          <Card title="收入趋势" extra={<TrendingUpOutlined />}>
            <Line {...earningsConfig} height={300} />
          </Card>
        </Col>

        {/* 订单趋势 */}
        <Col xs={24} lg={12}>
          <Card title="订单趋势" extra={<CalendarOutlined />}>
            <Column {...ordersConfig} height={300} />
          </Card>
        </Col>

        {/* 订单状态分布 */}
        <Col xs={24} lg={8}>
          <Card title="订单状态分布">
            <Pie {...statusPieConfig} height={300} />
          </Card>
        </Col>

        {/* 最近订单 */}
        <Col xs={24} lg={16}>
          <Card 
            title="最近订单" 
            extra={
              <Button type="link" href="/provider/orders">
                查看全部
              </Button>
            }
          >
            <Table
              dataSource={recentOrders?.items}
              pagination={false}
              size="small"
              columns={[
                {
                  title: '订单号',
                  dataIndex: 'orderNo',
                  width: 120,
                },
                {
                  title: '服务项目',
                  dataIndex: ['service', 'name'],
                },
                {
                  title: '客户',
                  dataIndex: ['user', 'name'],
                },
                {
                  title: '金额',
                  dataIndex: 'totalAmount',
                  render: (amount: number) => `¥${amount}`,
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                  render: (status: string) => {
                    const config = ORDER_STATUS_MAP[status];
                    return <Tag color={config.color}>{config.text}</Tag>;
                  },
                },
                {
                  title: '时间',
                  dataIndex: 'createdAt',
                  render: (time: string) => dayjs(time).format('MM-DD HH:mm'),
                },
              ]}
            />
          </Card>
        </Col>
      </Row>

      {/* 待处理事项 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="待处理事项" extra={<BellOutlined />}>
            <List
              dataSource={pendingTasks}
              renderItem={(item: any) => (
                <List.Item
                  actions={[
                    <Button type="link" key="handle">
                      处理
                    </Button>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={<Avatar icon={item.icon} />}
                    title={item.title}
                    description={item.description}
                  />
                  <div>{dayjs(item.createdAt).fromNow()}</div>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 服务评价概览 */}
        <Col xs={24} lg={12}>
          <Card title="服务评价" extra={<StarOutlined />}>
            <div className={styles.ratingOverview}>
              <div className={styles.ratingScore}>
                <Statistic
                  value={stats?.averageRating || 0}
                  precision={1}
                  suffix="分"
                  valueStyle={{ fontSize: 32, color: '#fa8c16' }}
                />
                <div className={styles.ratingCount}>
                  基于 {stats?.totalReviews || 0} 条评价
                </div>
              </div>
              <div className={styles.ratingBars}>
                {[5, 4, 3, 2, 1].map((star) => (
                  <div key={star} className={styles.ratingBar}>
                    <span>{star}星</span>
                    <Progress
                      percent={stats?.ratingDistribution?.[star] || 0}
                      size="small"
                      showInfo={false}
                    />
                    <span>{stats?.ratingDistribution?.[star] || 0}%</span>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ProviderDashboard;
```
