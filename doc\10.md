好的，我们终于来到了本系列教程的最后一部分——部署与维护。经过本地的开发和测试，现在是时候将我们的应用推向生产环境，让它真正运行起来了。

---

### **《从零构建家政维修平台：打造稳健高效的混合架构后台应用》**

#### **第十篇：后台部署与维护——从本地到轻量应用服务器**

**摘要：** 经过前九篇的本地开发与测试，我们已经构建了一个功能完善的家政维修平台后端。本篇将详细指导你如何将NestJS后端部署到腾讯云轻量应用服务器（Lighthouse）上，并介绍一些基本的运维和安全最佳实践，确保应用稳健运行。

---

**一、选择腾讯云轻量应用服务器（Lighthouse）的优势**

* **简单易用：** 界面直观，一键部署应用镜像，适合初学者和中小型项目。
* **高性价比：** 价格亲民，套餐灵活，包含流量包，降低运营成本。
* **性能优越：** 提供SSD云硬盘，保证应用运行流畅。
* **网络带宽：** 预设了公网带宽，方便对外提供服务。
* **安全性：** 内置防火墙，方便配置安全组规则。

**二、部署前的准备工作**

1.  **代码准备：**
    * **Git版本控制：** 确保你的后端项目代码已提交到Git仓库（如GitHub、Gitee）。这是最佳实践，方便后续部署、回滚和团队协作。
    * **环境变量配置：** 再次检查你的 `.env` 文件。在生产环境中，我们不应该直接上传 `.env` 文件，而是通过服务器的环境变量配置或更安全的配置管理方案来设置 `SecretId`、`SecretKey`、数据库密码等敏感信息。
    * **数据库连接：** 确保 `src/app.module.ts` 中的数据库配置指向一个可从服务器访问的MySQL实例。如果你之前使用了Docker在本地运行MySQL，现在你需要一个远程的MySQL数据库。
        * **推荐：** 使用腾讯云的云数据库MySQL版（CDB）服务，它提供高可用、免维护的数据库服务，并且与轻量应用服务器在同一地域时，内网互通，安全性高，速度快。
        * **配置信息：** 记录下云数据库的连接地址、端口、用户名和密码。

2.  **腾讯云轻量应用服务器购买与配置：**
    * **购买实例：** 登录腾讯云轻量应用服务器控制台 `https://lighthouse.cloud.tencent.com/`。
        * 点击“新建实例”。
        * **地域：** **非常重要！** 选择与你COS存储桶、未来云数据库MySQL实例相同的地域（例如：广州、上海等），以确保内网互通，降低流量费用和网络延迟。
        * **镜像：** 选择“应用镜像”中的 **“Node.js”** 镜像或“系统镜像”中的 **“CentOS / Ubuntu”**。Node.js镜像会预装Node.js环境，系统镜像则需要你手动安装。这里我们以Node.js镜像为例，它更省心。
        * **套餐：** 根据你的需求选择合适的CPU、内存和带宽套餐。
        * **系统盘：** 默认即可。
        * **登录方式：** 设置一个安全的登录密码或使用SSH密钥对。
        * 购买并等待实例启动。

3.  **配置防火墙/安全组：**
    * 实例启动后，进入“防火墙”选项卡。
    * **添加规则：**
        * 开放端口 `3000` (你的NestJS应用监听的端口)。
        * 开放端口 `80` 和 `443` (如果你后续需要部署前端静态文件或使用Nginx反向代理)。
        * 如果你使用了腾讯云数据库，无需开放数据库的公网端口，轻量应用服务器通过内网即可访问。

**三、部署NestJS后端应用**

有两种常见的部署方式：手动部署和使用PM2进行进程管理。

1.  **登录服务器：**
    * 使用SSH客户端（Windows可使用PuTTY、Git Bash，macOS/Linux直接使用终端）连接你的轻量应用服务器。
    * 命令示例：`ssh root@你的服务器公网IP`

2.  **安装Git和PM2：**
    * 如果服务器未预装Git：`sudo yum install git -y` (CentOS) 或 `sudo apt-get install git -y` (Ubuntu)。
    * 安装PM2（Node.js进程管理器）：`npm install -g pm2`

3.  **克隆代码并安装依赖：**
    * 选择一个目录存放你的应用代码，例如 `/app`：
        ```bash
        mkdir /app
        cd /app
        ```
    * 克隆你的后端项目代码：
        ```bash
        git clone https://github.com/YourUsername/home-repair-backend.git
        cd home-repair-backend
        ```
    * 安装生产环境依赖：
        ```bash
        npm install --production # 只安装生产环境依赖，跳过devDependencies
        # 或者 yarn install --production
        ```

4.  **构建生产环境代码：**
    * NestJS项目需要编译成JavaScript代码才能运行。
    * ```bash
        npm run build
        # 或者 yarn build
        ```
    * 这会在项目根目录生成一个 `dist` 文件夹，包含编译后的JavaScript文件。

5.  **设置环境变量：**
    * **最推荐的方式：** 使用PM2的环境变量配置。
    * 在 `home-repair-backend` 目录下创建 `ecosystem.config.js` 文件：
        ```javascript
        // ecosystem.config.js
        module.exports = {
          apps : [{
            name: "home-repair-backend", // 应用名称
            script: "./dist/main.js",    // 启动文件路径（编译后的入口文件）
            instances: "max",           // 进程数量，"max"表示最大CPU核心数
            exec_mode: "cluster",       // 集群模式
            env: {
              NODE_ENV: "production",
              PORT: 3000,
              // 数据库配置
              DB_HOST: "your_mysql_host",
              DB_PORT: 3306,
              DB_USERNAME: "your_mysql_username",
              DB_PASSWORD: "your_mysql_password",
              DB_DATABASE: "your_mysql_database",
              // COS配置
              COS_SECRET_ID: "your_cos_secret_id",
              COS_SECRET_KEY: "your_cos_secret_key",
              COS_BUCKET: "your_cos_bucket_name",
              COS_REGION: "your_cos_region",
              // 企业微信配置
              WECOM_CORP_ID: "your_wecom_corp_id",
              WECOM_CONTACT_SYNC_SECRET: "your_wecom_contact_sync_secret",
              WECOM_AGENT_ID: "your_wecom_agent_id",
              WECOM_AGENT_SECRET: "your_wecom_agent_secret",
              // 飞书配置
              FEISHU_APP_ID: "your_feishu_app_id",
              FEISHU_APP_SECRET: "your_feishu_app_secret",
              FEISHU_BASE_TOKEN: "your_feishu_base_token",
              FEISHU_ORDER_TABLE_ID: "your_feishu_order_table_id",
              // JWT Secret (重要的安全配置，必须替换为强密码)
              JWT_SECRET: "YOUR_VERY_STRONG_JWT_SECRET_KEY", // 确保与你AuthModule中使用的Secret一致
            },
          }]
        };
        ```
        * **替换所有 `your_...` 为实际的生产环境配置！** 特别是 `JWT_SECRET`，请务必使用一个非常强大、随机生成的字符串。
        * `JWT_SECRET` 应与 `src/auth/auth.module.ts` 中 `JwtModule.register()` 的 `secret` 配置项一致。如果你的JWT Secret是通过 `@nestjs/config` 读取的，那么 `ecosystem.config.js` 中的 `JWT_SECRET` 会自动被 `ConfigService` 读取到。

6.  **使用PM2启动应用：**
    * 在 `home-repair-backend` 目录下，运行：
        ```bash
        pm2 start ecosystem.config.js
        ```
    * 查看应用状态：`pm2 list` 或 `pm2 status`
    * 查看应用日志：`pm2 logs home-repair-backend`
    * 设置开机自启：
        ```bash
        pm2 startup
        pm2 save
        ```
        * 这会生成一个命令让你粘贴执行，以确保服务器重启后PM2和你的应用能自动启动。

**四、部署前端管理后台**

1.  **前端打包：** 在本地 `home-repair-admin` 项目根目录运行：
    ```bash
    npm run build
    # 或者 yarn build
    ```
    * 这会在项目根目录生成一个 `dist` 文件夹，包含所有用于生产环境的静态文件。

2.  **上传静态文件：**
    * 通过SCP、SFTP工具（如WinSCP、FileZilla）或 `rsync` 命令将本地 `home-repair-admin/dist` 文件夹中的所有文件上传到服务器的一个Web服务器可以访问的目录，例如 `/usr/share/nginx/html` 或 `/var/www/html/home-repair-admin`。

3.  **配置Web服务器（Nginx，推荐）：**
    * 如果你的轻量应用服务器没有预装Nginx，请先安装：
        * CentOS: `sudo yum install nginx -y`
        * Ubuntu: `sudo apt-get install nginx -y`
    * 配置Nginx反向代理和静态文件服务：
        * 创建Nginx配置文件（例如 `/etc/nginx/conf.d/home-repair.conf`）：
            ```nginx
            server {
                listen 80;
                server_name your_domain.com your_server_ip; # 替换为你的域名或服务器IP

                location / {
                    root /var/www/html/home-repair-admin; # 你的前端文件存放路径
                    index index.html index.htm;
                    try_files $uri $uri/ /index.html; # SPA应用需要此配置
                }

                location /api/ { # 后端API的反向代理
                    proxy_pass http://localhost:3000/; # 你的NestJS后端监听的地址和端口
                    proxy_set_header Host $host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;
                }

                # 可选：配置SSL/HTTPS，需要证书
                # listen 443 ssl;
                # ssl_certificate /path/to/your_ssl_certificate.crt;
                # ssl_certificate_key /path/to/your_ssl_key.key;
                # ... 其他SSL配置 ...
            }
            ```
        * 测试Nginx配置：`sudo nginx -t`
        * 重启Nginx服务：`sudo systemctl restart nginx`

**五、生产环境安全与维护最佳实践**

* **HTTPS：** 强烈建议为你的域名配置SSL证书（可从腾讯云申请免费证书），实现HTTPS加密，保护数据传输安全。
* **日志管理：** PM2会自动管理应用的日志，但你也可以配置更专业的日志收集系统（如ELK Stack、腾讯云CLS）来集中管理和分析日志。
* **监控报警：** 配置腾讯云云监控，监控服务器CPU、内存、带宽、磁盘使用情况，以及应用进程状态，设置异常报警。
* **数据备份：** 定期备份MySQL数据库数据，确保数据安全。腾讯云数据库MySQL版自带备份功能。
* **安全更新：** 定期更新操作系统、Node.js版本和项目依赖库，修复已知安全漏洞。
* **代码更新：** 部署新版本时，先 `git pull` 拉取最新代码，然后 `npm install --production` 和 `npm run build`，最后 `pm2 reload home-repair-backend` 平滑重启应用。

恭喜你！你已经成功地将一个功能完善的家政维修平台后端从零构建，并在本地完成测试，最终部署到了轻量应用服务器上，并配置了必要的前端服务和安全措施。这是一个完整的开发到部署的循环，希望本系列教程能为你未来的项目开发和运维打下坚实基础。

---

至此，《从零构建家政维修平台：打造稳健高效的混合架构后台应用》系列教程全部结束。