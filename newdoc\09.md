好的，我们正式开始前端部分的**第一篇：前端项目初始化与基础配置**。

在这一篇中，我们将从零开始搭建 React 项目，并集成 Ant Design 和 Axios，为后续的界面开发和后端 API 联调打下坚实的基础。

---

## 第一篇：前端项目初始化与基础配置

### 1.1 React 项目初始化 (Vite)

我们选择 **Vite** 来初始化 React 项目。Vite 是一个现代化的前端构建工具，以其极快的开发服务器启动速度和即时热模块更新（HMR）而闻名，能显著提升开发体验。

#### 1.1.1 安装 Vite

首先，确保你的电脑上安装了 Node.js（推荐使用 LTS 版本）。然后，你可以通过 npm 或 yarn 创建 Vite 项目。

打开你的终端或命令行工具，导航到你希望创建项目的目录，然后运行以下命令：

```bash
# 使用 npm
npm create vite@latest my-homerepair-frontend -- --template react-ts

# 或者使用 yarn
yarn create vite my-homerepair-frontend --template react-ts

# 或者使用 pnpm
pnpm create vite my-homerepair-frontend --template react-ts
```

* `my-homerepair-frontend`：这是你项目文件夹的名称，你可以根据喜好修改。
* `--template react-ts`：这会创建一个基于 React 和 TypeScript 的项目。我们选择 TypeScript 是因为它能提供更好的类型检查和代码提示，尤其对于大型项目非常有益。

执行命令后，Vite 会自动帮你完成项目文件的生成。

#### 1.1.2 进入项目并安装依赖

项目创建完成后，进入新生成的项目目录，并安装其依赖：

```bash
cd my-homerepair-frontend
npm install
# 或者 yarn install
# 或者 pnpm install
```

#### 1.1.3 运行项目

安装完依赖后，你可以启动开发服务器，在浏览器中查看你的 React 应用：

```bash
npm run dev
# 或者 yarn dev
# 或者 pnpm dev
```

通常，Vite 会在 `http://localhost:5173`（或类似端口）启动开发服务器。打开浏览器访问这个地址，你就能看到一个简单的 React 欢迎页面。

#### 1.1.4 项目结构概览

一个典型的 Vite + React + TypeScript 项目结构大致如下：

```
my-homerepair-frontend/
├── public/                # 存放静态资源，如 favicon.ico
├── src/
│   ├── assets/            # 存放图片、字体等静态资源
│   ├── components/        # 存放可复用的 UI 组件
│   ├── pages/             # 存放应用的页面组件
│   ├── services/          # 存放后端 API 请求相关服务
│   ├── utils/             # 存放通用工具函数
│   ├── App.css            # 应用全局样式
│   ├── App.tsx            # 应用主组件
│   ├── index.css          # 全局 CSS 样式
│   ├── main.tsx           # 应用入口文件，ReactDOM 渲染根组件
│   ├── vite-env.d.ts      # Vite 环境变量声明
│   └── react-app-env.d.ts # React 类型声明
├── .eslintrc.cjs          # ESLint 配置 (代码风格检查)
├── .gitignore             # Git 忽略文件
├── index.html             # HTML 入口文件
├── package.json           # 项目配置文件和依赖
├── tsconfig.json          # TypeScript 配置文件
├── tsconfig.node.json     # TypeScript node 环境配置
├── vite.config.ts         # Vite 配置文件
└── README.md
```

**我们后续的开发主要集中在 `src/` 目录。**

---

### 1.2 引入 Ant Design

Ant Design 是一套企业级 UI 设计语言和 React 组件库，它提供了丰富的、高质量的组件，能够帮助我们快速构建美观且功能完善的界面。

#### 1.2.1 安装 Ant Design

在你的项目根目录中，运行以下命令安装 Ant Design：

```bash
npm install antd --save
# 或者 yarn add antd
# 或者 pnpm add antd
```

#### 1.2.2 配置按需加载

为了优化打包体积，我们通常会配置 Ant Design 的**按需加载**。这意味着只有你实际使用的组件才会被打包到最终的代码中。

对于 Vite 项目，你可以使用 `vite-plugin-style-import` 插件来实现按需加载。

1.  **安装插件：**

    ```bash
    npm install vite-plugin-style-import -D
    # 或者 yarn add vite-plugin-style-import -D
    # 或者 pnpm add vite-plugin-style-import -D
    ```

2.  **配置 `vite.config.ts`：**

    打开项目根目录下的 `vite.config.ts` 文件，修改内容如下：

    ```typescript
    import { defineConfig } from 'vite';
    import react from '@vitejs/plugin-react';
    import { createStyleImportPlugin, AntdResolve } from 'vite-plugin-style-import';

    // https://vitejs.dev/config/
    export default defineConfig({
      plugins: [
        react(),
        createStyleImportPlugin({
          resolves: [AntdResolve()],
          // 根据实际情况选择要导入的样式文件类型
          // 例如，如果你想引入所有样式，可以不用加这个
          // 如果只引入组件的样式，可以保留
          libs: [
            {
              libraryName: 'antd',
              esModule: true,
              resolveStyle: (name) => {
                return `antd/es/${name}/style/index.js`;
              },
            },
          ],
        }),
      ],
      css: {
        preprocessorOptions: {
          less: {
            javascriptEnabled: true, // 允许 less 中使用 js
            // 可以通过 modifyVars 进行主题定制，稍后讲解
            // modifyVars: {
            //   '@primary-color': '#1DA57A', // 全局主色
            // },
          },
        },
      },
    });
    ```

    **注意：** 如果你在 `createStyleImportPlugin` 中配置了 `libs` 选项，那么你就不需要在 `main.tsx` 或 `App.tsx` 中 `import 'antd/dist/antd.css';` (或 `antd.min.css`)。如果你的 Ant Design 版本是 5.x 及以上，通常不再需要手动引入 CSS，组件会自带样式。但为了兼容性和更灵活的按需加载，上述配置仍是推荐的。

3.  **验证按需加载：**

    你可以在 `src/App.tsx` 中简单使用一个 Ant Design 组件，比如 `Button`：

    ```tsx
    // src/App.tsx
    import React from 'react';
    import { Button } from 'antd'; // 从 antd 引入组件

    function App() {
      return (
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <h1>Hello Ant Design!</h1>
          <Button type="primary">Primary Button</Button>
          <p>Edit <code>src/App.tsx</code> and save to test HMR</p>
        </div>
      );
    }

    export default App;
    ```

    重新运行 `npm run dev`，如果一切正常，你会在页面上看到一个蓝色的主按钮。此时，你可以检查打包体积，会发现只有 `Button` 组件及其依赖的样式被引入，而不是整个 Ant Design 库。

#### 1.2.3 Ant Design 主题定制 (简单修改主色调)

Ant Design 使用 Less 作为样式语言，允许你通过修改 Less 变量来定制主题。

1.  **在 `vite.config.ts` 中配置 `modifyVars`：**

    在 `vite.config.ts` 的 `css.preprocessorOptions.less` 配置中添加 `modifyVars`。例如，我们将主色调改为绿色：

    ```typescript
    import { defineConfig } from 'vite';
    import react from '@vitejs/plugin-react';
    import { createStyleImportPlugin, AntdResolve } from 'vite-plugin-style-import';

    // https://vitejs.dev/config/
    export default defineConfig({
      plugins: [
        react(),
        createStyleImportPlugin({
          resolves: [AntdResolve()],
          libs: [
            {
              libraryName: 'antd',
              esModule: true,
              resolveStyle: (name) => {
                return `antd/es/${name}/style/index.js`;
              },
            },
          ],
        }),
      ],
      css: {
        preprocessorOptions: {
          less: {
            javascriptEnabled: true,
            modifyVars: {
              '@primary-color': '#00B96B', // 设置为绿色
              // '@link-color': '#1DA57A', // 链接色
              // '@border-radius-base': '2px', // 组件/浮层圆角
              // ... 更多变量
            },
          },
        },
      },
    });
    ```

    保存 `vite.config.ts` 文件并重新运行 `npm run dev`，你会发现 `Button` 的颜色变成了你设置的绿色。你可以查阅 Ant Design 官方文档了解更多可定制的 Less 变量。

---

### 1.3 配置 HTTP 请求库 (Axios)

**Axios** 是一个基于 Promise 的 HTTP 客户端，用于浏览器和 Node.js。它具有拦截请求和响应、自动转换 JSON 数据等优点，非常适合与我们的 NestJS 后端进行通信。

#### 1.3.1 安装 Axios

```bash
npm install axios --save
# 或者 yarn add axios
# 或者 pnpm add axios
```

#### 1.3.2 创建 Axios 实例并配置基础 URL

我们将在 `src/services/api.ts` 中创建一个 Axios 实例，并配置后端 API 的基础 URL。这样，所有的请求都将基于这个 URL，避免重复书写。

创建一个 `src/services/api.ts` 文件：

```typescript
// src/services/api.ts
import axios from 'axios';

// 从环境变量中获取后端 API 基础 URL
// 在 Vite 项目中，环境变量需要以 VITE_ 为前缀，并在 .env 文件中定义
// 例如：VITE_API_BASE_URL=http://localhost:3000/api
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
});

export default api;
```

**创建 `.env` 文件：**
在项目根目录创建 `.env` 文件（如果还没有的话），并添加后端 API 的基础 URL：

```
# .env
VITE_API_BASE_URL=http://localhost:3000/api
```

`http://localhost:3000/api` 是我们之前 NestJS 后端设置的默认地址（假设 NestJS 在 3000 端口运行，并且全局前缀是 `/api`）。

#### 1.3.3 设置请求拦截器（统一添加 JWT Token）

当用户登录成功后，我们需要将 JWT Token 存储起来，并在后续的每个需要认证的请求中自动携带该 Token。请求拦截器是实现这一功能的理想选择。

修改 `src/services/api.ts` 文件：

```typescript
// src/services/api.ts
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 每次请求前，从 localStorage 中获取 JWT Token
    const token = localStorage.getItem('access_token'); // 假设 Token 存储在 localStorage
    if (token) {
      // 如果 Token 存在，则添加到请求头中
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default api;
```

#### 1.3.4 设置响应拦截器（统一处理错误、过期 Token）

响应拦截器可以统一处理后端返回的错误响应，例如，如果 Token 过期或无效，我们可以自动跳转到登录页面。

继续修改 `src/services/api.ts` 文件：

```typescript
// src/services/api.ts
import axios from 'axios';
import { message } from 'antd'; // 引入 Ant Design 的消息提示组件

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 (同上)
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 可以在这里对成功的响应进行一些通用处理，例如解构数据
    return response;
  },
  (error) => {
    // 统一错误处理
    if (error.response) {
      const { status, data } = error.response;
      let errorMessage = data.message || '服务器内部错误';

      switch (status) {
        case 401: // 未授权或 Token 过期
          message.error('登录状态已过期，请重新登录。');
          localStorage.removeItem('access_token'); // 清除过期 Token
          // 可以选择跳转到登录页面，这里假设 /login 是登录路由
          window.location.href = '/login';
          break;
        case 403: // 禁止访问
          message.error('您没有权限访问此资源。');
          break;
        case 404: // 资源未找到
          message.error('请求的资源不存在。');
          break;
        case 400: // Bad Request (例如验证失败)
          // NestJS 的 ValidationPipe 返回的错误信息可能是一个数组
          if (Array.isArray(errorMessage)) {
            errorMessage = errorMessage.join('; ');
          }
          message.error(`请求参数错误：${errorMessage}`);
          break;
        default:
          message.error(`请求失败：${errorMessage}`);
          break;
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      message.error('网络错误或服务器无响应，请检查网络。');
    } else {
      // 发送请求时发生错误
      message.error(`请求配置错误：${error.message}`);
    }
    return Promise.reject(error);
  }
);

export default api;
```

现在，你可以在任何组件或服务文件中导入 `api` 实例来发送请求了：

```typescript
// src/pages/LoginPage.tsx (示例)
import api from '../services/api';

async function login(username, password) {
  try {
    const response = await api.post('/auth/login', { username, password });
    localStorage.setItem('access_token', response.data.access_token);
    // 登录成功后的跳转逻辑
  } catch (error) {
    // 错误已由拦截器处理，这里可以做一些 UI 上的反馈
    console.error('Login failed:', error);
  }
}
```

---

### 1.4 React Router 基础配置

**React Router** 是 React 应用中最流行的路由库，它允许你声明式地定义和管理应用中的 URL 路由，实现单页应用（SPA）的无刷新导航。

#### 1.4.1 安装 `react-router-dom`

```bash
npm install react-router-dom --save
# 或者 yarn add react-router-dom
# 或者 pnpm add react-router-dom
```

#### 1.4.2 配置基本的路由结构

我们将在 `src/App.tsx`（或独立的路由配置文件）中配置应用的路由。

首先，在 `src/pages` 目录下创建一些简单的页面组件：

* `src/pages/HomePage.tsx`
* `src/pages/LoginPage.tsx`
* `src/pages/RegisterPage.tsx`
* `src/pages/UserDashboard.tsx`
* `src/pages/NotFoundPage.tsx`

```tsx
// src/pages/HomePage.tsx
import React from 'react';
import { Typography } from 'antd';

const { Title, Paragraph } = Typography;

const HomePage: React.FC = () => {
  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <Title level={2}>欢迎来到家政维修服务平台！</Title>
      <Paragraph>我们提供专业的上门维修服务。</Paragraph>
      {/* 可以在这里添加服务分类、热门服务等 */}
    </div>
  );
};

export default HomePage;

// src/pages/LoginPage.tsx
import React from 'react';
import { Typography } from 'antd';

const { Title } = Typography;

const LoginPage: React.FC = () => {
  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <Title level={2}>登录</Title>
      <p>这里将是登录表单...</p>
    </div>
  );
};

export default LoginPage;

// src/pages/RegisterPage.tsx
import React from 'react';
import { Typography } from 'antd';

const { Title } = Typography;

const RegisterPage: React.FC = () => {
  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <Title level={2}>注册</Title>
      <p>这里将是注册表单...</p>
    </div>
  );
};

export default RegisterPage;

// src/pages/UserDashboard.tsx
import React from 'react';
import { Typography } from 'antd';

const { Title } = Typography;

const UserDashboard: React.FC = () => {
  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <Title level={2}>用户中心</Title>
      <p>这里将展示用户个人信息、订单等。</p>
    </div>
  );
};

export default UserDashboard;

// src/pages/NotFoundPage.tsx
import React from 'react';
import { Result, Button } from 'antd';
import { Link } from 'react-router-dom';

const NotFoundPage: React.FC = () => {
  return (
    <Result
      status="404"
      title="404"
      subTitle="抱歉，您访问的页面不存在。"
      extra={
        <Button type="primary">
          <Link to="/">返回首页</Link>
        </Button>
      }
    />
  );
};

export default NotFoundPage;
```

然后，修改 `src/App.tsx` 来配置路由：

```tsx
// src/App.tsx
import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { Layout, Menu, Button } from 'antd';
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import UserDashboard from './pages/UserDashboard';
import NotFoundPage from './pages/NotFoundPage';
import './App.css'; // 引入应用样式

const { Header, Content, Footer } = Layout;

function App() {
  return (
    <Router>
      <Layout className="layout">
        <Header>
          <div className="logo" /> {/* 可以放置你的 Logo */}
          <Menu theme="dark" mode="horizontal" defaultSelectedKeys={['1']}>
            <Menu.Item key="1">
              <Link to="/">首页</Link>
            </Menu.Item>
            <Menu.Item key="2">
              <Link to="/login">登录</Link>
            </Menu.Item>
            <Menu.Item key="3">
              <Link to="/register">注册</Link>
            </Menu.Item>
            <Menu.Item key="4">
              <Link to="/dashboard">用户中心</Link>
            </Menu.Item>
          </Menu>
        </Header>
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/login" element={<LoginPage />} />
              <Route path="/register" element={<RegisterPage />} />
              <Route path="/dashboard" element={<UserDashboard />} />
              <Route path="*" element={<NotFoundPage />} /> {/* 404 页面 */}
            </Routes>
          </div>
        </Content>
        <Footer style={{ textAlign: 'center' }}>
          家政维修服务平台 ©{new Date().getFullYear()} Created by Your Name
        </Footer>
      </Layout>
    </Router>
  );
}

export default App;
```

为了让布局更好看，我们还需要在 `src/App.css` 中添加一些基础样式：

```css
/* src/App.css */
.layout {
  min-height: 100vh;
}

.logo {
  float: left;
  width: 120px;
  height: 31px;
  margin: 16px 24px 16px 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  /* 可以替换为你的 Logo 图片 */
}

.site-layout-content {
  background: #fff;
  padding: 24px;
  margin-top: 24px;
  min-height: calc(100vh - 134px - 48px); /* 134px 是Header+Footer的高度，48px是上下padding */
  border-radius: 8px; /* 添加圆角 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 添加阴影 */
}

/* 覆盖 Ant Design 默认样式，使其内容居中 */
.ant-layout-header {
  padding: 0 20px !important;
}

.ant-menu-horizontal {
  line-height: 64px !important;
}

.ant-menu-item {
  line-height: 64px !important;
}
```

#### 1.4.3 实现路由守卫 (Private Routes)

路由守卫用于保护某些页面，只有在用户登录或满足特定条件时才能访问。

1.  **创建 `PrivateRoute` 组件：**

    创建一个 `src/components/PrivateRoute.tsx` 文件：

    ```tsx
    // src/components/PrivateRoute.tsx
    import React from 'react';
    import { Navigate, Outlet } from 'react-router-dom';
    import { Spin } from 'antd'; // 可用于加载状态

    interface PrivateRouteProps {
      allowedRoles?: string[]; // 允许访问的角色列表
    }

    const PrivateRoute: React.FC<PrivateRouteProps> = ({ allowedRoles }) => {
      const isAuthenticated = !!localStorage.getItem('access_token'); // 检查用户是否登录
      // 实际应用中，你可能需要一个全局状态来判断认证状态和用户角色
      // 例如：const { isAuthenticated, userRole, isLoading } = useAuth();

      // if (isLoading) {
      //   return <div style={{ textAlign: 'center', padding: '50px' }}><Spin size="large" /></div>;
      // }

      if (!isAuthenticated) {
        // 未登录，重定向到登录页
        return <Navigate to="/login" replace />;
      }

      // 如果有角色限制，并且当前用户角色不在允许列表中
      // if (allowedRoles && !allowedRoles.includes(userRole)) {
      //   // 可以重定向到无权限页面或首页
      //   return <Navigate to="/403" replace />;
      // }

      // 已认证且符合权限，渲染子路由
      return <Outlet />;
    };

    export default PrivateRoute;
    ```

    **注意：** 上述 `PrivateRoute` 中的角色判断和加载状态 (`isLoading`) 需要一个全局认证上下文（`useAuth`）。我们会在后续篇章中实现一个简单的认证 Context 来管理用户登录状态和角色信息。

2.  **在 `App.tsx` 中使用 `PrivateRoute`：**

    ```tsx
    // src/App.tsx (部分)
    import PrivateRoute from './components/PrivateRoute';
    // ... 其他导入

    function App() {
      return (
        <Router>
          <Layout className="layout">
            {/* ... Header, Content, Footer */}
            <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
              <div className="site-layout-content">
                <Routes>
                  <Route path="/" element={<HomePage />} />
                  <Route path="/login" element={<LoginPage />} />
                  <Route path="/register" element={<RegisterPage />} />

                  {/* 保护需要登录才能访问的路由 */}
                  <Route element={<PrivateRoute />}>
                    <Route path="/dashboard" element={<UserDashboard />} />
                    {/* <Route path="/user/profile" element={<UserProfilePage />} />
                    <Route path="/user/orders" element={<UserOrdersPage />} /> */}
                  </Route>

                  {/* 保护需要特定角色才能访问的路由 (例如管理员后台) */}
                  {/* <Route element={<PrivateRoute allowedRoles={['admin']} />}>
                    <Route path="/admin/dashboard" element={<AdminDashboard />} />
                  </Route> */}

                  <Route path="*" element={<NotFoundPage />} />
                </Routes>
              </div>
            </Content>
            {/* ... Footer */}
          </Layout>
        </Router>
      );
    }

    export default App;
    ```

---

**本篇总结：**

我们成功地完成了前端项目的初始化，并配置了开发所需的基础工具：
* 使用 **Vite** 搭建了 React + TypeScript 项目，提供了快速的开发体验。
* 集成了 **Ant Design** 并配置了按需加载，确保了界面组件的丰富性和项目的性能。
* 配置了 **Axios** 作为 HTTP 请求库，实现了请求拦截器（用于添加 JWT Token）和响应拦截器（用于统一错误处理和 Token 过期跳转）。
* 设置了 **React Router** 的基本路由结构，并初步实现了路由守卫，为后续页面的开发和权限控制打下了基础。

至此，你的前端项目已经准备就绪，可以开始与后端 API 进行真正的交互了！

---

您对本篇内容有什么疑问吗？如果没有，我们就可以进入**第二篇：认证与用户中心界面实现**了。