-----

# 《新一代在线家政维修服务平台：从需求到实现的企业级开发实战》

## 第一篇：项目起步——打造你的本地开发环境

**摘要：** 还在为复杂的开发环境配置头疼吗？别担心！在本篇教程中，我们将手把手带你从零开始，为“新一代在线家政维修服务平台”搭建一套完整的本地开发环境。即使你是编程小白，也能轻松搞定 Node.js、MySQL 的安装与配置，并初始化你的第一个 NestJS 项目。我们将一起为这个支持多角色协作、AI 智能匹配、以及各种创新功能的 O2O 服务平台打下坚实基础。

-----

## 1.1 开发环境：准备你的工具箱

在开始“盖房子”之前，我们得先把工具准备好。这里主要涉及 **Node.js** 和 **MySQL**，它们分别是我们后端服务的运行环境和数据存储中心。

### Node.js 18+ LTS：JavaScript 的魔法棒

**Node.js** 就像一个可以让你的 JavaScript 代码在服务器上运行的“魔法棒”。NestJS 应用就运行在 Node.js 上，所以它是我们首先要安装的。为了确保稳定性和性能，我们强烈推荐使用 **LTS (长期支持)** 版本。

**推荐版本：** Node.js 18.x 或 20.x LTS

#### 安装步骤：

1.  **官方网站下载：**

      * 打开 [https://nodejs.org/](https://nodejs.org/)
      * 点击下载**LTS 版本**（这个版本更稳定，适合长期使用）

2.  **安装验证：**
    安装完成后，我们需要确认 Node.js 和它自带的包管理工具 **npm** 都安装成功了。打开你的终端（或命令提示符），输入以下命令：

    ```bash
    # 验证Node.js版本
    node --version
    # 如果看到类似 v18.x.x 或 v20.x.x 的输出，说明安装成功了！

    # 验证npm版本
    npm --version
    # 看到 9.x.x 或更高版本就好
    ```

3.  **配置 npm 镜像源（可选，但强烈推荐！）：**
    npm 在下载依赖包时，有时候速度会比较慢。我们可以把它的下载地址换成国内的“淘宝镜像源”，这样下载速度会飞快！

    ```bash
    # 设置淘宝镜像源
    npm config set registry https://registry.npmmirror.com

    # 验证一下是否设置成功
    npm config get registry
    # 应该显示 'https://registry.npmmirror.com/'
    ```

4.  **安装 pnpm（推荐，更高效的包管理器）：**
    **pnpm** 是另一个非常优秀的包管理工具，它比 npm 更快，而且能帮你节省大量的磁盘空间。既然我们要搞企业级开发，当然要用更高效的工具啦！

    ```bash
    # 全局安装 pnpm
    npm install -g pnpm

    # 验证安装
    pnpm --version
    # 看到版本号就说明安装成功了
    ```

### MySQL 8.0：你的数据管家

**MySQL 8.0** 是一个非常流行且强大的关系型数据库，就像你的数据管家，能帮你把平台上的所有信息（用户、服务、订单等等）整理得井井有条。它性能出色，还支持 JSON 数据格式，非常适合我们的项目。

#### 安装步骤：

1.  **下载 MySQL Community Server：**

      * 访问 [移除了无效网址]
      * 根据你的操作系统（Windows、macOS 或 Linux）选择合适的版本下载。

2.  **Windows 安装：**

      * 下载 `MySQL Installer for Windows` 安装程序。
      * 运行安装程序，选择 "**Developer Default**"（开发者默认）安装类型，这会安装开发所需的所有组件。
      * **重要提示：** 在安装过程中，会让你设置 **root 用户密码**。**这个密码一定要牢记！** 它是你管理 MySQL 的“钥匙”。
      * 默认的数据库端口是 **3306**。

3.  **macOS 安装：**

      * 如果你是 macOS 用户，**Homebrew** 是一个超级方便的包管理器。强烈推荐使用它来安装 MySQL。

    <!-- end list -->

    ```bash
    # 使用 Homebrew 安装 MySQL
    brew install mysql

    # 启动 MySQL 服务
    brew services start mysql

    # 设置 root 密码和安全配置
    # 这一步会引导你设置 root 密码，并进行一些安全加固，跟着提示操作就好
    mysql_secure_installation
    ```

4.  **Linux 安装（以 Ubuntu/Debian 为例）：**

      * 如果你用的是 Ubuntu 或 Debian 系统：

    <!-- end list -->

    ```bash
    # 更新包列表，确保能下载到最新的软件
    sudo apt update

    # 安装 MySQL 服务器
    sudo apt install mysql-server

    # 启动 MySQL 服务
    sudo systemctl start mysql

    # 设置 root 密码和安全配置，这和 macOS 的步骤类似
    sudo mysql_secure_installation
    ```

5.  **创建项目数据库：**
    安装好 MySQL 后，我们需要为我们的“家政维修服务平台”创建一个专门的数据库。

    ```sql
    -- 首先，连接到 MySQL。会提示你输入 root 密码
    mysql -u root -p

    -- 创建我们的项目数据库，名字叫 home_service_platform
    -- CHARACTER SET utf8mb4 和 COLLATE utf8mb4_unicode_ci 是为了支持中文和表情符号
    CREATE DATABASE home_service_platform
    CHARACTER SET utf8mb4
    COLLATE utf8mb4_unicode_ci;

    -- （可选，但推荐！）创建一个专门用于我们项目的数据库用户
    -- 这样可以避免直接使用 root 用户，提高安全性
    CREATE USER 'homeservice'@'localhost' IDENTIFIED BY 'your_password';
    -- 记得把 'your_password' 替换成一个你自己的密码！

    -- 授予这个新用户对 home_service_platform 数据库的所有权限
    GRANT ALL PRIVILEGES ON home_service_platform.* TO 'homeservice'@'localhost';

    -- 刷新权限，让刚才的更改生效
    FLUSH PRIVILEGES;

    -- 验证一下数据库是否创建成功
    SHOW DATABASES;
    -- 应该能看到 home_service_platform 在列表里

    -- 进入我们刚创建的数据库
    USE home_service_platform;
    ```

### 开发工具：你的得力助手

有了 Node.js 和 MySQL，我们还需要一些好用的开发工具来提高效率。

**必备工具：**

  * **IDE (集成开发环境)**：**Visual Studio Code (VS Code)**

      * **VS Code** 是一个轻量级但功能强大的代码编辑器，前端和后端开发都非常适合。
      * **推荐插件：** 安装以下插件能让你的开发体验更棒：
          * **TypeScript:** NestJS 主要使用 TypeScript 编写，这个插件能提供智能提示和错误检查。
          * **Prettier:** 自动格式化代码，让你的代码风格统一漂亮。
          * **ESLint:** 检查代码规范，帮你发现潜在的错误。
          * **Thunder Client:** 一个轻量级的 API 测试工具，可以直接在 VS Code 里发送 HTTP 请求。

  * **数据库管理工具**：

      * **DBeaver** (免费，功能强大，支持多种数据库)
      * **Navicat** (付费，界面友好，功能全面)
        选择一个你喜欢的，用来查看和管理你的 MySQL 数据库。

  * **API 测试工具**：

      * **Postman** (功能全面，业界标准)
      * **Apifox** (国人开发，集 API 设计、开发、测试于一体)
        这两个工具用于测试你的后端接口，确保它们正常工作。

  * **版本控制**：**Git**

      * **Git** 是一个强大的版本控制工具，能帮你管理代码的历史版本，方便团队协作。它通常会随着你的系统或开发工具一起安装。

**VS Code 插件安装：**
你可以在 VS Code 的扩展市场里搜索并安装这些插件，或者在终端里运行以下命令：

```bash
# 安装推荐插件
code --install-extension ms-vscode.vscode-typescript-next
code --install-extension esbenp.prettier-vscode
code --install-extension dbaeumer.vscode-eslint
code --install-extension rangav.vscode-thunder-client
```

-----

## 1.2 NestJS 项目初始化：搭起你的服务骨架

现在，我们有了 Node.js 这个运行环境，也准备好了 MySQL 数据库，是时候用 **NestJS** 来搭建我们后端服务的骨架了。

### NestJS CLI 安装与项目创建

**NestJS** 是一个非常棒的 Node.js 框架，它借鉴了 Angular 的一些优秀思想，能帮你快速构建高效、可扩展的服务端应用。

1.  **安装 NestJS CLI：**
    **NestJS CLI (命令行工具)** 能帮你快速创建 NestJS 项目和各种模块。

    ```bash
    # 全局安装 NestJS CLI
    npm install -g @nestjs/cli

    # 验证安装是否成功
    nest --version
    # 看到版本号就说明安装好了
    ```

2.  **创建项目：**
    现在，用 NestJS CLI 来创建一个新的后端项目吧！

    ```bash
    # 创建一个名为 home-service-platform-backend 的新项目
    nest new home-service-platform-backend

    # 过程中会让你选择包管理器，推荐选择 pnpm，然后回车
    # 创建成功后，进入项目目录
    cd home-service-platform-backend
    ```

3.  **安装核心依赖：**
    进入项目目录后，我们需要安装一些常用且必要的依赖包，它们会为我们的项目提供各种功能，比如数据库操作、用户认证、数据校验等。

    ```bash
    # 数据库相关：TypeORM 是 NestJS 常用的 ORM（对象关系映射）工具，mysql2 是 MySQL 的驱动
    pnpm add @nestjs/typeorm typeorm mysql2

    # 配置管理：方便我们读取 .env 文件中的环境变量
    pnpm add @nestjs/config

    # 认证相关：用于用户登录、权限管理等
    pnpm add @nestjs/jwt @nestjs/passport passport passport-jwt passport-local
    pnpm add bcryptjs # 用于密码加密

    # 验证相关：用于校验前端传来的数据是否符合规范
    pnpm add class-validator class-transformer

    # API 文档：自动生成接口文档，方便前后端协作和接口测试
    pnpm add @nestjs/swagger swagger-ui-express

    # 开发依赖：只在开发阶段需要，比如一些 TypeScript 的类型定义
    pnpm add -D @types/passport-jwt @types/passport-local @types/bcryptjs
    ```

### 项目结构规划与模块化设计

一个清晰的项目结构是高效开发的基础。基于“新一代在线家政维修服务平台”的需求，我们规划了如下的模块化结构。这样，不同的功能放在不同的文件夹里，方便管理和扩展。

```
src/
├── common/              # 公共模块，存放所有模块都可能用到的工具和通用代码
│   ├── decorators/      # 自定义装饰器（高级用法，简化代码）
│   ├── filters/         # 异常过滤器（统一处理错误信息）
│   ├── guards/          # 守卫（控制接口访问权限）
│   ├── interceptors/    # 拦截器（统一处理请求/响应）
│   ├── pipes/           # 管道（数据转换和校验）
│   └── utils/           # 工具函数（各种常用的小工具）
├── config/              # 配置模块，存放各种环境配置
│   ├── database.config.ts # 数据库连接配置
│   ├── jwt.config.ts    # JWT 认证配置
│   └── app.config.ts    # 应用通用配置
├── modules/             # 业务模块，每个文件夹代表一个核心业务功能
│   ├── auth/            # 认证模块（用户注册、登录、权限管理）
│   ├── users/           # 用户管理（用户信息的增删改查）
│   ├── providers/       # 服务商管理（维修师傅、家政人员等）
│   ├── services/        # 服务管理（家政服务、维修服务等具体项目）
│   ├── orders/          # 订单管理（用户下单、支付、完成等流程）
│   ├── payments/        # 支付模块（与第三方支付平台对接）
│   ├── reviews/         # 评价模块（用户对服务商和服务的评价）
│   ├── notifications/   # 通知模块（短信、邮件、站内信通知）
│   └── admin/           # 管理员模块（后台管理功能）
├── database/            # 数据库相关，与数据库结构和数据有关
│   ├── entities/        # 实体定义（数据库表的 TypeScript 类表示）
│   ├── migrations/      # 数据库迁移（管理数据库结构变更）
│   └── seeds/           # 种子数据（初始化一些测试数据）
├── app.module.ts        # 应用的根模块，整合所有子模块
└── main.ts              # 应用的入口文件，负责启动 NestJS 应用
```

### TypeORM + MySQL 连接配置

现在，我们需要告诉 NestJS 我们的 MySQL 数据库在哪里，以及怎么连接它。这就像给 NestJS 一张地图，让它知道怎么找到数据。

1.  **创建环境配置文件：**
    我们使用 `.env` 文件来存放敏感信息和配置，比如数据库密码。在项目根目录下创建一个名为 `.env` 的文件。

    ```bash
    # 在项目根目录创建 .env 文件
    touch .env
    ```

2.  **配置环境变量：**
    打开 `home-service-platform-backend/.env` 文件，复制粘贴以下内容。**记得把 `your_password` 替换成你在安装 MySQL 时为 `homeservice` 用户设置的密码！**

    ```env
    # .env
    # 应用配置
    NODE_ENV=development # 当前环境是开发环境
    PORT=3000            # 应用运行在 3000 端口

    # 数据库配置
    DB_HOST=localhost            # 数据库地址，本地就是 localhost
    DB_PORT=3306                 # 数据库端口，MySQL 默认是 3306
    DB_USERNAME=homeservice      # 连接数据库的用户名
    DB_PASSWORD=your_password    # 连接数据库的密码，请替换成你自己的密码！
    DB_DATABASE=home_service_platform # 要连接的数据库名称

    # JWT 配置 (用于用户认证和授权)
    JWT_SECRET=your-super-secret-jwt-key-change-in-production # JWT 签名秘钥，生产环境一定要改掉！
    JWT_EXPIRES_IN=7d            # JWT 有效期 7 天

    # 文件上传配置 (未来会用到)
    UPLOAD_DEST=./uploads        # 文件上传存储路径
    MAX_FILE_SIZE=5242880        # 单个文件最大大小 (5MB)

    # 第三方服务配置 (未来会用到，如微信支付、支付宝支付)
    WECHAT_APP_ID=your_wechat_app_id
    WECHAT_APP_SECRET=your_wechat_app_secret
    ALIPAY_APP_ID=your_alipay_app_id
    ```

3.  **创建数据库配置：**
    我们将在 `src/config` 目录下创建一个 `database.config.ts` 文件，用来从 `.env` 文件中读取数据库配置，并告诉 TypeORM 如何连接数据库。

    ```typescript
    // src/config/database.config.ts
    import { TypeOrmModuleOptions } from '@nestjs/typeorm';
    import { ConfigService } from '@nestjs/config';

    // 这是一个工厂函数，根据 ConfigService 获取到的环境变量，返回 TypeORM 的配置对象
    export const getDatabaseConfig = (
      configService: ConfigService,
    ): TypeOrmModuleOptions => ({
      type: 'mysql', // 数据库类型是 MySQL
      host: configService.get('DB_HOST'), // 从环境变量获取数据库地址
      port: parseInt(configService.get('DB_PORT'), 10), // 从环境变量获取数据库端口
      username: configService.get('DB_USERNAME'), // 从环境变量获取用户名
      password: configService.get('DB_PASSWORD'), // 从环境变量获取密码
      database: configService.get('DB_DATABASE'), // 从环境变量获取数据库名称
      entities: [__dirname + '/../**/*.entity{.ts,.js}'], // 告诉 TypeORM 实体（数据库表对应的类）在哪里
      synchronize: configService.get('NODE_ENV') === 'development', // 开发环境下自动同步数据库结构，生产环境关闭！
      logging: configService.get('NODE_ENV') === 'development', // 开发环境下开启 SQL 日志
      timezone: '+00:00', // 设置时区，确保时间存储正确
      charset: 'utf8mb4', // 字符集，支持中文和表情
    });
    ```

-----

## 1.3 基础配置与中间件：让你的服务更健壮

搭建好骨架后，我们需要配置一些基础功能，让你的服务更健壮、更安全、更易用。

### 环境变量配置与验证

我们之前创建了 `.env` 文件和 `database.config.ts`。现在，我们需要把这些配置模块导入到 NestJS 的根模块 `AppModule` 中，让整个应用都能访问到这些配置。

1.  **更新 app.module.ts：**
    打开 `src/app.module.ts` 文件，修改为以下内容。这里我们导入了 `ConfigModule` 和 `TypeOrmModule`，并使用我们之前定义的 `getDatabaseConfig` 来配置 TypeORM。

    ```typescript
    // src/app.module.ts
    import { Module } from '@nestjs/common';
    import { ConfigModule, ConfigService } from '@nestjs/config'; // 导入 ConfigModule 和 ConfigService
    import { TypeOrmModule } from '@nestjs/typeorm'; // 导入 TypeOrmModule
    import { getDatabaseConfig } from './config/database.config'; // 导入我们自己的数据库配置函数
    import { AppController } from './app.controller';
    import { AppService } from './app.service';

    @Module({
      imports: [
        // 配置模块 - 全局可用
        // .forRoot() 方法用于注册 ConfigModule，让它在整个应用中都可以访问到环境变量
        ConfigModule.forRoot({
          isGlobal: true, // 设置为全局模块，这样其他模块就不用重复导入了
          envFilePath: '.env', // 指定 .env 文件的路径
          validationSchema: null, // 暂时不添加验证，后续可以加上 Joi 或 Zod 来校验环境变量
        }),

        // 数据库模块 - 异步配置，因为需要等待 ConfigModule 加载完环境变量
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule], // 依赖 ConfigModule
          useFactory: getDatabaseConfig, // 使用我们自己的数据库配置函数
          inject: [ConfigService], // 注入 ConfigService，让 useFactory 能访问到它
        }),
      ],
      controllers: [AppController],
      providers: [AppService],
    })
    export class AppModule {}
    ```

### 全局验证管道与异常过滤器

为了让你的 API 更健壮，我们需要对请求数据进行统一的校验，并在出现错误时返回统一的错误信息。

1.  **创建全局异常过滤器：**
    当后端服务发生错误时，我们不希望直接把详细的错误信息暴露给用户。异常过滤器就像一个“错误处理中心”，能帮你把错误信息格式化成统一的、友好的 JSON 格式。在 `src/common/filters` 目录下创建 `http-exception.filter.ts`：

    ```typescript
    // src/common/filters/http-exception.filter.ts
    import {
      ExceptionFilter,
      Catch,
      ArgumentsHost,
      HttpException,
      HttpStatus,
    } from '@nestjs/common';
    import { Request, Response } from 'express';

    @Catch(HttpException) // @Catch(HttpException) 表示这个过滤器只处理 HttpException 类型的异常
    export class HttpExceptionFilter implements ExceptionFilter {
      catch(exception: HttpException, host: ArgumentsHost) {
        const ctx = host.switchToHttp(); // 获取 HTTP 上下文
        const response = ctx.getResponse<Response>(); // 获取响应对象
        const request = ctx.getRequest<Request>(); // 获取请求对象
        const status = exception.getStatus(); // 获取 HTTP 状态码

        // 构建统一的错误响应格式
        const errorResponse = {
          code: status, // 错误码，就是 HTTP 状态码
          timestamp: new Date().toISOString(), // 错误发生的时间
          path: request.url, // 请求的路径
          method: request.method, // 请求的方法 (GET, POST 等)
          message: exception.message || null, // 错误信息，如果 HttpException 没有具体信息则为 null
        };

        // 发送 JSON 格式的错误响应
        response.status(status).json(errorResponse);
      }
    }
    ```

### Swagger API 文档配置

为了让前端开发人员、测试人员，甚至你未来的自己都能轻松理解你的后端接口，我们需要一套自动生成的 API 文档。**Swagger** 就是这样的一个强大工具！

1.  **更新 main.ts：**
    `main.ts` 是 NestJS 应用的入口文件。我们在这里会进行一些全局配置，包括应用前缀、全局验证管道、全局异常过滤器，以及最重要的 Swagger API 文档集成。

    ```typescript
    // src/main.ts
    import { NestFactory } from '@nestjs/core';
    import { ValidationPipe } from '@nestjs/common'; // 导入全局验证管道
    import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger'; // 导入 Swagger 相关模块
    import { AppModule } from './app.module';
    import { HttpExceptionFilter } from './common/filters/http-exception.filter'; // 导入我们自己的异常过滤器

    async function bootstrap() {
      const app = await NestFactory.create(AppModule);

      // 全局前缀：所有 API 接口都会带上 /api，例如 /api/users
      app.setGlobalPrefix('api');

      // 全局验证管道
      // ValidationPipe 会自动根据 DTO (数据传输对象) 对请求体进行校验
      app.useGlobalPipes(
        new ValidationPipe({
          whitelist: true, // 过滤掉 DTO 中没有定义的字段
          forbidNonWhitelisted: true, // 如果请求体包含 DTO 中没有定义的字段，直接报错
          transform: true, // 自动转换请求参数到 DTO 定义的类型
          transformOptions: {
            enableImplicitConversion: true, // 允许隐式类型转换 (比如字符串 '123' 自动转成数字 123)
          },
        }),
      );

      // 全局异常过滤器
      // 捕获所有 HTTP 异常，并用我们自定义的格式返回
      app.useGlobalFilters(new HttpExceptionFilter());

      // CORS 配置 (跨域资源共享)
      // 允许特定来源的网页（比如你的前端应用）访问后端接口
      app.enableCors({
        origin: ['http://localhost:3000', 'http://localhost:5173'], // 允许这些地址访问你的 API
        credentials: true, // 允许发送 Cookie 等凭证信息
      });

      // Swagger 配置：生成美观的 API 文档
      const config = new DocumentBuilder()
        .setTitle('新一代在线家政维修服务平台 API') // API 文档标题
        .setDescription('企业级O2O服务平台后端API文档') // API 文档描述
        .setVersion('1.0') // API 版本
        .addBearerAuth() // 添加 Bearer Token 认证，方便测试需要认证的接口
        .addTag('认证', '用户认证相关接口') // 给接口分组
        .addTag('用户管理', '用户信息管理接口')
        .addTag('服务商管理', '服务商相关接口')
        .addTag('服务管理', '服务项目管理接口')
        .addTag('订单管理', '订单流程管理接口')
        .build(); // 构建 Swagger 文档配置

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup('api-docs', app, document); // 在 /api-docs 路径下开放 Swagger 文档

      // 从环境变量获取端口，如果没有设置则默认 3000
      const port = process.env.PORT || 3000;
      await app.listen(port); // 启动应用

      console.log(`🚀 应用启动成功！`);
      console.log(`📖 API文档地址: http://localhost:${port}/api-docs`);
      console.log(`🔗 应用地址: http://localhost:${port}/api`);
    }

    bootstrap(); // 调用启动函数
    ```

2.  **启动项目验证：**
    现在，所有配置都已完成！是时候启动你的 NestJS 应用，看看效果了。

    ```bash
    # 在项目根目录，启动开发服务器
    pnpm run start:dev
    ```

    打开你的浏览器，访问以下地址进行验证：

      * `http://localhost:3000/api` - 你的 NestJS 应用应该会显示一个简单的“Hello World\!”页面（或者 404 错误，因为我们还没写具体接口，但至少说明应用启动了）。
      * `http://localhost:3000/api-docs` - 你会看到一个漂亮的 Swagger UI 界面，里面列出了你配置的 API 文档。

-----

### 第一篇总结：

太棒了！你已经迈出了“新一代在线家政维修服务平台”开发的第一步，并成功完成了：

✅ **开发环境搭建**：安装并配置了 Node.js 18+、MySQL 8.0，并准备好了 VS Code 等开发工具。
✅ **项目初始化**：创建了 NestJS 项目，安装了所有核心依赖，并规划了清晰的项目结构。
✅ **基础配置**：实现了数据库连接、环境变量管理，并设置了全局验证管道和异常过滤器，让你的服务更健壮。
✅ **API 文档**：集成了 Swagger，为你自动生成了漂亮的 API 文档，方便后续的接口开发和测试。

你现在拥有了一个坚实的企业级开发基础，为后续构建复杂的功能奠定了牢固的基础。

-----

准备好深入数据世界了吗？**请告诉我“继续”**，我们将进入第二篇：**数据库设计与核心实体建模**，开始构建完整的数据模型，定义平台上的所有数据。