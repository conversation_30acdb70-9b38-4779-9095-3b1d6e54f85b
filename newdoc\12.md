好的，我们继续进行前端部分的**第四篇：订单管理与状态流转**。

在本篇中，我们将重点实现用户和技师的订单管理功能，以及订单状态的流转逻辑。这将是用户和技师与平台互动最频繁的模块之一。

---

## 第四篇：订单管理与状态流转

### 4.1 用户订单列表

我们将创建“我的订单”页面，让用户可以查看自己的所有订单，并支持按订单状态进行筛选。

#### 4.1.1 用户订单列表页面 (`src/pages/UserOrderListPage.tsx`)

```tsx
// src/pages/UserOrderListPage.tsx
import React, { useState, useEffect } from 'react';
import { Card, List, Typography, Spin, Tag, Space, Select, Empty, Button, Modal, message } from 'antd';
import { Link, useNavigate } from 'react-router-dom';
import api from '../services/api';
import moment from 'moment';

const { Title, Text } = Typography;
const { Option } = Select;

// 定义订单类型，与后端 Order 实体字段对应
interface Order {
  id: number;
  orderNumber: string;
  serviceName: string;
  status: 'pending' | 'accepted' | 'inProgress' | 'completed' | 'cancelled' | 'refunded' | 'quoted'; // 示例状态
  totalAmount: number;
  scheduledTime: string;
  technicianName?: string; // 可能有技师信息
  createdAt: string;
  // ... 其他订单字段
}

const UserOrderListPage: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [filterStatus, setFilterStatus] = useState<string | undefined>(undefined);
  const navigate = useNavigate();

  // 模拟订单状态映射，方便显示
  const orderStatusMap: { [key: string]: { text: string; color: string } } = {
    pending: { text: '待接受', color: 'blue' },
    accepted: { text: '已接受', color: 'geekblue' },
    inProgress: { text: '服务中', color: 'processing' },
    completed: { text: '已完成', color: 'success' },
    cancelled: { text: '已取消', color: 'default' },
    refunded: { text: '已退款', color: 'warning' },
    quoted: { text: '已报价', color: 'magenta' }, // 技师报价后
  };

  useEffect(() => {
    fetchOrders();
  }, [filterStatus]); // 依赖 filterStatus 变化时重新获取

  const fetchOrders = async () => {
    setLoading(true);
    try {
      const params = filterStatus ? { status: filterStatus } : {};
      const response = await api.get('/orders/user/list', { params }); // 假设后端获取用户订单列表 API
      setOrders(response.data);
    } catch (error) {
      console.error('获取订单列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelOrder = (orderId: number) => {
    Modal.confirm({
      title: '确认取消订单',
      content: '确定要取消此订单吗？订单取消后将无法恢复。',
      okText: '确认取消',
      cancelText: '我再想想',
      onOk: async () => {
        try {
          await api.post(`/orders/${orderId}/cancel`); // 假设后端取消订单 API
          message.success('订单取消成功！');
          fetchOrders(); // 刷新列表
        } catch (error) {
          console.error('取消订单失败:', error);
          // 错误已由拦截器处理
        }
      },
    });
  };

  // 根据订单状态显示操作按钮
  const renderOrderActions = (order: Order) => {
    switch (order.status) {
      case 'pending':
        return (
          <Button key="cancel" type="link" danger onClick={() => handleCancelOrder(order.id)}>
            取消订单
          </Button>
        );
      case 'completed':
        return (
          <Space key="actions">
            <Button type="link">
              <Link to={`/order/${order.id}/review`}>去评价</Link> {/* 评价链接 */}
            </Button>
            <Button type="link">
              <Link to={`/order/${order.id}/after-sales`}>申请售后</Link> {/* 售后链接 */}
            </Button>
          </Space>
        );
      case 'quoted':
        return (
          <Space key="actions">
            <Button type="primary" onClick={() => navigate(`/order/${order.id}`)}>
              查看报价
            </Button>
            <Button type="link" danger onClick={() => handleCancelOrder(order.id)}>
              取消订单
            </Button>
          </Space>
        );
      default:
        return null;
    }
  };

  return (
    <Card title="我的订单" style={{ maxWidth: 1200, margin: '20px auto' }}>
      <div style={{ marginBottom: 20 }}>
        <Space>
          <Text>筛选状态:</Text>
          <Select
            style={{ width: 150 }}
            allowClear
            placeholder="所有状态"
            onChange={value => setFilterStatus(value)}
            value={filterStatus}
          >
            {Object.keys(orderStatusMap).map(key => (
              <Option key={key} value={key}>{orderStatusMap[key].text}</Option>
            ))}
          </Select>
        </Space>
      </div>

      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}><Spin size="large" tip="加载订单列表..." /></div>
      ) : (
        <List
          itemLayout="vertical"
          dataSource={orders}
          locale={{ emptyText: <Empty description="您还没有任何订单" /> }}
          renderItem={order => (
            <List.Item
              key={order.id}
              actions={[
                <Button key="view" type="link" onClick={() => navigate(`/order/${order.id}`)}>
                  查看详情
                </Button>,
                renderOrderActions(order)
              ].filter(Boolean)} // 过滤掉 null 的操作
            >
              <List.Item.Meta
                title={<Link to={`/order/${order.id}`}>{`订单号: ${order.orderNumber}`}</Link>}
                description={
                  <Space direction="vertical">
                    <Text strong>服务名称: {order.serviceName}</Text>
                    <Text>预约时间: {moment(order.scheduledTime).format('YYYY-MM-DD HH:mm')}</Text>
                    <Text>总金额: <Text strong type="danger">¥{order.totalAmount.toFixed(2)}</Text></Text>
                    {order.technicianName && <Text>技师: {order.technicianName}</Text>}
                    <Tag color={orderStatusMap[order.status]?.color || 'default'}>
                      {orderStatusMap[order.status]?.text || order.status}
                    </Tag>
                    <Text type="secondary">创建时间: {moment(order.createdAt).format('YYYY-MM-DD HH:mm')}</Text>
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      )}
    </Card>
  );
};

export default UserOrderListPage;
```

**在 `App.tsx` 中添加路由：**

```tsx
// src/App.tsx (部分)
import UserOrderListPage from './pages/UserOrderListPage';
// ... 其他 import

function App() {
  return (
    <Router>
      <Layout className="layout">
        {/* ... Header, Content, Footer */}
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              {/* ... 其他公共路由 */}

              <Route element={<PrivateRoute />}>
                {/* ... 其他受保护的用户路由 */}
                <Route path="/orders" element={<UserOrderListPage />} /> {/* 新增的订单列表页面 */}
              </Route>

              {/* ... 404 路由 */}
            </Routes>
          </div>
        </Content>
        {/* ... Footer */}
      </Layout>
    </Router>
  );
}

export default App;
```

### 4.2 订单详情页

用户点击订单列表中的订单项，将跳转到订单详情页，展示该订单的所有详细信息和当前状态。

#### 4.2.1 订单详情页面 (`src/pages/OrderDetailPage.tsx`)

```tsx
// src/pages/OrderDetailPage.tsx
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Typography, Spin, Descriptions, Button, Divider, Space, Tag, Steps, message, Timeline } from 'antd';
import api from '../services/api';
import moment from 'moment';
import { useUser } from '../contexts/UserContext';

const { Title, Text } = Typography;
const { Step } = Steps;

// 扩展 Order 类型，包含更多详情字段
interface OrderDetail {
  id: number;
  orderNumber: string;
  serviceName: string;
  serviceDescription: string; // 服务描述
  status: 'pending' | 'accepted' | 'inProgress' | 'completed' | 'cancelled' | 'refunded' | 'quoted';
  totalAmount: number;
  scheduledTime: string;
  remarks?: string;
  receiverName: string;
  receiverPhone: string;
  serviceAddress: string; // 完整地址字符串
  technician?: {
    id: number;
    username: string;
    phoneNumber: string;
    avatarUrl?: string;
  };
  createdAt: string;
  acceptedAt?: string;
  startedAt?: string;
  completedAt?: string;
  cancelledAt?: string;
  quotedPrice?: number; // 技师报价
  quoteDescription?: string; // 技师报价说明
  // ... 其他订单详情字段，如支付信息、评价状态等
}

// 订单状态步骤条配置
const orderStepsConfig = [
  { key: 'pending', title: '待接受', description: '等待技师接单' },
  { key: 'accepted', title: '已接受', description: '技师已接单' },
  { key: 'inProgress', title: '服务中', description: '技师正在服务' },
  { key: 'completed', title: '已完成', description: '服务已完成' },
  // 'cancelled', 'refunded', 'quoted' 等状态不一定在主流程步骤条中显示
];

const OrderDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useUser(); // 获取当前用户角色

  const [order, setOrder] = useState<OrderDetail | null>(null);
  const [loading, setLoading] = useState(true);

  // 模拟订单状态映射
  const orderStatusMap: { [key: string]: { text: string; color: string } } = {
    pending: { text: '待接受', color: 'blue' },
    accepted: { text: '已接受', color: 'geekblue' },
    inProgress: { text: '服务中', color: 'processing' },
    completed: { text: '已完成', color: 'success' },
    cancelled: { text: '已取消', color: 'default' },
    refunded: { text: '已退款', color: 'warning' },
    quoted: { text: '已报价', color: 'magenta' },
  };

  useEffect(() => {
    if (id) {
      fetchOrderDetail(parseInt(id));
    }
  }, [id]);

  const fetchOrderDetail = async (orderId: number) => {
    setLoading(true);
    try {
      // 后端需要提供一个 /orders/:id 接口来获取订单详情
      const response = await api.get(`/orders/${orderId}`);
      setOrder(response.data);
    } catch (error) {
      console.error('获取订单详情失败:', error);
      message.error('订单详情加载失败。');
      navigate('/orders'); // 获取失败后跳转回订单列表
    } finally {
      setLoading(false);
    }
  };

  const handleCancelOrder = () => {
    Modal.confirm({
      title: '确认取消订单',
      content: '确定要取消此订单吗？订单取消后将无法恢复。',
      okText: '确认取消',
      cancelText: '我再想想',
      onOk: async () => {
        try {
          await api.post(`/orders/${order!.id}/cancel`);
          message.success('订单取消成功！');
          fetchOrderDetail(order!.id); // 刷新详情
        } catch (error) {
          console.error('取消订单失败:', error);
        }
      },
    });
  };

  // 技师端操作：接受/拒绝订单
  const handleTechnicianAcceptOrder = async () => {
    Modal.confirm({
      title: '接受订单',
      content: '确认接受此订单吗？接受后将成为您的待处理任务。',
      okText: '确认接受',
      cancelText: '取消',
      onOk: async () => {
        try {
          await api.post(`/orders/${order!.id}/accept`); // 假设后端接受订单 API
          message.success('订单已接受！');
          fetchOrderDetail(order!.id);
        } catch (error) {
          console.error('接受订单失败:', error);
        }
      },
    });
  };

  const handleTechnicianRejectOrder = async () => {
    Modal.confirm({
      title: '拒绝订单',
      content: '确认拒绝此订单吗？',
      okText: '确认拒绝',
      cancelText: '取消',
      onOk: async () => {
        try {
          await api.post(`/orders/${order!.id}/reject`); // 假设后端拒绝订单 API
          message.success('订单已拒绝！');
          fetchOrderDetail(order!.id);
          navigate('/technician/tasks'); // 拒绝后跳转到技师任务列表
        } catch (error) {
          console.error('拒绝订单失败:', error);
        }
      },
    });
  };

  // 技师端操作：开始服务
  const handleTechnicianStartService = async () => {
    Modal.confirm({
      title: '开始服务',
      content: '确认开始此服务吗？',
      okText: '确认开始',
      cancelText: '取消',
      onOk: async () => {
        try {
          await api.post(`/orders/${order!.id}/start`); // 假设后端开始服务 API
          message.success('服务已开始！');
          fetchOrderDetail(order!.id);
        } catch (error) {
          console.error('开始服务失败:', error);
        }
      },
    });
  };

  // 技师端操作：提交报价 (如果订单类型是需要报价的)
  const handleTechnicianSubmitQuote = () => {
    // 这里需要一个模态框或新页面来填写报价金额和说明
    Modal.info({
      title: '提交报价',
      content: (
        <Form
          form={form} // 使用当前组件的form实例
          onFinish={async (values) => {
            try {
              await api.post(`/orders/${order!.id}/quote`, values); // 假设后端提交报价 API
              message.success('报价已提交！');
              Modal.destroyAll(); // 关闭所有模态框
              fetchOrderDetail(order!.id);
            } catch (error) {
              console.error('提交报价失败:', error);
            }
          }}
          layout="vertical"
        >
          <Form.Item name="quotedPrice" label="报价金额" rules={[{ required: true, message: '请输入报价金额！' }]}>
            <Input type="number" step="0.01" prefix="¥" />
          </Form.Item>
          <Form.Item name="quoteDescription" label="报价说明">
            <Input.TextArea rows={3} placeholder="请填写报价说明（可选）" />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">提交</Button>
          </Form.Item>
        </Form>
      ),
      okButtonProps: { style: { display: 'none' } }, // 隐藏默认的OK按钮
      onCancel: () => form.resetFields(),
    });
  };


  // 技师端操作：完成服务
  const handleTechnicianCompleteService = async () => {
    Modal.confirm({
      title: '完成服务',
      content: '确认此服务已完成吗？',
      okText: '确认完成',
      cancelText: '取消',
      onOk: async () => {
        try {
          await api.post(`/orders/${order!.id}/complete`); // 假设后端完成服务 API
          message.success('服务已完成！');
          fetchOrderDetail(order!.id);
        } catch (error) {
          console.error('完成服务失败:', error);
        }
      },
    });
  };


  if (loading || !order) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}><Spin size="large" tip="加载订单详情..." /></div>
    );
  }

  // 计算步骤条当前激活的步骤
  const currentStepIndex = orderStepsConfig.findIndex(step => step.key === order.status);
  // 对于非主流程状态，可以设置当前步骤为最后一个主流程步骤，或显示额外信息
  const stepsCurrent = currentStepIndex !== -1 ? currentStepIndex : orderStepsConfig.length - 1;


  return (
    <Card title={`订单详情: ${order.orderNumber}`} style={{ maxWidth: 1000, margin: '20px auto' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 订单状态步骤条 */}
        <Steps current={stepsCurrent} status={['cancelled', 'refunded'].includes(order.status) ? 'error' : 'process'} style={{ marginBottom: 30 }}>
          {orderStepsConfig.map(item => (
            <Step key={item.key} title={item.title} description={item.description} />
          ))}
        </Steps>

        <Descriptions bordered column={2} title="订单信息">
          <Descriptions.Item label="订单号">{order.orderNumber}</Descriptions.Item>
          <Descriptions.Item label="服务名称">{order.serviceName}</Descriptions.Item>
          <Descriptions.Item label="服务描述" span={2}>
            {order.serviceDescription}
          </Descriptions.Item>
          <Descriptions.Item label="预约时间">
            {moment(order.scheduledTime).format('YYYY-MM-DD HH:mm')}
          </Descriptions.Item>
          <Descriptions.Item label="订单状态">
            <Tag color={orderStatusMap[order.status]?.color || 'default'}>
              {orderStatusMap[order.status]?.text || order.status}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="总金额">
            <Text strong type="danger">¥{order.totalAmount.toFixed(2)}</Text>
          </Descriptions.Item>
          {order.quotedPrice && (
            <Descriptions.Item label="技师报价">
              <Text strong type="success">¥{order.quotedPrice.toFixed(2)}</Text>
            </Descriptions.Item>
          )}
          {order.quoteDescription && (
            <Descriptions.Item label="报价说明" span={2}>
              {order.quoteDescription}
            </Descriptions.Item>
          )}
          <Descriptions.Item label="备注" span={2}>
            {order.remarks || '无'}
          </Descriptions.Item>
        </Descriptions>

        <Divider />

        <Descriptions bordered column={1} title="服务地址信息">
          <Descriptions.Item label="收件人">{order.receiverName}</Descriptions.Item>
          <Descriptions.Item label="手机号">{order.receiverPhone}</Descriptions.Item>
          <Descriptions.Item label="服务地址">{order.serviceAddress}</Descriptions.Item>
        </Descriptions>

        {order.technician && (
          <>
            <Divider />
            <Descriptions bordered column={1} title="技师信息">
              <Descriptions.Item label="技师姓名">{order.technician.username}</Descriptions.Item>
              <Descriptions.Item label="联系电话">{order.technician.phoneNumber}</Descriptions.Item>
              {/* 技师头像可以展示 */}
            </Descriptions>
          </>
        )}
        
        {/* 订单时间轴 (可选，但推荐) */}
        <Divider />
        <Title level={4}>订单时间轴</Title>
        <Timeline>
          <Timeline.Item>
            <Text strong>订单创建</Text>
            <p>{moment(order.createdAt).format('YYYY-MM-DD HH:mm:ss')}</p>
          </Timeline.Item>
          {order.acceptedAt && (
            <Timeline.Item color="blue">
              <Text strong>订单接受</Text>
              <p>{moment(order.acceptedAt).format('YYYY-MM-DD HH:mm:ss')}</p>
            </Timeline.Item>
          )}
          {order.startedAt && (
            <Timeline.Item color="green">
              <Text strong>服务开始</Text>
              <p>{moment(order.startedAt).format('YYYY-MM-DD HH:mm:ss')}</p>
            </Timeline.Item>
          )}
          {order.completedAt && (
            <Timeline.Item color="orange">
              <Text strong>服务完成</Text>
              <p>{moment(order.completedAt).format('YYYY-MM-DD HH:mm:ss')}</p>
            </Timeline.Item>
          )}
          {order.cancelledAt && (
            <Timeline.Item color="red">
              <Text strong>订单取消</Text>
              <p>{moment(order.cancelledAt).format('YYYY-MM-DD HH:mm:ss')}</p>
            </Timeline.Item>
          )}
          {/* 其他状态的时间点 */}
        </Timeline>


        {/* 用户端操作按钮 */}
        {user?.role === 'user' && (
          <div style={{ textAlign: 'center', marginTop: '30px' }}>
            {order.status === 'pending' && (
              <Button type="primary" danger onClick={handleCancelOrder}>
                取消订单
              </Button>
            )}
            {order.status === 'quoted' && ( // 技师报价后，用户可以确认或取消
                <Space>
                    <Button type="primary">确认报价并支付</Button> {/* 待实现支付流程 */}
                    <Button type="default" danger onClick={handleCancelOrder}>取消订单</Button>
                </Space>
            )}
            {order.status === 'completed' && (
              <Space>
                <Button type="primary">
                  <Link to={`/order/${order.id}/review`}>去评价</Link>
                </Button>
                <Button type="default">
                  <Link to={`/order/${order.id}/after-sales`}>申请售后</Link>
                </Button>
              </Space>
            )}
          </div>
        )}

        {/* 技师端操作按钮 */}
        {user?.role === 'technician' && (
          <div style={{ textAlign: 'center', marginTop: '30px' }}>
            {order.status === 'pending' && (
              <Space>
                <Button type="primary" onClick={handleTechnicianAcceptOrder}>接受订单</Button>
                <Button type="default" danger onClick={handleTechnicianRejectOrder}>拒绝订单</Button>
              </Space>
            )}
            {order.status === 'accepted' && ( // 接受后可能需要技师报价，或者直接开始服务
                <Space>
                    <Button type="primary" onClick={handleTechnicianStartService}>开始服务</Button>
                    <Button type="default" onClick={handleTechnicianSubmitQuote}>提交报价</Button> {/* 如果需要报价 */}
                </Space>
            )}
            {order.status === 'inProgress' && (
              <Button type="primary" onClick={handleTechnicianCompleteService}>完成服务</Button>
            )}
          </div>
        )}
      </Space>
    </Card>
  );
};

export default OrderDetailPage;
```

**在 `App.tsx` 中添加路由：**

```tsx
// src/App.tsx (部分)
import OrderDetailPage from './pages/OrderDetailPage';
// ... 其他 import

function App() {
  return (
    <Router>
      <Layout className="layout">
        {/* ... Header, Content, Footer */}
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              {/* ... 其他公共路由 */}

              <Route element={<PrivateRoute />}>
                {/* ... 其他受保护的用户路由 */}
                <Route path="/order/:id" element={<OrderDetailPage />} /> {/* 新增的订单详情路由 */}
              </Route>

              {/* ... 404 路由 */}
            </Routes>
          </div>
        </Content>
        {/* ... Footer */}
      </Layout>
    </Router>
  );
}

export default App;
```

### 4.3 订单操作 (用户端)

在 `UserOrderListPage.tsx` 和 `OrderDetailPage.tsx` 中，我们已经集成了用户端的取消订单功能。

#### 4.3.1 申请售后 (预留)

对于已完成的订单，用户可能需要申请售后。这将是一个独立页面或模态框，用于填写售后原因、上传照片等。

```tsx
// src/pages/OrderAfterSalesPage.tsx (预留，后续实现)
import React from 'react';
import { Card, Typography, Form, Input, Button, message, Upload, Space } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import api from '../services/api';

const { Title, Paragraph } = Typography;
const { TextArea } = Input;

const OrderAfterSalesPage: React.FC = () => {
  const { id } = useParams<{ id: string }>(); // 获取订单ID
  const [loading, setLoading] = React.useState(false);

  const onFinish = async (values: any) => {
    setLoading(true);
    try {
      // 假设后端售后申请 API 为 /orders/:id/after-sales
      // 需要将 values.attachments 中的文件信息处理成后端可接受的格式
      const payload = {
        ...values,
        attachments: values.attachments ? values.attachments.fileList.map((file: any) => file.response?.url || file.url) : [],
      };
      await api.post(`/orders/${id}/after-sales`, payload);
      message.success('售后申请已提交，请等待处理！');
      // 提交成功后可以跳转回订单详情页或订单列表
      // navigate(`/order/${id}`);
    } catch (error) {
      console.error('售后申请失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card title={`订单 ${id} 的售后申请`} style={{ maxWidth: 800, margin: '20px auto' }}>
      <Paragraph>
        请详细描述您的问题，并提供相关照片以便我们更好地处理。
      </Paragraph>
      <Form
        name="after_sales_form"
        onFinish={onFinish}
        layout="vertical"
      >
        <Form.Item
          name="reason"
          label="售后原因"
          rules={[{ required: true, message: '请填写售后原因！' }]}
        >
          <TextArea rows={4} placeholder="请详细描述您遇到的问题..." />
        </Form.Item>
        <Form.Item
          name="attachments"
          label="上传照片 (可选)"
          valuePropName="fileList"
          getValueFromEvent={(e: any) => (Array.isArray(e) ? e : e && e.fileList)}
        >
          <Upload
            name="file"
            action="/api/upload" // 替换为你的文件上传接口
            listType="picture"
            maxCount={3}
          >
            <Button icon={<UploadOutlined />}>上传照片</Button>
          </Upload>
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading} block>
            提交申请
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default OrderAfterSalesPage;
```

**在 `App.tsx` 中添加路由：**

```tsx
// src/App.tsx (部分)
import OrderAfterSalesPage from './pages/OrderAfterSalesPage'; // 引入售后页面
// ... 其他 import

function App() {
  return (
    <Router>
      <Layout className="layout">
        {/* ... Header, Content, Footer */}
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              {/* ... 其他公共路由 */}

              <Route element={<PrivateRoute />}>
                {/* ... 其他受保护的用户路由 */}
                <Route path="/order/:id/after-sales" element={<OrderAfterSalesPage />} /> {/* 新增的售后申请路由 */}
              </Route>

              {/* ... 404 路由 */}
            </Routes>
          </div>
        </Content>
        {/* ... Footer */}
      </Layout>
    </Router>
  );
}

export default App;
```

### 4.4 技师端订单管理

技师将拥有自己的订单列表和订单详情页面，能够执行接受、拒绝、开始服务、提交报价、完成服务等操作。

#### 4.4.1 技师订单列表页面 (`src/pages/TechnicianOrderListPage.tsx`)

```tsx
// src/pages/TechnicianOrderListPage.tsx
import React, { useState, useEffect } from 'react';
import { Card, List, Typography, Spin, Tag, Space, Select, Empty, Button, Modal, message } from 'antd';
import { Link, useNavigate } from 'react-router-dom';
import api from '../services/api';
import moment from 'moment';
import { useUser } from '../contexts/UserContext'; // 确保只有技师能访问

const { Title, Text } = Typography;
const { Option } = Select;

// 订单类型与用户端类似，但可能包含技师特有的字段
interface TechnicianOrder {
  id: number;
  orderNumber: string;
  serviceName: string;
  status: 'pending' | 'accepted' | 'inProgress' | 'completed' | 'cancelled' | 'refunded' | 'quoted';
  totalAmount: number;
  scheduledTime: string;
  user?: { // 订单关联的用户信息
    id: number;
    username: string;
    phoneNumber: string;
  };
  serviceAddress: string;
  createdAt: string;
  // ... 其他订单字段
}

const TechnicianOrderListPage: React.FC = () => {
  const [orders, setOrders] = useState<TechnicianOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [filterStatus, setFilterStatus] = useState<string | undefined>(undefined);
  const navigate = useNavigate();
  const { user } = useUser();

  // 确保只有技师可以访问此页面
  useEffect(() => {
    if (!user || user.role !== 'technician') {
      message.error('您没有权限访问此页面！');
      navigate('/dashboard'); // 或者其他无权限页面
    }
  }, [user, navigate]);


  // 模拟订单状态映射，方便显示
  const orderStatusMap: { [key: string]: { text: string; color: string } } = {
    pending: { text: '新订单', color: 'blue' },
    accepted: { text: '已接受', color: 'geekblue' },
    inProgress: { text: '服务中', color: 'processing' },
    completed: { text: '已完成', color: 'success' },
    cancelled: { text: '已取消', color: 'default' },
    refunded: { text: '已退款', color: 'warning' },
    quoted: { text: '已报价待确认', color: 'magenta' },
  };

  useEffect(() => {
    if (user && user.role === 'technician') {
      fetchTechnicianOrders();
    }
  }, [filterStatus, user]); // 依赖 filterStatus 和 user 变化时重新获取

  const fetchTechnicianOrders = async () => {
    setLoading(true);
    try {
      const params = filterStatus ? { status: filterStatus } : {};
      // 假设后端技师订单列表 API 为 /orders/technician/list
      const response = await api.get('/orders/technician/list', { params });
      setOrders(response.data);
    } catch (error) {
      console.error('获取技师订单列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 技师端操作按钮
  const renderTechnicianOrderActions = (order: TechnicianOrder) => {
    switch (order.status) {
      case 'pending':
        return (
          <Space key="actions">
            <Button type="primary" onClick={() => navigate(`/technician/order/${order.id}`)}>
              查看详情并处理
            </Button>
          </Space>
        );
      case 'accepted':
        return (
          <Space key="actions">
            <Button type="primary" onClick={() => navigate(`/technician/order/${order.id}`)}>
              去服务
            </Button>
          </Space>
        );
      case 'quoted':
        return (
          <Space key="actions">
            <Button type="primary" onClick={() => navigate(`/technician/order/${order.id}`)}>
              查看报价详情
            </Button>
          </Space>
        );
      case 'inProgress':
        return (
          <Button key="complete" type="primary" onClick={() => navigate(`/technician/order/${order.id}`)}>
            完成服务
          </Button>
        );
      default:
        return null;
    }
  };

  if (user && user.role !== 'technician') {
    return null; // 如果不是技师，直接不渲染内容或显示无权限
  }

  return (
    <Card title="我的任务" style={{ maxWidth: 1200, margin: '20px auto' }}>
      <div style={{ marginBottom: 20 }}>
        <Space>
          <Text>筛选状态:</Text>
          <Select
            style={{ width: 200 }}
            allowClear
            placeholder="所有任务状态"
            onChange={value => setFilterStatus(value)}
            value={filterStatus}
          >
            {Object.keys(orderStatusMap).map(key => (
              <Option key={key} value={key}>{orderStatusMap[key].text}</Option>
            ))}
          </Select>
        </Space>
      </div>

      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}><Spin size="large" tip="加载任务列表..." /></div>
      ) : (
        <List
          itemLayout="vertical"
          dataSource={orders}
          locale={{ emptyText: <Empty description="暂无待处理任务" /> }}
          renderItem={order => (
            <List.Item
              key={order.id}
              actions={[
                renderTechnicianOrderActions(order)
              ].filter(Boolean)}
            >
              <List.Item.Meta
                title={<Link to={`/technician/order/${order.id}`}>{`订单号: ${order.orderNumber}`}</Link>}
                description={
                  <Space direction="vertical">
                    <Text strong>服务名称: {order.serviceName}</Text>
                    <Text>预约时间: {moment(order.scheduledTime).format('YYYY-MM-DD HH:mm')}</Text>
                    <Text>用户: {order.user?.username} ({order.user?.phoneNumber})</Text>
                    <Text>地址: {order.serviceAddress}</Text>
                    <Tag color={orderStatusMap[order.status]?.color || 'default'}>
                      {orderStatusMap[order.status]?.text || order.status}
                    </Tag>
                    <Text type="secondary">创建时间: {moment(order.createdAt).format('YYYY-MM-DD HH:mm')}</Text>
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      )}
    </Card>
  );
};

export default TechnicianOrderListPage;
```

**在 `App.tsx` 中添加路由：**

```tsx
// src/App.tsx (部分)
import TechnicianOrderListPage from './pages/TechnicianOrderListPage';
// ... 其他 import

function App() {
  return (
    <Router>
      <Layout className="layout">
        {/* ... Header, Content, Footer */}
        <Content style={{ padding: '0 50px', minHeight: 'calc(100vh - 134px)' }}>
          <div className="site-layout-content">
            <Routes>
              {/* ... 其他公共路由 */}

              <Route element={<PrivateRoute allowedRoles={['technician']} />}> {/* 技师专属路由 */}
                <Route path="/technician/tasks" element={<TechnicianOrderListPage />} /> {/* 技师任务列表 */}
                {/* 技师订单详情页可以直接复用 OrderDetailPage，但在其中添加技师特有操作 */}
                <Route path="/technician/order/:id" element={<OrderDetailPage />} /> 
              </Route>

              {/* ... 404 路由 */}
            </Routes>
          </div>
        </Content>
        {/* ... Footer */}
      </Layout>
    </Router>
  );
}

export default App;
```

#### 4.4.2 技师订单详情页 (复用 `OrderDetailPage` 并根据角色展示操作)

在 `OrderDetailPage.tsx` 中，我们已经根据 `user?.role` 字段判断并渲染了用户端和技师端不同的操作按钮（接受/拒绝订单、开始服务、提交报价、完成服务）。这样可以复用同一个详情页，但根据用户角色提供不同的交互。

**重要提示：** `OrderDetailPage` 中技师操作按钮的实现需要确保后端 API `post(`/orders/${order!.id}/accept`)`, `post(`/orders/${order!.id}/reject`)`, `post(`/orders/${order!.id}/start`)`, `post(`/orders/${order!.id}/quote`)`, `post(`/orders/${order!.id}/complete`)` 已经存在。

---

**本篇总结：**

在这一篇中，我们完成了：

* **用户订单列表：** 实现用户查看自己所有订单的页面，并支持按状态筛选。
* **订单详情页：** 详细展示订单信息，包括服务、地址、技师等，并根据用户角色显示不同的操作按钮（用户端取消、申请售后；技师端接受、拒绝、开始、提交报价、完成）。
* **技师端订单管理：** 为技师提供了专属的订单列表，使其能够管理分配给自己的任务。

通过这些功能的实现，订单的生命周期和不同角色的交互逻辑得到了初步实现。

---

您对本篇内容有什么疑问吗？如果没有，我们就可以进入**第五篇：技师端功能与评价体系**了。