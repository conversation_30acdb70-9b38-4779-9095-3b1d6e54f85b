好的，我们现在开始定制化系列教程的第一篇内容。

---

### **《新一代在线家政维修服务平台：从需求到实现的高效开发实战》**

#### **定制化系列教程 第一篇：项目初始化与本地环境搭建**

**摘要：** 在本篇教程中，我们将为“家维在线系统”的后端服务进行项目初始化，并详细指导如何在本地环境中搭建Node.js和MySQL数据库，确保所有开发所需的基础设施都已准备就绪。

---

### **1.1 Node.js 及 npm 安装**

Node.js 是运行 NestJS 应用程序的JavaScript运行时环境，而 npm (Node Package Manager) 是Node.js的包管理器，用于安装、管理和分享JavaScript库。

**推荐版本：** 始终推荐安装 Node.js 的 **LTS (长期支持) 版本**，因为它更稳定，并会得到更长时间的支持。

**安装步骤：**

1.  **访问 Node.js 官方网站：**
    打开浏览器，访问 [https://nodejs.org/](https://nodejs.org/)。

2.  **下载安装包：**
    * 您会看到两个主要版本：LTS (推荐) 和 Current (最新功能)。请点击 **LTS 版本**的下载按钮。
    * 网站会自动检测您的操作系统（Windows, macOS, Linux）并提供相应的安装包。

    * **Windows 用户：** 下载 `.msi` 安装包。
    * **macOS 用户：** 下载 `.pkg` 安装包。
    * **Linux 用户：** 官方网站会提供针对不同发行版的安装指南（通常是使用包管理器如 `apt` 或 `yum`）。

3.  **执行安装程序：**
    * **Windows / macOS：** 双击下载的安装包，按照安装向导的指示一步步进行。通常，选择默认设置即可，确保勾选了“Install Node.js runtime”和“npm package manager”。
    * **Linux：** 按照官方网站的指南，在终端中执行相应的命令。

4.  **验证安装：**
    安装完成后，打开您的终端（或命令提示符/PowerShell），输入以下命令并按回车：

    ```bash
    node -v
    npm -v
    ```

    如果安装成功，您将看到 Node.js 和 npm 的版本号，例如：

    ```
    v18.17.0
    9.6.7
    ```
    （版本号可能因您下载的LTS版本而异）

### **1.2 MySQL 服务器本地安装**

MySQL 是一个流行的开源关系型数据库管理系统，我们将用它来存储家维在线系统的所有业务数据。

**安装工具推荐：**

* **MySQL Community Server：** MySQL的免费开源版本，适合本地开发。
* **MySQL Workbench / DBeaver：** 图形化数据库管理工具，方便创建数据库、执行SQL查询和管理数据。强烈推荐安装其中一个。

**安装步骤（以通用指导为例，具体步骤请参考官方文档）：**

1.  **访问 MySQL 官方网站：**
    打开浏览器，访问 [https://dev.mysql.com/downloads/](https://dev.mysql.com/downloads/)。

2.  **下载 MySQL Community Server：**
    * 在下载页面，找到 `MySQL Community (GPL) Downloads`。
    * 点击 `MySQL Community Server`。
    * 选择适合您操作系统的版本进行下载。
        * **Windows 用户：** 推荐下载 `MySQL Installer for Windows`，它会引导您安装MySQL服务器、Workbench和其他开发工具。
        * **macOS 用户：** 下载 `.dmg` 文件。
        * **Linux 用户：** 官方提供了针对RPM或DEB包的安装指南，或者您可以使用对应的包管理器安装。

3.  **执行安装程序：**
    * **Windows (使用 MySQL Installer)：**
        * 运行下载的 `mysql-installer-community-*.msi` 文件。
        * 选择 `Developer Default` 或 `Custom` 安装类型。如果选择 `Custom`，请确保勾选 `MySQL Server` 和 `MySQL Workbench`。
        * 在安装过程中，您会被要求设置 **MySQL `root` 用户的密码**。**请务必记住这个密码**，它将是您连接数据库的关键。
        * 默认情况下，MySQL会在端口 `3306` 上运行。
        * 完成安装向导。

    * **macOS：**
        * 打开 `.dmg` 文件，双击 `mysql-*.pkg` 安装包。
        * 按照安装向导提示进行，同样会要求您设置 `root` 密码。
        * 安装完成后，您可以在“系统偏好设置”中找到MySQL面板，用于启动/停止MySQL服务。

    * **Linux：**
        * 根据您的发行版（如Ubuntu/Debian使用`apt`，CentOS/Fedora使用`yum`/`dnf`），在终端执行相应的命令安装MySQL服务器。
        * 安装后，通常需要运行一个安全脚本来设置root密码、删除匿名用户等：`sudo mysql_secure_installation`。
        * 确保MySQL服务正在运行：`sudo systemctl start mysql` 或 `sudo service mysql start`。

4.  **安装数据库客户端工具 (推荐)：**
    * **MySQL Workbench：** 如果您在Windows上使用了MySQL Installer，可能已经包含了。否则，可以从MySQL官网单独下载安装。
    * **DBeaver：** 一个通用的数据库客户端，支持多种数据库，界面友好。可以从 [https://dbeaver.io/download/](https://dbeaver.io/download/) 下载。

5.  **创建数据库：**
    安装并启动MySQL服务器后，使用MySQL Workbench或DBeaver连接到您的本地MySQL实例（通常主机为`localhost`，端口`3306`，用户`root`，密码为您安装时设置的密码）。

    连接成功后，执行以下SQL命令来创建一个名为 `home_repair_db` 的数据库：

    ```sql
    CREATE DATABASE IF NOT EXISTS home_repair_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    ```
    这将创建我们的应用程序将使用的数据库。

### **1.3 NestJS 项目初始化**

现在，我们将使用 NestJS CLI 来初始化我们的后端项目。

1.  **安装 NestJS CLI：**
    打开终端（或命令提示符），运行以下命令全局安装NestJS CLI：

    ```bash
    npm install -g @nestjs/cli
    ```

2.  **创建新项目骨架：**
    选择一个您希望存放项目代码的目录，然后在终端中导航到该目录。运行以下命令创建新的NestJS项目：

    ```bash
    nest new home-repair-backend
    ```
    * 当提示选择包管理器时，选择 `npm`。
    * CLI会下载必要的依赖并设置好项目结构。

3.  **进入项目目录：**

    ```bash
    cd home-repair-backend
    ```

4.  **运行项目 (可选，验证安装)：**
    您可以尝试运行一下这个空的NestJS项目，确保一切正常：

    ```bash
    npm run start:dev
    ```
    如果一切顺利，您会在终端看到类似以下输出：

    ```
    [Nest] 12345  - 01/01/2023 10:00:00 AM     LOG [NestFactory] Starting Nest application...
    [Nest] 12345  - 01/01/2023 10:00:00 AM     LOG [AppModule] AppModule dependencies initialized
    [Nest] 12345  - 01/01/2023 10:00:00 AM     LOG [RoutesResolver] AppController {/}:
    [Nest] 12345  - 01/01/2023 10:00:00 AM     LOG [RouterExplorer] Mapped {/, GET} route
    [Nest] 12345  - 01/01/2023 10:00:00 AM     LOG [NestApplication] Nest application successfully started on port 3000
    ```
    您可以在浏览器中访问 `http://localhost:3000`，应该会看到 "Hello World!"。

5.  **项目结构概览：**
    `nest new` 命令会创建一个标准的NestJS项目结构，主要目录和文件如下：

    * `src/`: 应用程序的源代码目录。
        * `src/main.ts`: 应用程序的入口文件，负责创建NestJS应用实例。
        * `src/app.module.ts`: 根模块，应用程序的各个功能模块会在这里注册。
        * `src/app.controller.ts`: 默认的控制器，处理HTTP请求。
        * `src/app.service.ts`: 默认的服务，包含业务逻辑。
    * `test/`: 包含端到端测试。
    * `tsconfig.json`: TypeScript 配置文件。
    * `package.json`: 项目的元数据和依赖管理文件。
    * `nest-cli.json`: Nest CLI 配置文件。

### **1.4 NestJS 连接本地 MySQL 数据库**

现在，我们将配置 NestJS 项目，使其能够连接到我们刚刚在本地安装的 MySQL 数据库。我们将使用 TypeORM 作为ORM (Object Relational Mapper)，它允许我们用TypeScript/JavaScript类来操作数据库，而无需编写原生SQL。

1.  **安装必要的包：**
    在项目根目录（`home-repair-backend`）下，运行以下命令安装 TypeORM 和 MySQL 驱动：

    ```bash
    npm install @nestjs/typeorm typeorm mysql2
    ```
    * `@nestjs/typeorm`: NestJS 对 TypeORM 的集成模块。
    * `typeorm`: TypeORM 核心库。
    * `mysql2`: 用于与MySQL数据库通信的驱动程序。

2.  **配置 `AppModule` 连接数据库：**
    打开 `src/app.module.ts` 文件，修改其内容如下：

    ```typescript
    // src/app.module.ts
    import { Module } from '@nestjs/common';
    import { TypeOrmModule } from '@nestjs/typeorm'; // 导入 TypeOrmModule
    import { AppController } from './app.controller';
    import { AppService } from './app.service';

    @Module({
      imports: [
        TypeOrmModule.forRoot({
          type: 'mysql',            // 指定数据库类型为 MySQL
          host: 'localhost',        // 数据库主机地址，因为是本地安装，所以是 localhost
          port: 3306,               // MySQL 默认端口
          username: 'root',         // 您的 MySQL 用户名，通常默认为 root
          password: 'YOUR_MYSQL_ROOT_PASSWORD', // !!! 替换为您安装MySQL时设置的root密码
          database: 'home_repair_db', // 我们之前创建的数据库名称
          entities: [],             // 这里暂时为空，稍后我们会在这里添加实体类
          synchronize: true,        // !!! 注意：此选项在开发环境可以自动根据实体创建表，
                                    // !!! 但在生产环境强烈不建议使用，因为它可能会导致数据丢失。
                                    // !!! 生产环境应使用数据库迁移工具。
          logging: ['query', 'error'], // 打印SQL查询和错误信息，方便调试
        }),
      ],
      controllers: [AppController],
      providers: [AppService],
    })
    export class AppModule {}
    ```

    **请务必将 `YOUR_MYSQL_ROOT_PASSWORD` 替换为您在安装 MySQL 时设置的实际 `root` 用户密码。**

3.  **验证数据库连接：**
    保存 `src/app.module.ts` 文件。重新启动NestJS应用：

    ```bash
    npm run start:dev
    ```

    * **如果一切正常：** 您应该不会在终端看到关于数据库连接的错误。相反，如果您设置了 `logging: ['query', 'error']`，您可能会看到一些 TypeORM 相关的日志信息，表明它已成功初始化连接。
    * **如果出现错误：**
        * 检查 `username`, `password`, `database`, `host`, `port` 是否正确。
        * 确保您的本地 MySQL 服务器正在运行。
        * 检查MySQL的日志文件以获取更多信息。

**第一篇总结：**

恭喜！到目前为止，您已经成功地：

* 安装了 Node.js 和 npm。
* 在本地安装并配置了 MySQL 服务器，并创建了所需的数据库。
* 初始化了 NestJS 项目，并配置了 TypeORM 以连接到您的本地 MySQL 数据库。

您现在拥有一个强大的本地开发环境，为构建“家维在线系统”的后端服务奠定了坚实的基础。

---

**[请告诉我“继续”，我们就可以进入第二篇：核心基础设施搭建——数据库设计与API基础，开始定义实体和编写用户模块的API了。]**