### **《新一代在线家政维修服务平台：从需求到实现的企业级开发实战》**

#### **第一篇：项目初始化与本地环境搭建**

**摘要：** 在本篇教程中，我们将为"新一代在线家政维修服务平台"搭建完整的本地开发环境。基于您的需求规格说明书，我们将构建一个支持多角色协同、AI智能匹配、创新增值功能的企业级O2O服务平台。本篇将详细指导Node.js、MySQL的安装配置，以及NestJS项目的初始化和基础配置。

---

## **1.1 开发环境准备**

### **Node.js 18+ LTS 安装与配置**

Node.js是运行NestJS应用的JavaScript运行时环境。为了确保最佳的稳定性和性能，我们选择LTS（长期支持）版本。

**推荐版本：** Node.js 18.x 或 20.x LTS

**安装步骤：**

1. **访问官方网站下载：**
   - 打开 [https://nodejs.org/](https://nodejs.org/)
   - 点击下载 **LTS 版本**（推荐用于生产环境）

2. **安装验证：**
   ```bash
   # 验证Node.js版本
   node --version
   # 应显示 v18.x.x 或 v20.x.x
   
   # 验证npm版本
   npm --version
   # 应显示 9.x.x 或更高版本
   ```

3. **配置npm镜像源（可选，提升下载速度）：**
   ```bash
   # 设置淘宝镜像源
   npm config set registry https://registry.npmmirror.com
   
   # 验证配置
   npm config get registry
   ```

4. **安装pnpm（推荐）：**
   ```bash
   # pnpm性能更好，节省磁盘空间
   npm install -g pnpm
   
   # 验证安装
   pnpm --version
   ```

### **MySQL 8.0 本地安装与数据库创建**

MySQL 8.0是我们选择的关系型数据库，具有优秀的性能和JSON支持能力。

**安装步骤：**

1. **下载MySQL Community Server：**
   - 访问 [https://dev.mysql.com/downloads/mysql/](https://dev.mysql.com/downloads/mysql/)
   - 选择适合您操作系统的版本

2. **Windows安装：**
   ```bash
   # 下载MySQL Installer for Windows
   # 选择"Developer Default"安装类型
   # 设置root密码（请牢记此密码）
   # 默认端口：3306
   ```

3. **macOS安装：**
   ```bash
   # 使用Homebrew安装（推荐）
   brew install mysql
   
   # 启动MySQL服务
   brew services start mysql
   
   # 设置root密码
   mysql_secure_installation
   ```

4. **Linux安装（Ubuntu/Debian）：**
   ```bash
   # 更新包列表
   sudo apt update
   
   # 安装MySQL服务器
   sudo apt install mysql-server
   
   # 启动MySQL服务
   sudo systemctl start mysql
   
   # 设置root密码和安全配置
   sudo mysql_secure_installation
   ```

5. **创建项目数据库：**
   ```sql
   -- 连接到MySQL（使用root用户）
   mysql -u root -p
   
   -- 创建项目数据库
   CREATE DATABASE home_service_platform 
   CHARACTER SET utf8mb4 
   COLLATE utf8mb4_unicode_ci;
   
   -- 创建专用用户（可选，推荐）
   CREATE USER 'homeservice'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON home_service_platform.* TO 'homeservice'@'localhost';
   FLUSH PRIVILEGES;
   
   -- 验证数据库创建
   SHOW DATABASES;
   USE home_service_platform;
   ```

### **开发工具推荐**

**必备工具：**
- **IDE**: Visual Studio Code
  - 推荐插件：TypeScript, Prettier, ESLint, Thunder Client
- **数据库管理**: DBeaver（免费）或 Navicat（付费）
- **API测试**: Postman 或 Apifox
- **版本控制**: Git

**VS Code插件安装：**
```bash
# 安装推荐插件
code --install-extension ms-vscode.vscode-typescript-next
code --install-extension esbenp.prettier-vscode
code --install-extension dbaeumer.vscode-eslint
code --install-extension rangav.vscode-thunder-client
```

## **1.2 NestJS项目初始化**

### **NestJS CLI安装与项目创建**

NestJS是一个用于构建高效、可扩展的Node.js服务器端应用程序的框架。

1. **安装NestJS CLI：**
   ```bash
   # 全局安装NestJS CLI
   npm install -g @nestjs/cli
   
   # 验证安装
   nest --version
   ```

2. **创建项目：**
   ```bash
   # 创建新项目
   nest new home-service-platform-backend
   
   # 选择包管理器（推荐pnpm）
   # 进入项目目录
   cd home-service-platform-backend
   ```

3. **安装核心依赖：**
   ```bash
   # 数据库相关
   pnpm add @nestjs/typeorm typeorm mysql2
   
   # 配置管理
   pnpm add @nestjs/config
   
   # 认证相关
   pnpm add @nestjs/jwt @nestjs/passport passport passport-jwt passport-local
   pnpm add bcryptjs
   
   # 验证相关
   pnpm add class-validator class-transformer
   
   # API文档
   pnpm add @nestjs/swagger swagger-ui-express
   
   # 开发依赖
   pnpm add -D @types/passport-jwt @types/passport-local @types/bcryptjs
   ```

### **项目结构规划与模块化设计**

基于需求规格说明书，我们设计如下项目结构：

```
src/
├── common/                 # 公共模块
│   ├── decorators/         # 自定义装饰器
│   ├── filters/           # 异常过滤器
│   ├── guards/            # 守卫
│   ├── interceptors/      # 拦截器
│   ├── pipes/             # 管道
│   └── utils/             # 工具函数
├── config/                # 配置模块
│   ├── database.config.ts
│   ├── jwt.config.ts
│   └── app.config.ts
├── modules/               # 业务模块
│   ├── auth/              # 认证模块
│   ├── users/             # 用户管理
│   ├── providers/         # 服务商管理
│   ├── services/          # 服务管理
│   ├── orders/            # 订单管理
│   ├── payments/          # 支付模块
│   ├── reviews/           # 评价模块
│   ├── notifications/     # 通知模块
│   └── admin/             # 管理员模块
├── database/              # 数据库相关
│   ├── entities/          # 实体定义
│   ├── migrations/        # 数据库迁移
│   └── seeds/             # 种子数据
├── app.module.ts
└── main.ts
```

### **TypeORM + MySQL 连接配置**

1. **创建环境配置文件：**
   ```bash
   # 创建.env文件
   touch .env
   ```

2. **配置环境变量：**
   ```env
   # .env
   # 应用配置
   NODE_ENV=development
   PORT=3000
   
   # 数据库配置
   DB_HOST=localhost
   DB_PORT=3306
   DB_USERNAME=homeservice
   DB_PASSWORD=your_password
   DB_DATABASE=home_service_platform
   
   # JWT配置
   JWT_SECRET=your-super-secret-jwt-key-change-in-production
   JWT_EXPIRES_IN=7d
   
   # 文件上传配置
   UPLOAD_DEST=./uploads
   MAX_FILE_SIZE=5242880
   
   # 第三方服务配置（后续使用）
   WECHAT_APP_ID=your_wechat_app_id
   WECHAT_APP_SECRET=your_wechat_app_secret
   ALIPAY_APP_ID=your_alipay_app_id
   ```

3. **创建数据库配置：**
   ```typescript
   // src/config/database.config.ts
   import { TypeOrmModuleOptions } from '@nestjs/typeorm';
   import { ConfigService } from '@nestjs/config';
   
   export const getDatabaseConfig = (
     configService: ConfigService,
   ): TypeOrmModuleOptions => ({
     type: 'mysql',
     host: configService.get('DB_HOST'),
     port: configService.get('DB_PORT'),
     username: configService.get('DB_USERNAME'),
     password: configService.get('DB_PASSWORD'),
     database: configService.get('DB_DATABASE'),
     entities: [__dirname + '/../**/*.entity{.ts,.js}'],
     synchronize: configService.get('NODE_ENV') === 'development',
     logging: configService.get('NODE_ENV') === 'development',
     timezone: '+00:00',
     charset: 'utf8mb4',
   });
   ```

## **1.3 基础配置与中间件**

### **环境变量配置与验证**

1. **更新app.module.ts：**
   ```typescript
   // src/app.module.ts
   import { Module } from '@nestjs/common';
   import { ConfigModule, ConfigService } from '@nestjs/config';
   import { TypeOrmModule } from '@nestjs/typeorm';
   import { getDatabaseConfig } from './config/database.config';
   import { AppController } from './app.controller';
   import { AppService } from './app.service';
   
   @Module({
     imports: [
       // 配置模块 - 全局可用
       ConfigModule.forRoot({
         isGlobal: true,
         envFilePath: '.env',
         validationSchema: null, // 后续添加验证
       }),
       
       // 数据库模块
       TypeOrmModule.forRootAsync({
         imports: [ConfigModule],
         useFactory: getDatabaseConfig,
         inject: [ConfigService],
       }),
     ],
     controllers: [AppController],
     providers: [AppService],
   })
   export class AppModule {}
   ```

### **全局验证管道与异常过滤器**

1. **创建全局异常过滤器：**
   ```typescript
   // src/common/filters/http-exception.filter.ts
   import {
     ExceptionFilter,
     Catch,
     ArgumentsHost,
     HttpException,
     HttpStatus,
   } from '@nestjs/common';
   import { Request, Response } from 'express';
   
   @Catch(HttpException)
   export class HttpExceptionFilter implements ExceptionFilter {
     catch(exception: HttpException, host: ArgumentsHost) {
       const ctx = host.switchToHttp();
       const response = ctx.getResponse<Response>();
       const request = ctx.getRequest<Request>();
       const status = exception.getStatus();
   
       const errorResponse = {
         code: status,
         timestamp: new Date().toISOString(),
         path: request.url,
         method: request.method,
         message: exception.message || null,
       };
   
       response.status(status).json(errorResponse);
     }
   }
   ```

### **Swagger API文档配置**

1. **更新main.ts：**
   ```typescript
   // src/main.ts
   import { NestFactory } from '@nestjs/core';
   import { ValidationPipe } from '@nestjs/common';
   import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
   import { AppModule } from './app.module';
   import { HttpExceptionFilter } from './common/filters/http-exception.filter';
   
   async function bootstrap() {
     const app = await NestFactory.create(AppModule);
   
     // 全局前缀
     app.setGlobalPrefix('api');
   
     // 全局验证管道
     app.useGlobalPipes(
       new ValidationPipe({
         whitelist: true,
         forbidNonWhitelisted: true,
         transform: true,
         transformOptions: {
           enableImplicitConversion: true,
         },
       }),
     );
   
     // 全局异常过滤器
     app.useGlobalFilters(new HttpExceptionFilter());
   
     // CORS配置
     app.enableCors({
       origin: ['http://localhost:3000', 'http://localhost:5173'],
       credentials: true,
     });
   
     // Swagger配置
     const config = new DocumentBuilder()
       .setTitle('新一代在线家政维修服务平台 API')
       .setDescription('企业级O2O服务平台后端API文档')
       .setVersion('1.0')
       .addBearerAuth()
       .addTag('认证', '用户认证相关接口')
       .addTag('用户管理', '用户信息管理接口')
       .addTag('服务商管理', '服务商相关接口')
       .addTag('服务管理', '服务项目管理接口')
       .addTag('订单管理', '订单流程管理接口')
       .build();
   
     const document = SwaggerModule.createDocument(app, config);
     SwaggerModule.setup('api-docs', app, document);
   
     const port = process.env.PORT || 3000;
     await app.listen(port);
   
     console.log(`🚀 应用启动成功！`);
     console.log(`📖 API文档地址: http://localhost:${port}/api-docs`);
     console.log(`🔗 应用地址: http://localhost:${port}/api`);
   }
   
   bootstrap();
   ```

2. **启动项目验证：**
   ```bash
   # 启动开发服务器
   pnpm run start:dev
   
   # 访问以下地址验证：
   # http://localhost:3000/api - 应用首页
   # http://localhost:3000/api-docs - API文档
   ```

**第一篇总结：**

恭喜！您已经成功完成了：

✅ **开发环境搭建**：Node.js 18+、MySQL 8.0、开发工具配置
✅ **项目初始化**：NestJS项目创建、依赖安装、结构规划
✅ **基础配置**：数据库连接、环境变量、全局中间件
✅ **API文档**：Swagger集成，便于后续接口开发和测试

您现在拥有了一个企业级的开发基础，为构建"新一代在线家政维修服务平台"奠定了坚实基础。

---

**[请告诉我"继续"，我们将进入第二篇：数据库设计与核心实体建模，开始构建完整的数据模型。]**
