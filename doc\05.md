好的，我们继续第五篇，聚焦于家政维修平台最核心的业务功能——工单管理。

---

### **《从零构建家政维修平台：打造稳健高效的混合架构后台应用》**

#### **第五篇：核心业务模块构建——工单管理（创建、派发与状态流转）**

**摘要：** 工单是家政维修平台的核心，它贯穿了从客户报修到服务完成的整个生命周期。本篇教程将指导你如何在NestJS后端构建工单管理模块，实现工单的创建、查询、派发给维修工人以及状态流转等关键功能。

---

**一、设计工单数据模型（Order Entity）**

首先，我们需要定义工单在数据库中的结构，包含其所有必要的属性。

1.  **创建工单实体（Order Entity）：**
    * 在 `src` 目录下新建一个 `order` 文件夹（如果之前用户管理模块没有创建，注意避免混淆），并在其中创建 `order.entity.ts` 文件。
    * 工单的字段应能完整描述一次维修服务的全貌。
    * **主要字段：**
        * `id`: 唯一标识符。
        * `customerName`: 客户姓名。
        * `customerPhone`: 客户联系电话。
        * `address`: 报修地址。
        * `serviceType`: 服务类型（例如：空调维修、管道疏通、家电清洗等）。
        * `description`: 报修问题描述。
        * `status`: 工单状态（这是核心，例如 `pending` 待处理, `dispatched` 已派发, `in_progress` 进行中, `completed` 已完成, `canceled` 已取消）。
        * `reporterImages`: 客户上传的报修图片URL数组（从COS获取）。
        * `workerImages`: 维修工人上传的维修前后图片URL数组（从COS获取）。
        * `workerId`: 关联的维修工人的ID（关联到 `User` 实体）。
        * `dispatcherId`: 关联的调度员ID（关联到 `User` 实体，可选）。
        * `estimatedCost`: 预估费用。
        * `finalCost`: 最终费用。
        * `dispatchTime`: 派发时间。
        * `completeTime`: 完成时间。
        * `createdAt`, `updatedAt`: 创建和更新时间戳。
    * **TypeORM映射：** 使用 `@Entity()`、`@PrimaryGeneratedColumn()`、`@Column()` 等装饰器。对于关联字段，如 `workerId`，我们可以使用 `@ManyToOne()` 装饰器与 `User` 实体建立关系。

    ```typescript
    // src/order/order.entity.ts

    import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
    import { User } from '../user/user.entity'; // 导入用户实体

    export enum OrderStatus {
      Pending = 'pending',          // 待处理
      Dispatched = 'dispatched',    // 已派发
      InProgress = 'in_progress',   // 维修中
      Completed = 'completed',      // 已完成
      Canceled = 'canceled',        // 已取消
      // 更多状态可根据业务需求添加，如 'quoted' 已报价, 'paid' 已支付等
    }

    @Entity('orders') // 数据库表名为 'orders'
    export class Order {
      @PrimaryGeneratedColumn()
      id: number;

      @Column()
      customerName: string;

      @Column()
      customerPhone: string;

      @Column()
      address: string;

      @Column()
      serviceType: string;

      @Column('text') // 文本类型，用于存储较长的描述
      description: string;

      @Column({
        type: 'enum',
        enum: OrderStatus,
        default: OrderStatus.Pending,
      })
      status: OrderStatus;

      @Column('json', { nullable: true }) // 存储图片URL数组
      reporterImages: string[]; // 客户报修图片URL

      @Column('json', { nullable: true }) // 存储图片URL数组
      workerImages: string[]; // 维修工人图片URL

      @ManyToOne(() => User, { nullable: true }) // 多个订单可以关联一个维修工人
      @JoinColumn({ name: 'worker_id' }) // 外键列名
      worker: User; // 关联的维修工人对象

      @Column({ nullable: true, name: 'worker_id' }) // 手动定义外键列
      workerId: number;

      @ManyToOne(() => User, { nullable: true }) // 多个订单可以关联一个调度员
      @JoinColumn({ name: 'dispatcher_id' })
      dispatcher: User;

      @Column({ nullable: true, name: 'dispatcher_id' })
      dispatcherId: number;

      @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
      estimatedCost: number;

      @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
      finalCost: number;

      @Column({ type: 'datetime', nullable: true })
      dispatchTime: Date;

      @Column({ type: 'datetime', nullable: true })
      completeTime: Date;

      @CreateDateColumn({ name: 'created_at' })
      createdAt: Date;

      @UpdateDateColumn({ name: 'updated_at' })
      updatedAt: Date;
    }
    ```
2.  **更新 `app.module.ts`：** 将 `Order` 实体添加到 `TypeOrmModule.forRoot()` 的 `entities` 数组中。

    ```typescript
    // src/app.module.ts (部分代码)
    import { User } from './user/user.entity';
    import { Order } from './order/order.entity'; // 导入工单实体

    @Module({
      imports: [
        TypeOrmModule.forRoot({
          // ... 其他配置 ...
          entities: [User, Order], // 添加 User 和 Order 实体
          synchronize: true, // 开发环境开启，会自动创建orders表
        }),
        // ...
      ],
      // ...
    })
    export class AppModule {}
    ```

**二、构建工单模块（Order Module）**

按照NestJS的模块化原则，为工单功能创建独立的模块、服务和控制器。

1.  **创建工单模块：** 在命令行中运行：
    ```bash
    nest g module order
    ```
    这会在 `src/order/` 目录下创建 `order.module.ts`。

2.  **创建工单服务（Order Service）：**
    * 它将处理所有与工单相关的业务逻辑，如创建、查询、更新状态、派发等。
    * 在命令行中运行：
        ```bash
        nest g service order
        ```
    * 在 `src/order/order.service.ts` 中，使用 `@InjectRepository()` 注入 `OrderRepository`。为了方便后续派单，我们还需要注入 `UserRepository`。

    ```typescript
    // src/order/order.service.ts (部分代码)

    import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
    import { InjectRepository } from '@nestjs/typeorm';
    import { Repository } from 'typeorm';
    import { Order, OrderStatus } from './order.entity';
    import { User, UserRole } from '../user/user.entity'; // 导入 User 实体和角色枚举

    @Injectable()
    export class OrderService {
      constructor(
        @InjectRepository(Order)
        private ordersRepository: Repository<Order>,
        @InjectRepository(User) // 注入用户仓库，用于派单时查询工人
        private usersRepository: Repository<User>,
      ) {}

      // 创建工单（由客户小程序或客服后台创建）
      async createOrder(orderData: Partial<Order>): Promise<Order> {
        const newOrder = this.ordersRepository.create(orderData);
        // 新创建的工单默认状态为 Pending
        newOrder.status = OrderStatus.Pending;
        return this.ordersRepository.save(newOrder);
      }

      // 查询单个工单详情
      async findOrderById(id: number): Promise<Order> {
        const order = await this.ordersRepository.findOne({ where: { id }, relations: ['worker', 'dispatcher'] }); // 加载关联的worker和dispatcher信息
        if (!order) {
          throw new NotFoundException(`Order with ID ${id} not found.`);
        }
        return order;
      }

      // 查询所有工单或按状态、工人等条件筛选
      async findAllOrders(query: { status?: OrderStatus; workerId?: number }): Promise<Order[]> {
        const where: any = {};
        if (query.status) {
          where.status = query.status;
        }
        if (query.workerId) {
          where.workerId = query.workerId;
        }
        return this.ordersRepository.find({ where, relations: ['worker', 'dispatcher'], order: { createdAt: 'DESC' } }); // 默认按创建时间倒序
      }

      // 派发工单（给维修工人）
      async dispatchOrder(orderId: number, workerId: number, dispatcherId?: number): Promise<Order> {
        const order = await this.findOrderById(orderId);
        if (order.status !== OrderStatus.Pending) {
          throw new BadRequestException('Only pending orders can be dispatched.');
        }

        const worker = await this.usersRepository.findOne({ where: { id: workerId, role: UserRole.Worker } });
        if (!worker) {
          throw new NotFoundException(`Worker with ID ${workerId} not found or is not a worker role.`);
        }

        order.worker = worker;
        order.workerId = worker.id;
        order.status = OrderStatus.Dispatched;
        order.dispatchTime = new Date();
        if (dispatcherId) {
          const dispatcher = await this.usersRepository.findOne({ where: { id: dispatcherId, role: UserRole.Dispatcher } });
          if (dispatcher) {
              order.dispatcher = dispatcher;
              order.dispatcherId = dispatcher.id;
          }
        }
        return this.ordersRepository.save(order);
      }

      // 更新工单状态（如进行中、已完成、已取消）
      async updateOrderStatus(orderId: number, newStatus: OrderStatus): Promise<Order> {
        const order = await this.findOrderById(orderId);
        // 在这里可以添加状态流转的业务逻辑校验，例如：
        // if (order.status === OrderStatus.Completed && newStatus !== OrderStatus.Completed) {
        //   throw new BadRequestException('Completed orders cannot change status.');
        // }
        order.status = newStatus;
        // 如果状态变为Completed，记录完成时间
        if (newStatus === OrderStatus.Completed) {
          order.completeTime = new Date();
        }
        return this.ordersRepository.save(order);
      }

      // TODO: 添加更多方法，如更新工单信息、添加维修图片等
    }
    ```
3.  **创建工单控制器（Order Controller）：**
    * 提供API接口供前端（小程序和管理后台）调用，实现工单的CRUD操作。
    * 在命令行中运行：
        ```bash
        nest g controller order
        ```
    * 在 `src/order/order.controller.ts` 中，添加API路由。

    ```typescript
    // src/order/order.controller.ts (部分代码)

    import { Controller, Post, Get, Put, Body, Param, Query, UseGuards, Req } from '@nestjs/common';
    import { OrderService } from './order.service';
    import { Order, OrderStatus } from './order.entity';
    import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard'; // 导入JWT认证守卫
    import { RolesGuard } from '../auth/guards/roles.guard'; // 导入角色守卫
    import { Roles } from '../auth/decorators/roles.decorator'; // 导入角色装饰器
    import { UserRole } from '../user/user.entity';

    @Controller('orders') // 接口前缀是 /orders
    @UseGuards(JwtAuthGuard, RolesGuard) // 默认所有接口都需要认证和角色检查
    export class OrderController {
      constructor(private readonly orderService: OrderService) {}

      // 创建工单（客户通过小程序调用，或客服后台创建）
      @Post()
      @Roles(UserRole.Customer, UserRole.CustomerService) // 只有客户或客服可以创建
      async create(@Body() createOrderDto: Partial<Order>): Promise<Order> {
        return this.orderService.createOrder(createOrderDto);
      }

      // 获取单个工单详情
      @Get(':id')
      @Roles(UserRole.Admin, UserRole.CustomerService, UserRole.Dispatcher, UserRole.Worker, UserRole.Customer) // 所有相关角色可查看
      async findOne(@Param('id') id: number): Promise<Order> {
        return this.orderService.findOrderById(id);
      }

      // 获取工单列表（可根据状态、工人ID筛选）
      @Get()
      @Roles(UserRole.Admin, UserRole.CustomerService, UserRole.Dispatcher) // 管理员、客服、调度员可查看所有工单
      async findAll(@Query('status') status?: OrderStatus, @Query('workerId') workerId?: number): Promise<Order[]> {
        return this.orderService.findAllOrders({ status, workerId });
      }

      // 维修工人获取自己的工单列表
      @Get('my-orders')
      @Roles(UserRole.Worker)
      async findMyOrders(@Req() req): Promise<Order[]> {
        // 假设 req.user.id 是当前认证用户的ID
        return this.orderService.findAllOrders({ workerId: req.user.sub });
      }

      // 派发工单
      @Put(':id/dispatch')
      @Roles(UserRole.CustomerService, UserRole.Dispatcher) // 只有客服或调度员可以派发
      async dispatch(
        @Param('id') orderId: number,
        @Body('workerId') workerId: number,
        @Req() req // 获取当前调度员ID
      ): Promise<Order> {
        return this.orderService.dispatchOrder(orderId, workerId, req.user.sub);
      }

      // 更新工单状态（如进行中、已完成、已取消）
      @Put(':id/status')
      @Roles(UserRole.CustomerService, UserRole.Dispatcher, UserRole.Worker) // 相关人员可以更新状态
      async updateStatus(
        @Param('id') orderId: number,
        @Body('newStatus') newStatus: OrderStatus,
      ): Promise<Order> {
        return this.orderService.updateOrderStatus(orderId, newStatus);
      }
    }
    ```
4.  **在 `order.module.ts` 中注册：** 确保模块、控制器和服务都正确注册到工单模块中。同时，由于 `OrderService` 需要使用 `UserRepository`，所以需要导入 `UserModule` 或直接在 `OrderModule` 中导入 `TypeOrmModule.forFeature([Order, User])`。

    ```typescript
    // src/order/order.module.ts

    import { Module } from '@nestjs/common';
    import { TypeOrmModule } from '@nestjs/typeorm';
    import { Order } from './order.entity';
    import { OrderService } from './order.service';
    import { OrderController } from './order.controller';
    import { User } from '../user/user.entity'; // 导入User实体

    @Module({
      imports: [
        TypeOrmModule.forFeature([Order, User]), // 注册Order和User实体到当前模块
        // 如果想避免UserModule的循环依赖，可以直接在这里注入User的Repository
        // 或者导入UserModule并确保UserService被导出
      ],
      providers: [OrderService],
      controllers: [OrderController],
      exports: [OrderService], // 如果其他模块需要使用OrderService，需要导出
    })
    export class OrderModule {}
    ```
5.  **在 `app.module.ts` 中导入工单模块：**

    ```typescript
    // src/app.module.ts (部分代码)

    import { OrderModule } from './order/order.module'; // 导入工单模块

    @Module({
      imports: [
        // ...
        OrderModule, // 导入工单模块
      ],
      // ...
    })
    export class AppModule {}
    ```
    * **重要提示：** 确保 `UserModule` 也在 `app.module.ts` 中导入，并且 `UserService` 在 `UserModule` 中被 `exports`，这样 `OrderService` 才能注入 `UserService`。或者，更直接的方式是在 `OrderModule` 中直接 `TypeOrmModule.forFeature([Order, User])`，让 `OrderService` 直接注入 `Repository<User>`。上面的代码已采用直接注入 `Repository<User>` 的方式。

**三、测试工单管理功能**

1.  **启动后端应用：** 在项目根目录运行 `npm run start:dev`。
2.  **使用API测试工具（如Postman、Apifox）：**
    * **先注册用户并登录：** 确保你有管理员、客服和维修工人等不同角色的测试账号，并获取其JWT `access_token`。
    * **创建工单：**
        * URL: `http://localhost:3000/orders`
        * Method: `POST`
        * Headers: `Authorization: Bearer YOUR_CUSTOMER_OR_CUSTOMER_SERVICE_TOKEN`
        * Body (JSON): 包含 `customerName`, `customerPhone`, `address`, `serviceType`, `description`, `reporterImages` (图片URL数组，可先放空或测试数据) 等字段。
    * **查看所有工单：**
        * URL: `http://localhost:3000/orders`
        * Method: `GET`
        * Headers: `Authorization: Bearer YOUR_ADMIN_OR_CUSTOMER_SERVICE_OR_DISPATCHER_TOKEN`
    * **派发工单：**
        * URL: `http://localhost:3000/orders/:orderId/dispatch` (将 `:orderId` 替换为实际工单ID)
        * Method: `PUT`
        * Headers: `Authorization: Bearer YOUR_CUSTOMER_SERVICE_OR_DISPATCHER_TOKEN`
        * Body (JSON): `{ "workerId": YOUR_WORKER_ID }`
    * **更新工单状态：**
        * URL: `http://localhost:3000/orders/:orderId/status`
        * Method: `PUT`
        * Headers: `Authorization: Bearer YOUR_CUSTOMER_SERVICE_OR_DISPATCHER_OR_WORKER_TOKEN`
        * Body (JSON): `{ "newStatus": "in_progress" }` (或其他状态)

通过本篇教程，你已经为家政维修平台的核心功能——工单管理——打下了坚实的基础。你可以创建、查询、派发工单，并更新其状态。下一步我们将把重点放在与企业微信的集成上，实现工单状态变更的实时通知。

---

**[请告诉我“继续”，我将提供第六篇：企业微信集成——通讯录同步。]**