### **《新一代在线家政维修服务平台：从零构建企业级O2O服务生态》**

#### **第二篇：数据库设计与核心实体建模——构建稳固的数据基础**

**摘要：** 数据库设计是整个平台的基石，直接影响系统的性能、扩展性和维护性。本篇将深入讲解如何设计一个支持复杂业务场景的数据库架构，包括用户体系、服务管理、订单流程、支付结算等核心实体的建模，为后续功能开发奠定坚实基础。

---

**一、数据库设计原则与策略**

在设计家政维修服务平台的数据库时，我们需要考虑以下关键因素：

**设计原则：**
* **业务驱动**：以实际业务需求为导向，避免过度设计
* **扩展性优先**：为未来功能扩展预留空间
* **性能考虑**：合理设计索引和查询优化
* **数据一致性**：确保关键业务数据的完整性
* **安全性**：敏感数据加密存储和访问控制

**技术选型：**
```sql
-- 数据库：MySQL 8.0
-- 字符集：utf8mb4 (支持emoji和特殊字符)
-- 存储引擎：InnoDB (支持事务和外键)
-- 时区：统一使用UTC时间
```

**二、核心实体关系设计**

### 1. 用户体系设计

我们的平台需要支持三种核心角色：终端用户、服务商、管理员。采用统一用户表设计，通过角色字段区分：

```sql
-- 用户基础表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    phone VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    role ENUM('customer', 'provider', 'admin') NOT NULL COMMENT '用户角色',
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active' COMMENT '账户状态',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    real_name VARCHAR(50) COMMENT '真实姓名',
    id_card VARCHAR(18) COMMENT '身份证号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_phone (phone),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
) COMMENT '用户基础信息表';

-- 用户详细信息表
CREATE TABLE user_profiles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    nickname VARCHAR(50) COMMENT '昵称',
    gender ENUM('male', 'female', 'unknown') DEFAULT 'unknown',
    birth_date DATE COMMENT '出生日期',
    address TEXT COMMENT '详细地址',
    city VARCHAR(50) COMMENT '所在城市',
    district VARCHAR(50) COMMENT '所在区域',
    longitude DECIMAL(10,7) COMMENT '经度',
    latitude DECIMAL(10,7) COMMENT '纬度',
    preferences JSON COMMENT '用户偏好设置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_location (longitude, latitude)
) COMMENT '用户详细信息表';
```

### 2. 服务商管理体系

服务商是平台的核心资源，需要详细的资质管理和能力描述：

```sql
-- 服务商信息表
CREATE TABLE service_providers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    business_name VARCHAR(100) COMMENT '商户名称',
    business_license VARCHAR(100) COMMENT '营业执照号',
    certification_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    certification_note TEXT COMMENT '认证备注',
    service_radius INT DEFAULT 10 COMMENT '服务半径(公里)',
    base_longitude DECIMAL(10,7) COMMENT '服务基地经度',
    base_latitude DECIMAL(10,7) COMMENT '服务基地纬度',
    working_hours JSON COMMENT '工作时间设置',
    introduction TEXT COMMENT '服务商介绍',
    experience_years INT DEFAULT 0 COMMENT '从业年限',
    total_orders INT DEFAULT 0 COMMENT '总订单数',
    rating_average DECIMAL(3,2) DEFAULT 0.00 COMMENT '平均评分',
    rating_count INT DEFAULT 0 COMMENT '评价数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_certification_status (certification_status),
    INDEX idx_location (base_longitude, base_latitude),
    INDEX idx_rating (rating_average)
) COMMENT '服务商信息表';

-- 服务商技能认证表
CREATE TABLE provider_certifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    provider_id BIGINT NOT NULL,
    certification_type VARCHAR(50) NOT NULL COMMENT '认证类型',
    certification_name VARCHAR(100) NOT NULL COMMENT '认证名称',
    certificate_number VARCHAR(100) COMMENT '证书编号',
    certificate_url VARCHAR(500) COMMENT '证书图片URL',
    issue_date DATE COMMENT '颁发日期',
    expire_date DATE COMMENT '过期日期',
    status ENUM('valid', 'expired', 'revoked') DEFAULT 'valid',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (provider_id) REFERENCES service_providers(id) ON DELETE CASCADE,
    INDEX idx_provider_id (provider_id),
    INDEX idx_type (certification_type),
    INDEX idx_status (status)
) COMMENT '服务商技能认证表';
```

### 3. 服务分类与项目管理

建立灵活的服务分类体系，支持多级分类和动态扩展：

```sql
-- 服务分类表
CREATE TABLE service_categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    parent_id BIGINT COMMENT '父分类ID',
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '分类编码',
    description TEXT COMMENT '分类描述',
    icon_url VARCHAR(500) COMMENT '分类图标',
    sort_order INT DEFAULT 0 COMMENT '排序权重',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES service_categories(id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_code (code),
    INDEX idx_active (is_active)
) COMMENT '服务分类表';

-- 服务项目表
CREATE TABLE service_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    category_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL COMMENT '服务名称',
    description TEXT COMMENT '服务描述',
    pricing_type ENUM('fixed', 'hourly', 'custom') NOT NULL COMMENT '计价方式',
    base_price DECIMAL(10,2) COMMENT '基础价格',
    unit VARCHAR(20) COMMENT '计价单位',
    duration_minutes INT COMMENT '预估时长(分钟)',
    requirements TEXT COMMENT '服务要求',
    images JSON COMMENT '服务图片',
    tags JSON COMMENT '服务标签',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (category_id) REFERENCES service_categories(id),
    INDEX idx_category_id (category_id),
    INDEX idx_pricing_type (pricing_type),
    INDEX idx_active (is_active)
) COMMENT '服务项目表';

-- 服务商服务关联表
CREATE TABLE provider_services (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    provider_id BIGINT NOT NULL,
    service_item_id BIGINT NOT NULL,
    custom_price DECIMAL(10,2) COMMENT '自定义价格',
    is_available BOOLEAN DEFAULT TRUE COMMENT '是否可用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (provider_id) REFERENCES service_providers(id) ON DELETE CASCADE,
    FOREIGN KEY (service_item_id) REFERENCES service_items(id) ON DELETE CASCADE,
    UNIQUE KEY uk_provider_service (provider_id, service_item_id),
    INDEX idx_provider_id (provider_id),
    INDEX idx_service_item_id (service_item_id)
) COMMENT '服务商服务关联表';
```

### 4. 订单管理体系

订单是平台的核心业务实体，需要支持复杂的状态流转和扩展：

```sql
-- 订单主表
CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(32) UNIQUE NOT NULL COMMENT '订单号',
    customer_id BIGINT NOT NULL COMMENT '客户ID',
    provider_id BIGINT COMMENT '服务商ID',
    service_item_id BIGINT NOT NULL COMMENT '服务项目ID',
    status ENUM('pending', 'confirmed', 'assigned', 'in_progress', 'completed', 'cancelled', 'refunded') DEFAULT 'pending',
    
    -- 服务信息
    service_name VARCHAR(100) NOT NULL COMMENT '服务名称',
    service_description TEXT COMMENT '服务描述',
    appointment_time TIMESTAMP COMMENT '预约时间',
    estimated_duration INT COMMENT '预估时长(分钟)',
    
    -- 地址信息
    service_address TEXT NOT NULL COMMENT '服务地址',
    contact_name VARCHAR(50) NOT NULL COMMENT '联系人姓名',
    contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    longitude DECIMAL(10,7) COMMENT '经度',
    latitude DECIMAL(10,7) COMMENT '纬度',
    
    -- 价格信息
    base_price DECIMAL(10,2) NOT NULL COMMENT '基础价格',
    additional_fee DECIMAL(10,2) DEFAULT 0 COMMENT '附加费用',
    discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT '优惠金额',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '总金额',
    platform_fee DECIMAL(10,2) DEFAULT 0 COMMENT '平台费用',
    
    -- 时间记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    confirmed_at TIMESTAMP NULL COMMENT '确认时间',
    started_at TIMESTAMP NULL COMMENT '开始服务时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    cancelled_at TIMESTAMP NULL COMMENT '取消时间',
    
    -- 备注信息
    customer_note TEXT COMMENT '客户备注',
    provider_note TEXT COMMENT '服务商备注',
    cancel_reason TEXT COMMENT '取消原因',
    
    FOREIGN KEY (customer_id) REFERENCES users(id),
    FOREIGN KEY (provider_id) REFERENCES service_providers(id),
    FOREIGN KEY (service_item_id) REFERENCES service_items(id),
    
    INDEX idx_order_no (order_no),
    INDEX idx_customer_id (customer_id),
    INDEX idx_provider_id (provider_id),
    INDEX idx_status (status),
    INDEX idx_appointment_time (appointment_time),
    INDEX idx_created_at (created_at)
) COMMENT '订单主表';
```

**三、数据库初始化脚本**

为了便于开发和测试，我们需要准备初始化数据：

```sql
-- 插入默认服务分类
INSERT INTO service_categories (name, code, description, sort_order) VALUES
('家政保洁', 'housekeeping', '专业家政保洁服务', 1),
('家电维修', 'appliance_repair', '各类家电维修服务', 2),
('水电维修', 'plumbing_electrical', '水电安装维修服务', 3),
('搬家拉货', 'moving_transport', '搬家运输服务', 4);

-- 插入默认服务项目
INSERT INTO service_items (category_id, name, description, pricing_type, base_price, unit, duration_minutes) VALUES
(1, '日常保洁', '家庭日常清洁服务', 'hourly', 50.00, '小时', 120),
(1, '深度保洁', '全屋深度清洁服务', 'fixed', 200.00, '次', 240),
(2, '空调清洗', '空调深度清洗服务', 'fixed', 120.00, '台', 90),
(2, '洗衣机维修', '洗衣机故障维修', 'custom', 0.00, '次', 60);

-- 创建默认管理员账户
INSERT INTO users (username, phone, email, password_hash, role, real_name) VALUES
('admin', '13800000000', '<EMAIL>', '$2b$10$encrypted_password_hash', 'admin', '系统管理员');
```

**四、性能优化与索引策略**

针对高频查询场景，我们需要建立合适的索引：

```sql
-- 订单查询优化索引
CREATE INDEX idx_orders_customer_status ON orders(customer_id, status);
CREATE INDEX idx_orders_provider_status ON orders(provider_id, status);
CREATE INDEX idx_orders_location ON orders(longitude, latitude);

-- 服务商搜索优化索引
CREATE INDEX idx_providers_location_rating ON service_providers(base_longitude, base_latitude, rating_average);

-- 全文搜索索引
ALTER TABLE service_items ADD FULLTEXT(name, description);
ALTER TABLE service_providers ADD FULLTEXT(business_name, introduction);
```

通过本篇教程，我们建立了一个完整、灵活、可扩展的数据库架构。这个设计不仅满足当前的业务需求，还为未来的功能扩展预留了充足空间。

---

**[请告诉我"继续"，我将提供第三篇：开发环境搭建与NestJS项目初始化。]**
